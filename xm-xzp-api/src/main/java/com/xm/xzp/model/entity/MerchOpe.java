package com.xm.xzp.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("tb_merch_ope")
@ApiModel(description = "商户操作码表")
public class MerchOpe {

    @ApiModelProperty("商户号")
    @TableField("merch_id")
    private String merchId;

    @ApiModelProperty("操作码")
    @TableField("ope_cd")
    private String opeCd;

    @ApiModelProperty("产品名称")
    @TableField("prdt_nm")
    private String prdtNm;

    @ApiModelProperty("商户操作开关标志")
    @TableField("merch_ope_on_off_fg")
    private String merchOpeOnOffFg;

    @ApiModelProperty("开通日期")
    @TableField("open_dt")
    private String openDt;

    @ApiModelProperty("商户操作类型")
    @TableField("merch_ope_type")
    private String merchOpeType;

    @ApiModelProperty("汇总码")
    @TableField("summ_cd")
    private String summCd;

    @ApiModelProperty("CPCB汇总码")
    @TableField("cpcb_summ_cd")
    private String cpcbSummCd;

    @ApiModelProperty("最后修改柜员号")
    @TableField("last_modify_tlr_id")
    private String lastModifyTlrId;

    @ApiModelProperty("最后修改机构号")
    @TableField("last_modify_inst_id")
    private String lastModifyInstId;

    @ApiModelProperty("最后修改时间")
    @TableField("last_modify_dt")
    private LocalDateTime lastModifyDt;

    @ApiModelProperty("内部账户账号")
    @TableField("inner_acc_acc_no")
    private String innerAccAccNo;

    @ApiModelProperty("内部账户机构号")
    @TableField("inner_acc_inst_no")
    private String innerAccInstNo;
}
