package com.xm.xzp.model.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import com.psbc.pfpj.yoaf.validator.custom.annotation.ListRange;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 导出字典数据对象
 * </p>
 *
 * @version 1.0.0
 * <AUTHOR>
 * @date 2021-10-26 13:57
 */
@ExcelTarget("dict")
@Data
public class ExcelDictVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 字典类型代码
     */
    @Excel(name = "字典类型代码", orderNum = "0")
    @NotBlank(message = "字典类型代码不能为空")
    @Length(max = 32, message = "字典类型代码不能超过32个字符")
    private String dictTypeId;

    /**
     * 字典类型名称
     */
    @Excel(name = "字典类型名称", orderNum = "1")
    @NotBlank(message = "字典类型名称不能为空")
    @Length(max = 255, message = "字典类型名称不能超过255个字符")
    private String dictTypeName;

    /**
     * 数据来源
     */
    @Excel(name = "数据来源", orderNum = "2")
    @NotBlank(message = "数据来源不能为空")
    @Length(max = 32, message = "数据来源不能超过32个字符")
    private String dataSource;

    /**
     * 状态 1-启用 2-停用
     */
    @Excel(name = "字典类型状态", orderNum = "3")
    @NotBlank(message = "字典类型状态")
    @ListRange(value = { "1", "2" }, message = "状态可选项只能有【1, 2】")
    private String dictTypeStatus;

    /**
     * 描述
     */
    @Excel(name = "字典类型描述", orderNum = "4")
    @Length(max = 255, message = "字典类型描述不能超过255个字符")
    private String dictTypeDesc;

    /**
     * 字典项代码
     */
    @Excel(name = "字典项代码", orderNum = "5")
    @NotBlank(message = "字典项代码不能为空")
    @Length(max = 32, message = "字典项代码不能超过32个字符")
    private String dictId;

    /**
     * 字典项名称
     */
    @Excel(name = "字典项名称", orderNum = "6")
    @NotBlank(message = "字典项名称不能为空")
    @Length(max = 32, message = "字典项名称不能超过32个字符")
    private String dictName;

    /**
     * 状态 1-启用 2-停用
     */
    @Excel(name = "字典项状态", orderNum = "7")
    @NotBlank(message = "字典项状态不能为空")
    @ListRange(value = { "1", "2" }, message = "状态可选项只能有【1, 2】")
    private String dictStatus;

    /**
     * 显示顺序
     */
    @Excel(name = "显示顺序", orderNum = "8")
    private Integer displayOrder;

    /**
     * 创建人
     */
    @Excel(name = "创建人", orderNum = "9")
    private String createUser;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", orderNum = "10")
    private LocalDateTime createTime;

    /**
     * 最后维护人
     */
    @Excel(name = "最后维护人", orderNum = "11")
    private String updateUser;

    /**
     * 最后维护时间
     */
    @Excel(name = "最后维护时间", orderNum = "12")
    private LocalDateTime updateTime;

}
