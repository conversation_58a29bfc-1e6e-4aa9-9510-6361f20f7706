package com.xm.xzp.model.entity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

@Data
@TableName("tb_batch_tran_dtl")
@ApiModel(value = "批量交易明细")
public class BatchTranDtl {

    @ApiModelProperty(value = "批次号")
    @TableId
    private String batchId;

    @ApiModelProperty(value = "核心批次号")
    @TableField("core_batch_id")
    private String coreBatchId;

    @ApiModelProperty(value = "批量交易序号")
    private String batchTranSq;

    @ApiModelProperty(value = "主机批次号")
    private String hostBatchId;

    @ApiModelProperty(value = "主机交易序号")
    private String hostTranSq;

    @ApiModelProperty(value = "商户号")
    private String merchId;

    @ApiModelProperty(value = "操作码")
    private String opeCd;

    @ApiModelProperty(value = "交易机构")
    private String tranInstId;

    @ApiModelProperty(value = "交易日期")
    private String tranDt;

    @ApiModelProperty(value = "文件记账日期")
    private String fileInaccDt;

    @ApiModelProperty(value = "商户交易序号")
    private String merchTranSq;

    @ApiModelProperty(value = "单位代码")
    private String corpCd;

    @ApiModelProperty(value = "缴费号")
    private String payId;

    @ApiModelProperty(value = "缴费类型")
    private String rpType;

    @ApiModelProperty(value = "卡折标志")
    private String cardPkFg;

    @ApiModelProperty(value = "账号")
    private String accId;

    @ApiModelProperty(value = "签约人名称")
    private String signName;

    @ApiModelProperty(value = "账户行标志")
    private String accBankFlag;

    @ApiModelProperty(value = "账户卡折类型")
    private String accPkType;

    @ApiModelProperty(value = "发送系统标识")
    private String sendSysId;

    @ApiModelProperty(value = "账卡号")
    private String accCardId;

    @ApiModelProperty(value = "账户名称")
    private String accName;

    @ApiModelProperty(value = "签约机构")
    private String signInstId;

    @ApiModelProperty(value = "代理行号")
    private String commiBankNo;

    @ApiModelProperty(value = "清算机构")
    private String settleInstId;

    @ApiModelProperty(value = "代理机构")
    private String commiInstId;

    @ApiModelProperty(value = "特殊标志")
    private String specFg;

    @ApiModelProperty(value = "发行标志")
    private String issueFg;

    @ApiModelProperty(value = "欠费数量")
    private String oweFeeQt;

    @ApiModelProperty(value = "费用所属期")
    private String feePeriodTx;

    @ApiModelProperty(value = "查验单据标志")
    private String chkPaperFg;

    @ApiModelProperty(value = "单据类型代码")
    private String paperTypeCd;

    @ApiModelProperty(value = "单据号码")
    private String paperId;

    @ApiModelProperty(value = "存款类型代码")
    private String saveTypeCd;

    @ApiModelProperty(value = "期限")
    private String term;

    @ApiModelProperty(value = "摘要")
    private String summary;

    @ApiModelProperty(value = "跳转金额")
    private String jumpAt;

    @ApiModelProperty(value = "小数精度")
    private String decPrecision;

    @ApiModelProperty(value = "最小保留金额")
    private String minKeepAt;

    @ApiModelProperty(value = "小数处理代码")
    private String decCd;

    @ApiModelProperty(value = "不足标志")
    private String lackFg;

    @ApiModelProperty(value = "保留标识")
    private String keepId;

    @ApiModelProperty(value = "开户机构")
    private String openInstId;

    @ApiModelProperty(value = "客户号")
    private String custId;

    @ApiModelProperty(value = "存款余额")
    private String depBalAt;

    @ApiModelProperty(value = "账户余额")
    private String accBalAt;

    @ApiModelProperty(value = "交易金额")
    private String tranAt;

    @ApiModelProperty(value = "实际金额")
    private String actAt;

    @ApiModelProperty(value = "其他金额")
    private String othAt;

    @ApiModelProperty(value = "起息日期")
    private String beginIntDt;

    @ApiModelProperty(value = "账户状态代码")
    private String accStatCd;

    @ApiModelProperty(value = "原交易金额")
    private String orgTranAt;

    @ApiModelProperty(value = "原发送系统标识")
    private String orgSendSysId;

    @ApiModelProperty(value = "原文件记账日期")
    private String orgFileInaccDt;

    @ApiModelProperty(value = "原批次号")
    private String orgBatchId;

    @ApiModelProperty(value = "原交易序号")
    private String orgTranSq;

    @ApiModelProperty(value = "原主机批次号")
    private String orgHostBatchId;

    @ApiModelProperty(value = "原主机交易序号")
    private String orgHostTranSq;

    @ApiModelProperty(value = "原保留标识")
    private String orgKeepId;

    @ApiModelProperty(value = "手续费标志")
    private String opeFeeFg;

    @ApiModelProperty(value = "地区手续费收取比率")
    private String areaFeeRecvRt;

    @ApiModelProperty(value = "主机地区手续费")
    private String hostAreaFeeAt;

    @ApiModelProperty(value = "手续费金额")
    private String opeFeeAt;

    @ApiModelProperty(value = "预留手续费")
    private String prsvFeeAt;

    @ApiModelProperty(value = "金额1")
    private String amount1;

    @ApiModelProperty(value = "金额2")
    private String amount2;

    @ApiModelProperty(value = "金额3")
    private String amount3;

    @ApiModelProperty(value = "标志1")
    private String flag1Fg;

    @ApiModelProperty(value = "标志2")
    private String flag2Fg;

    @ApiModelProperty(value = "标志3")
    private String flag3Fg;

    @ApiModelProperty(value = "标志4")
    private String flag4Fg;

    @ApiModelProperty(value = "标志5")
    private String flag5Fg;

    @ApiModelProperty(value = "行文本")
    private String lineTx;

    @ApiModelProperty(value = "其他信息1")
    private String othMsg1Tx;

    @ApiModelProperty(value = "其他信息2")
    private String othMsg2Tx;

    @ApiModelProperty(value = "其他信息3")
    private String othMsg3Tx;

    @ApiModelProperty(value = "其他信息4")
    private String othMsg4Tx;

    @ApiModelProperty(value = "应答码")
    private String ansCd;

    @ApiModelProperty(value = "应答信息")
    private String ansTx;

    @ApiModelProperty(value = "处理标志")
    private String procFg;

    @ApiModelProperty(value = "当前断点信息")
    private String currBreakInfo;

    @ApiModelProperty(value = "最后修改程序标识")
    private String lastModifyPrgId;

    @ApiModelProperty(value = "最后修改柜员号")
    private String lastModifyTlrId;

    @ApiModelProperty(value = "最后修改时间")
    private String lastModifyTm;

    @ApiModelProperty(value = "其他控制信息1")
    private String othConMsg1Tx;

    @ApiModelProperty(value = "其他控制信息2")
    private String othConMsg2Tx;

    @ApiModelProperty(value = "其他预留信息1")
    private String othPreMsg1Tx;

    @ApiModelProperty(value = "其他预留信息2")
    private String othPreMsg2Tx;
}