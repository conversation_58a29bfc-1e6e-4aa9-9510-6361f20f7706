package com.xm.xzp.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;

@Data
@ApiModel(value = "批量交易明细展示对象")
public class BatchTranDtlDisplayVo {

    @ApiModelProperty(value = "批次号")
    private String batchId;

    @ApiModelProperty(value = "文件记账日期")
    private String fileInaccDt;

    @ApiModelProperty(value = "缴费号")
    private String payId;

    @ApiModelProperty(value = "账卡号")
    private String accCardId;

    @ApiModelProperty(value = "摘要")
    private String summary;

    @ApiModelProperty(value = "交易金额")
    private String tranAt;

    @ApiModelProperty(value = "应答码")
    private String ansCd;

    @ApiModelProperty(value = "应答信息")
    private String ansTx;

    @ApiModelProperty(value = "处理标志")
    private String procFg;
}