package com.xm.xzp.model.query;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 字典实体查询对象
 * </p>
 *
 * @version 1.0.0
 * <AUTHOR>
 * @date 2021-10-26 13:57
 */
@Data
public class DictEntryQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 字典类型代码
     */
    private String dictTypeId;

    /**
     * 字典项代码
     */
    private String dictId;

    /**
     * 字典项名称
     */
    private String dictName;

    /**
     * 状态 1-正常 2-停用
     */
    private String status;

}
