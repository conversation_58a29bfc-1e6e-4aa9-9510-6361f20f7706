package com.xm.xzp.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR> Ye
 * @description: TODO
 * @create 2024-09-13 14:52
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuppressWarnings("unchecked")
public class XzpDataVo<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 编码 0:不成功1: 成功
     */
    private String code;
    /**
     * 返回信息 code为0时必填
     */
    private String message;
    /**
     * 返回 数据 code为1时必填
     */
    private T data;

    /**
     * 快速返回操作成功响应结果(带响应数据)
     */
    public static <E> XzpDataVo<E> success(E data) {
        return new XzpDataVo<>("1", "操作成功", data);
    }

    public static XzpDataVo error(String message) {
        return new XzpDataVo("0", message, null);
    }
    public static XzpDataVo oth(String message) {
        return new XzpDataVo("2", message, null);
    }

}
