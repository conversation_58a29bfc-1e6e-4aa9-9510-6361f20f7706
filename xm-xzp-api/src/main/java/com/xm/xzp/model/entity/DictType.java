package com.xm.xzp.model.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.psbc.pfpj.yoaf.validator.custom.annotation.ListRange;
import com.psbc.pfpj.yoaf.validator.group.AddGroup;
import com.psbc.pfpj.yoaf.validator.group.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 字典类型表
 * </p>
 *
 * @version 1.0.0
 * <AUTHOR>
 * @date 2021-10-26 13:57
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("bc_pmctl_dict_type")
public class DictType implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 字典类型代码
     */
    @TableId
    @NotBlank(message = "字典类型代码不能为空", groups = { AddGroup.class, EditGroup.class })
    @Length(max = 64, message = "字典类型代码不能超过64个字符", groups = { AddGroup.class, EditGroup.class })
    private String dictTypeId;

    /**
     * 字典类型名称
     */
    @NotBlank(message = "字典类型名称不能为空", groups = { AddGroup.class, EditGroup.class })
    @Length(max = 255, message = "字典类型名称不能超过255个字符", groups = { AddGroup.class, EditGroup.class })
    private String dictTypeName;

    /**
     * 数据来源
     */
    @Length(max = 64, message = "数据来源不能超过64个字符", groups = { AddGroup.class, EditGroup.class })
    private String dataSource;

    /**
     * 状态 1-正常 2-停用
     */
    @NotBlank(message = "状态不能为空", groups = { AddGroup.class, EditGroup.class })
    @ListRange(value = { "1", "2" }, message = "状态取值必须在[1,2]之间", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 描述
     */
    @Length(max = 255, message = "描述长度不能超过255个字符", groups = { AddGroup.class, EditGroup.class })
    private String dictTypeDesc;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 最后维护人
     */
    private String updateUser;

    /**
     * 最后维护时间
     */
    private LocalDateTime updateTime;

}
