package com.xm.xzp.model.entity;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("tb_ope_cd")
@ApiModel(description = "业务代码查询")
public class OpeCd {
    @ApiModelProperty("操作码")
    private String opeCd;

    @ApiModelProperty("业务名称")
    private String opeNm;

    @ApiModelProperty("更新日期")
    private DateTime updateTm;
}