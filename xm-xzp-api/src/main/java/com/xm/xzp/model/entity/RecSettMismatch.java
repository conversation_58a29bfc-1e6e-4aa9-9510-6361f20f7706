package com.xm.xzp.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName("tb_3502_xmykt_dz_result")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "RecSettMismatch对象", description = "对账/清算不一致列表")
public class RecSettMismatch  implements Serializable {
    @ApiModelProperty(value = "不符类型")
    private String recordType;
    @ApiModelProperty(value = "交易日期")
    private String tranDt;
    @ApiModelProperty(value = "委托单位代码")
    private String merchId;
    @ApiModelProperty(value = "业务代码")
    private String opeCd;
    @ApiModelProperty(value = "子业务")
    private String typeCd;
    @ApiModelProperty(value = "用户号")
    private String userId;
    @ApiModelProperty(value = "对账结果")
    private String recordSta;
    @ApiModelProperty(value = "费用单位")
    private String unitdId;
    @ApiModelProperty(value = "交易代号")
    private String tranCd;
    @ApiModelProperty(value = "交易名称")
    private String tranNm;
    @ApiModelProperty(value = "银联清算日期")
    private String merchDt;
    @ApiModelProperty(value = "邮储日期")
    private String hostDt;
    @ApiModelProperty(value = "费用单位")
    private String itemId;
    @ApiModelProperty(value = "委托方流水号")
    private String traceId;
    @ApiModelProperty(value = "交易机构")
    private String tranInstId;
    @ApiModelProperty(value = "中平流水号")
    private String tranSq;
    @ApiModelProperty(value = "卡号")
    private String accCardId;
    @ApiModelProperty(value = "交易金额")
    private BigDecimal tranAmt;
    private String miscTx;
    @ApiModelProperty(value = "预留")
    private String othMsg1;
    @ApiModelProperty(value = "预留")
    private String othMsg2;
}
