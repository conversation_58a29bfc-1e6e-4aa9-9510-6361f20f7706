package com.xm.xzp.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MerchSubOpeVo {
    @ApiModelProperty(value = "委托单位代码")
    private String merchId;
    @ApiModelProperty(value = "业务代码")
    private String opeCd;
    @ApiModelProperty(value = "子业务代码")
    private String subOpeId;
    @ApiModelProperty(value = "子业务名称")
    private String subOpeNm;
    @ApiModelProperty(value = "SP商户代码")
    private String spMerchId;
    @ApiModelProperty(value="起始时间")
    private String startTime;
    @ApiModelProperty(value="结束时间")
    private String endTime;
}