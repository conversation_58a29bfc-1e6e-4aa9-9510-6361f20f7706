package com.xm.xzp.model.vo;

import lombok.Data;

@Data
public class IntTxnLogVo {

    /**
     * 起始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;
    /**
     * 委托代码
     */
    private String merchId;
    /**
     * 业务代码
     */
    private String opeCd;
    /**
     * 子业务代码+用户号
     */
    private String payId;

    /**
     * 子业务代码
     */
    private String subOpeCd;
    /**
     * 用户号
     */
    private String userCd;
    /**
     * 卡号
     */
    private String accCardId;

    /**
     * 开始日期 (用于SQL中的bng_date参数)
     */
    public String getBngDate() {
        return this.startTime;
    }

    /**
     * 结束日期 (用于SQL中的end_date参数)
     */
    public String getEndDate() {
        return this.endTime;
    }
}