package com.xm.xzp.model.entity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName("tb_batch_process_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "BatchProcessLog对象", description = "批量处理日志表")
public class BatchProcessLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "交易日期")
    private String tranDt;

    @ApiModelProperty(value = "商户号")
    private String merchId;

    @ApiModelProperty(value = "操作码")
    private String opeCd;

    @ApiModelProperty(value = "商户名称")
    private String merchName;

    @ApiModelProperty(value = "处理备注")
    private String procMemo;

    @ApiModelProperty(value = "输入文件名")
    private String inFileName;

    @ApiModelProperty(value = "输出文件名")
    private String outFileName;

    @ApiModelProperty(value = "批次号")
    private String batchId;

    @ApiModelProperty(value = "总笔数")
    private BigDecimal tolNum;

    @ApiModelProperty(value = "总金额")
    private BigDecimal tolAmt;

    @ApiModelProperty(value = "实际笔数")
    private BigDecimal actNum;

    @ApiModelProperty(value = "实际金额")
    private BigDecimal actAmt;

    @ApiModelProperty(value = "处理标志")
    private String procFlag;

    @ApiModelProperty(value = "处理原因")
    private String procReason;

    @ApiModelProperty(value = "加载时间")
    private String loadTime;
}