package com.xm.xzp.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 业务查询请求VO
 * <AUTHOR>
 */
@Data
@ApiModel(description = "业务查询请求参数")
public class BusinessQueryVo {

    @ApiModelProperty(value = "业务类型", required = true, example = "CHANGE")
    @NotBlank(message = "业务类型不能为空")
    private String busikind;

    @ApiModelProperty(value = "交易代码", required = true, example = "Feedbac")
    @NotBlank(message = "交易代码不能为空")
    private String tradecode;

    @ApiModelProperty(value = "商户号", required = true, example = "350200050314")
    @NotBlank(message = "商户号不能为空")
    private String merchid;

    @ApiModelProperty(value = "操作代码", required = true, example = "101064")
    @NotBlank(message = "操作代码不能为空")
    private String opecd;

    @ApiModelProperty(value = "员工姓名", required = true)
    @NotBlank(message = "员工姓名不能为空")
    private String empname;

    @ApiModelProperty(value = "员工代码", required = true)
    @NotBlank(message = "员工代码不能为空")
    private String empcode;

    @ApiModelProperty(value = "机构代码", required = true)
    @NotBlank(message = "机构代码不能为空")
    private String orgcode;

    @ApiModelProperty(value = "机构级别", required = true, example = "2")
    @NotBlank(message = "机构级别不能为空")
    private String orgdegree;

    @ApiModelProperty(value = "账户", required = true)
    @NotBlank(message = "账户不能为空")
    private String account;

    @ApiModelProperty(value = "开始日期", required = true, example = "********")
    @NotBlank(message = "开始日期不能为空")
    private String bgn_date;

    @ApiModelProperty(value = "结束日期", required = true, example = "********")
    @NotBlank(message = "结束日期不能为空")
    private String end_date;

    @ApiModelProperty(value = "目标URL", required = true)
    @NotBlank(message = "目标URL不能为空")
    private String targetUrl;

    @ApiModelProperty(value = "操作类型", required = true, example = "query")
    @NotBlank(message = "操作类型不能为空")
    private String action;
}
