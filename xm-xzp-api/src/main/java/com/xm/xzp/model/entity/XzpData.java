package com.xm.xzp.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * @Description: 营销积分零售信贷业务表
 * @Author: Yedehui
 * @Date: 2024-09-18
 * @Version: V1.0
 */
@Data
@TableName("t_js_yxjf_lsxdye")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "t_js_yxjf_lsxdye对象", description = "新一代综管表")
public class XzpData implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 数据日期
     */
    @ApiModelProperty(value = "数据日期")
    private String summDate;
    /**
     * 贷款申请ID
     */
    @ApiModelProperty(value = "贷款申请ID")
    private String loanApplyId;
    /**
     * 借据编号
     */
    @ApiModelProperty(value = "借据编号")
    private String loanRctNo;
    /**
     * 合同号
     */
    @ApiModelProperty(value = "合同号")
    private String contractNo;
    /**
     * 客户ID
     */
    @ApiModelProperty(value = "客户ID")
    private String custId;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String custName;
    /**
     * 贷款种类编号
     */
    @ApiModelProperty(value = "贷款种类编号")
    private String loanKindNo;
    /**
     * 贷款起始日期
     */
    @ApiModelProperty(value = "贷款起始日期")
    private String loanRctBgnDate;
    /**
     * 贷款终止日期
     */
    @ApiModelProperty(value = "贷款终止日期")
    private String loanRctEndDate;
    /**
     * 贷款出账金额
     */
    @ApiModelProperty(value = "贷款出账金额")
    private BigDecimal loanOutaccAmt;
    /**
     * 贷款合同金额
     */
    @ApiModelProperty(value = "贷款合同金额")
    private BigDecimal loanContractAmt;
    /**
     * 贷款利率
     */
    @ApiModelProperty(value = "贷款利率")
    private String loanIntRate;
    /**
     * 利率调整方式
     */
    @ApiModelProperty(value = "利率调整方式")
    private String intRateAdjMode;
    /**
     * 贷款余额（余额为（贷款余额+减值损失余额））
     */
    @ApiModelProperty(value = "贷款余额（余额为（贷款余额+减值损失余额））")
    private String loanBal;
    /**
     * 减值损失余额
     */
    @ApiModelProperty(value = "减值损失余额")
    private String devalLossBal;
    /**
     * 销户日期
     */
    @ApiModelProperty(value = "销户日期")
    private String clsAccDate;
    /**
     * 还款方式
     */
    @ApiModelProperty(value = "还款方式")
    private String repayMode;
    /**
     * 主担保方式（1-信用；2-保证；3-抵押；4-质押；9-其他）
     */
    @ApiModelProperty(value = "主担保方式（1-信用；2-保证；3-抵押；4-质押；9-其他）")
    private String mainGuaMode;
    /**
     * 经办机构号
     */
    @ApiModelProperty(value = "经办机构号")
    private String busiBelongInstNo;
    /**
     * 业务管户人ID
     */
    @ApiModelProperty(value = "业务管户人ID")
    private String busiAccMngPersId;
    /**
     * 业务管户人名称
     */
    @ApiModelProperty(value = "业务管户人名称")
    private String busiAccMngPersName;
    /**
     * 贷款状态代码
     */
    @ApiModelProperty(value = "贷款状态代码")
    private String loanStatusCode;
    /**
     * 额度合同编号
     */
    @ApiModelProperty(value = "额度合同编号")
    private String limitContractNo;
    /**
     * 结清日期
     */
    @ApiModelProperty(value = "结清日期")
    private String payOffDate;
    /**
     * 是否农户标志
     */
    @ApiModelProperty(value = "是否农户标志")
    private String farmFlag;
    /**
     * 客户行业类型代码
     */
    @ApiModelProperty(value = "客户行业类型代码")
    private String indsTypeCode;
    /**
     * 贷款五级分类代码（1-正常;2-关注;3-次级;4-可疑;5-损失）
     */
    @ApiModelProperty(value = "贷款五级分类代码（1-正常;2-关注;3-次级;4-可疑;5-损失）")
    private String loanLvl5ClassCode;
    /**
     * 客户主体类别（06-个体工商户;07-小微企业主;15其他）
     */
    @ApiModelProperty(value = "客户主体类别（06-个体工商户;07-小微企业主;15其他）")
    private String farmOrMerMainbCateg;
    /**
     * 是否涉农贷款
     */
    @ApiModelProperty(value = "是否涉农贷款")
    private String snFlag;
    /**
     * 经办机构名称
     */
    @ApiModelProperty(value = "经办机构名称")
    private String busiBelongInstName;
    /**
     * 币种代码
     */
    @ApiModelProperty(value = "币种代码")
    private String currCode;
    /**
     * 贷款种类编号
     */
    @ApiModelProperty(value = "贷款种类编号")
    private String loanKindName;
    /**
     * 数据来源标识（1-信贷业务平台；2-互联网网贷）
     */
    @ApiModelProperty(value = "数据来源标识（1-信贷业务平台；2-互联网网贷）")
    private String srcFlag;
    /**
     * 作业执行日期
     */
    @ApiModelProperty(value = "作业执行日期")
    private String bdsEtlJobDt;
    /**
     * 放款账户账号
     */
    @ApiModelProperty(value = "放款账户账号")
    private String disburseAccno;
    /**
     * 还款账户账号
     */
    @ApiModelProperty(value = "还款账户账号")
    private String repayAccAccNo;
    /**
     * 利率类型
     */
    @ApiModelProperty(value = "利率类型")
    private String intRateType;
    /**
     * 贴息前执行利率
     */
    @ApiModelProperty(value = "贴息前执行利率")
    private String subsidyBfrExecIntRate;
    /**
     * 居住详细地址
     */
    @ApiModelProperty(value = "居住详细地址")
    private String rsdDtlAddr;
    /**
     * 贷款投向
     */
    @ApiModelProperty(value = "贷款投向")
    private String loanDrctCd;
    /**
     * 证件号码_脱敏
     */
    @ApiModelProperty(value = "证件号码_脱敏")
    private String zjhmTm;
    /**
     * 户籍地址
     */
    @ApiModelProperty(value = "户籍地址")
    private String hsRegAddr;

    private String numb;

}
