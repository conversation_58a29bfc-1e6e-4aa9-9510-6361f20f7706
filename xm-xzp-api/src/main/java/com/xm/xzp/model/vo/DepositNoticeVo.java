package com.xm.xzp.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 存款通知请求VO
 * <AUTHOR>
 */
@Data
@ApiModel(description = "存款通知请求参数")
public class DepositNoticeVo {

    @ApiModelProperty(value = "业务类型", required = true, example = "DEPOSIT")
    @NotBlank(message = "业务类型不能为空")
    private String busikind;

    @ApiModelProperty(value = "交易代码", required = true, example = "Card")
    @NotBlank(message = "交易代码不能为空")
    private String tradecode;

    @ApiModelProperty(value = "委托单位代码", required = true)
    @NotBlank(message = "委托单位代码不能为空")
    private String merchid;

    @ApiModelProperty(value = "业务代码", required = true)
    @NotBlank(message = "业务代码不能为空")
    private String opecd;

    @ApiModelProperty(value = "子业务号")
    private String sub_ope_cd;

    @ApiModelProperty(value = "员工姓名", required = true)
    @NotBlank(message = "员工姓名不能为空")
    private String empname;

    @ApiModelProperty(value = "员工代码", required = true)
    @NotBlank(message = "员工代码不能为空")
    private String empcode;

    @ApiModelProperty(value = "机构代码", required = true)
    @NotBlank(message = "机构代码不能为空")
    private String orgcode;

    @ApiModelProperty(value = "机构级别", required = true, example = "2")
    @NotBlank(message = "机构级别不能为空")
    private String orgdegree;

    @ApiModelProperty(value = "缴费号码", required = true)
    @NotBlank(message = "缴费号码不能为空")
    private String account;

    @ApiModelProperty(value = "用户号")
    private String pay_id;

    @ApiModelProperty(value = "目标URL", required = true)
    @NotBlank(message = "目标URL不能为空")
    private String targetUrl;

    @ApiModelProperty(value = "操作类型", required = true, example = "deposit_notice")
    @NotBlank(message = "操作类型不能为空")
    private String action;

    @ApiModelProperty(value = "发送方流水号", example = "20240805123456789012")
    private String sendseqno;
}