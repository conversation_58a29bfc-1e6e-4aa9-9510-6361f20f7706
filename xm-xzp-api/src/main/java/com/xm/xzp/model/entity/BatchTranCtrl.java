package com.xm.xzp.model.entity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName("tb_batch_tran_ctrl")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "BatchTranCtrl对象", description = "批量交易控制表")
public class BatchTranCtrl implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "批次号")
    private String batchId;

    @ApiModelProperty(value = "核心批次号")
    private String coreBatchId;

    @ApiModelProperty(value = "交易日期")
    private String tranDt;

    @ApiModelProperty(value = "计划交易日期")
    private String planTranDt;

    @ApiModelProperty(value = "交易机构号")
    private String tranInstId;

    @ApiModelProperty(value = "节点类型")
    private String nodeType;

    @ApiModelProperty(value = "交易代码")
    private String tranCd;

    @ApiModelProperty(value = "批量请求交易代码")
    private String batchReqTranCd;

    @ApiModelProperty(value = "商户号")
    private String merchId;

    @ApiModelProperty(value = "操作码")
    private String opeCd;

    @ApiModelProperty(value = "总笔数")
    private BigDecimal totalQt;

    @ApiModelProperty(value = "总金额")
    private BigDecimal totalAt;

    @ApiModelProperty(value = "成功笔数")
    private BigDecimal succQt;

    @ApiModelProperty(value = "成功金额")
    private BigDecimal succAt;

    @ApiModelProperty(value = "失败笔数")
    private BigDecimal failQt;

    @ApiModelProperty(value = "失败金额")
    private BigDecimal failAt;

    @ApiModelProperty(value = "执行日期")
    private String execDt;

    @ApiModelProperty(value = "文件接入日期")
    private String fileInaccDt;

    @ApiModelProperty(value = "柜员号")
    private String tlrId;

    @ApiModelProperty(value = "授权柜员号")
    private String authTlrId;

    @ApiModelProperty(value = "机构终端号")
    private String instTermId;

    @ApiModelProperty(value = "请求文件名")
    private String reqFileNameTx;

    @ApiModelProperty(value = "请求文件日期")
    private String reqFileDt;

    @ApiModelProperty(value = "状态文件名(输入)")
    private String staFileNameI;

    @ApiModelProperty(value = "状态文件名(输出)")
    private String staFileNameO;

    @ApiModelProperty(value = "响应文件名")
    private String rspFileName;

    @ApiModelProperty(value = "其他信息1")
    private String othMsg1Tx;

    @ApiModelProperty(value = "其他信息2")
    private String othMsg2Tx;

    @ApiModelProperty(value = "其他信息3")
    private String othMsg3Tx;

    @ApiModelProperty(value = "渠道代码")
    private BigDecimal chnlCd;

    @ApiModelProperty(value = "流水号")
    private BigDecimal jnlCd;

    @ApiModelProperty(value = "处理标志")
    private String procFg;

    @ApiModelProperty(value = "应答码")
    private String ansCd;

    @ApiModelProperty(value = "应答信息")
    private String ansTx;

    @ApiModelProperty(value = "文件名")
    private String fileNm;

    @ApiModelProperty(value = "最后修改程序ID")
    private String lastModifyPrgId;

    @ApiModelProperty(value = "最后修改柜员号")
    private String lastModifyTlrId;

    @ApiModelProperty(value = "最后修改时间")
    private String lastModifyTm;
}