package com.xm.xzp.model.vo;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class BatchTranCtrlVo {
    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyyMMdd")
    private String startTime;
    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyyMMdd")
    private String endTime;
    /**
     * 操作码
     */
    private String opeCd;
    /**
     * 委托单位代码
     */
    private String merchId;
    /**
     * 状态
     */
    private String ansCd;
    /**
     * 标志
     */
    private String procFg;
    /**
     * 交易代码
     */
    private String tranCd;
    /**
     * 交易代码
     */
    private String ansTx;
}
