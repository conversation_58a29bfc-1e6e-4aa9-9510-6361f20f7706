package com.xm.xzp.model.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 批量删除字典实体对象
 * </p>
 *
 * @version 1.0.0
 * <AUTHOR>
 * @date 2021-10-26 13:57
 */
@Data
public class DictEntryPKVO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 字典类型代码
     */
    private String dictTypeId;

    /**
     * 字典实体代码
     */
    private String dictId;

}
