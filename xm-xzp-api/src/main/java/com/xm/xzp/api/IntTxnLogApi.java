package com.xm.xzp.api;

import com.github.pagehelper.PageInfo;
import com.psbc.pfpj.yoaf.response.RestResponse;
import com.xm.xzp.model.entity.IntTxnLog;
import com.xm.xzp.model.vo.IntTxnLogResultVo;
import com.xm.xzp.model.vo.IntTxnLogVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@ResponseBody
@RequestMapping("/api/admin/xzp/txn")
@Api(tags = "交易流水查询")
@Validated
public interface IntTxnLogApi {
    
    @ApiOperation(value = "交易流水查询", notes = "intTxnLogList")
    @PostMapping("/intTxnLogList")
    RestResponse<PageInfo<IntTxnLog>> intTxnLogList(
            @RequestBody IntTxnLogVo intTxnLog,
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize);

    @ApiOperation(value = "交易流水查询", notes = "intTxnLogList")
    @PostMapping("/selectIntTxnLogList")
    RestResponse<PageInfo<IntTxnLogResultVo>> selectIntTxnLogList(
            @RequestBody IntTxnLogVo intTxnLog,
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize);

    @ApiOperation(value = "缴费明细查询", notes = "queryTxnLogDetail")
    @PostMapping("/queryTxnLogDetail")
    RestResponse<List<Object>> queryTxnLogDetail(@RequestBody IntTxnLogVo intTxnLog);

    @ApiOperation(value = "导出缴费明细", notes = "exportTxnLogDetail")
    @PostMapping("/exportTxnLogDetail")
    void exportTxnLogDetail(
            @RequestBody IntTxnLogVo intTxnLog,
            HttpServletResponse response) throws IOException;
}