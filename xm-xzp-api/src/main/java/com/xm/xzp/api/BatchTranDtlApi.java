package com.xm.xzp.api;

import com.github.pagehelper.PageInfo;
import com.psbc.pfpj.yoaf.response.RestResponse;
import com.xm.xzp.model.entity.BatchTranDtl;
import com.xm.xzp.model.vo.BatchTranDtlDisplayVo;
import com.xm.xzp.model.vo.BatchTranDtlVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Api(tags = "批量交易明细")
@ResponseBody
@RequestMapping("/api/admin/xzp")
@Validated
public interface BatchTranDtlApi {

    @ApiOperation(value = "分页查询批量交易明细", notes = "batchTranDtlList")
    @PostMapping("/batchTranDtlList")
    RestResponse<PageInfo<BatchTranDtlDisplayVo>> batchTranDtlList(
            @RequestBody BatchTranDtlVo batchTranDtlVo,
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize);
}