package com.xm.xzp.api;


import com.github.pagehelper.PageInfo;
import com.psbc.pfpj.yoaf.response.RestResponse;
import com.xm.xzp.model.entity.BatchProcessLog;
import com.xm.xzp.model.entity.XzpOpLog;
import com.xm.xzp.model.vo.BatchProcessLogVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 */
@ResponseBody
@RequestMapping("/api/admin/xzp/log")
@Api(tags = "批量交易查询")
@Validated
public interface BatchProcessLogApi {
  /**
     * 日志查询
     *
     * @param pageNum    当前页
     * @param batchProcessLog 接收数据
     * @param pageSize   每页沙宣数量
     * @return 数据列表
     */
    @ApiOperation(value = "外联批量处理日志", notes = "batchProcessLogList")
    @PostMapping("/batchProcessLogList")
    RestResponse<PageInfo<BatchProcessLog>> batchProcessLogList(@RequestBody BatchProcessLogVo batchProcessLog, @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum, @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize);


}
