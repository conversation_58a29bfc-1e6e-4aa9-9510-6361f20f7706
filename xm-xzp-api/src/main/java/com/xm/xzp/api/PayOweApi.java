package com.xm.xzp.api;

import com.github.pagehelper.PageInfo;
import com.psbc.pfpj.yoaf.response.RestResponse;
import com.xm.xzp.model.entity.PayOwe;
import com.xm.xzp.model.entity.PayOweVo;
import com.xm.xzp.model.vo.PayOweUpdateVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Api(tags = "欠费记录管理")
@ResponseBody
@RequestMapping("/api/admin/xzp")
@Validated
public interface PayOweApi {

    @ApiOperation(value = "批量更新交易状态", notes = "根据操作码、商户号和批次号批量更新交易状态")
    @PostMapping("/batchUpdateTxnStaToZero")
    RestResponse<Integer> batchUpdateTxnStaToZero(@RequestBody PayOweUpdateVo payOweUpdateVo);


    @ApiOperation(value = "分页查询欠费记录", notes = "payOweList")
    @PostMapping("/payOweList")
    RestResponse<PageInfo<PayOwe>> payOweList(
            @RequestBody PayOweVo payOweVo,
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize);
}