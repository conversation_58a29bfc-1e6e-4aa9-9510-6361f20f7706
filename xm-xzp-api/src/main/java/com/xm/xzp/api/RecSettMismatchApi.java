package com.xm.xzp.api;

import com.github.pagehelper.PageInfo;
import com.psbc.pfpj.yoaf.response.RestResponse;
import com.xm.xzp.model.entity.RecSettMismatch;
import com.xm.xzp.model.vo.RecSettMismatchVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@ResponseBody
@RequestMapping("/api/admin/xzp")
@Api(tags = "对账/清算不一致查询")
@Validated
public interface RecSettMismatchApi {
    /**
     *
     * @param pageNum    当前页
     * @param recSettMismatchVo 接收数据
     * @param pageSize   每页数量
     * @return 数据列表
     */
    @ApiOperation(value = "对账/清算不一致列表", notes = "recSettMismatchList")
    @PostMapping("/recSettMismatchList")
    RestResponse<PageInfo<RecSettMismatch>> recSettMismatchList(@RequestBody RecSettMismatchVo recSettMismatchVo,
                                                                @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize);

    @ApiOperation(value = "编辑对账/清算不一致信息", notes = "editRecSettMismatch")
    @PostMapping("/recSettMismatchEdit")
    RestResponse<String> recSettMismatchEdit(@RequestBody RecSettMismatch recSettMismatch);

}
