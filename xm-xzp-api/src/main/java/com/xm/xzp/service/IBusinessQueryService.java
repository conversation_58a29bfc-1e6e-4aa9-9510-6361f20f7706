package com.xm.xzp.service;

import com.xm.xzp.model.vo.BusinessQueryVo;
import com.xm.xzp.model.vo.CardChangeNoticeVo;
import com.xm.xzp.model.vo.DepositNoticeVo;

/**
 * 业务查询服务接口
 * <AUTHOR>
 */
public interface IBusinessQueryService {

    /**
     * 执行业务查询转发
     * 将请求转发到指定的目标URL
     *
     * @param businessQueryVo 业务查询请求参数
     * @return 转发后的响应结果
     */
    Object executeBusinessQuery(BusinessQueryVo businessQueryVo);

    /**
     * 执行换卡通知转发
     * 将请求转发到指定的目标URL
     *
     * @param cardChangeNoticeVo 换卡通知请求参数
     * @return 转发后的响应结果
     */
    Object executeCardChangeNotice(CardChangeNoticeVo cardChangeNoticeVo);

    /**
     * 执行存款通知转发
     * 将请求转发到指定的目标URL
     *
     * @param depositNoticeVo 存款通知请求参数
     * @return 转发后的响应结果
     */
    Object executeDepositNotice(DepositNoticeVo depositNoticeVo);
}
