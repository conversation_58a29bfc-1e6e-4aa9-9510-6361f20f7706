# Spring框架相关配置
spring:
  application:
    #name: yoaf-cluster
    name: xm-xzp
  cloud:
    # nacos注册中心配置
    nacos:
      discovery:
        enabled: false
        server-addr: frp-fit.com:13603 # nacos 服务地址
#        server-addr: 10.220.50.36:28848 # nacos 服务地址
        username: nacos
        password: nacos
        namespace: ${pfpj.application.sys-code}
        metadata:
          pfpj:
            sys-code:  ${pfpj.application.sys-code}
            inst-grp-code:  DEFAULT
            pid: pid
            spring:
              application:
                name: ${spring.application.name}
            servlet-context-path:
            hystrix-stream-enabled: false
#      config:
#        enabled: true #开启使用nacos
#        #server-addr: 20.200.176.128:8848  #修改配置中心地址
#        server-addr: 10.220.50.36:28848  #修改配置中心地址
#        namespace: ${pfpj.application.sys-code}
# JAVA开发平台相关配置
pfpj:
  application:
    app-code: xm-xzp # governor建应用时设置的应用编码
    sys-code: xydpt-system # governor建系统时设置的系统编码
    sys-key: 0b726f0fe7514549aead1c21fb695b54 # governor建立系统后，生成的系统凭证
    app-inst-grp-code: default # governor给应用创建的配置组，默认为default

# 注册中心eureka相关配置
#eureka:
#  client:
#    # eureka server地址
#    service-url:
#      defaultZone: http://************:8761/eureka/
#  instance:
#    prefer-ip-address: true # 注册服务时使用ip注册

# 配置中心apollo相关配置
#apollo:
#  bootstrap:
#    enabled: true # 启用apollo
#  meta: http://************:18081 # apollo-configservice服务地址
#app:
#  id: yoaf-cluster # apollo配置应用的appid(同governor建应用时设置的应用编码)