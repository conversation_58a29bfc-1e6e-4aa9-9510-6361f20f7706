<assembly xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/assembly-1.0.0.xsd">
	<id>package</id>
	<formats>
		<format>tar.gz</format>
	</formats>
	<includeBaseDirectory>false</includeBaseDirectory>

	<fileSets>
		<!-- 程序本身的jar -->
		<fileSet>
			<directory>target</directory>
			<outputDirectory>.</outputDirectory>
			<includes>
				<include>${project.build.finalName}.jar</include>
			</includes>
			<fileMode>0755</fileMode>
		</fileSet>

		<!-- 程序的配置 -->
		<fileSet>
			<directory>src/main/resources</directory>
			<outputDirectory>config</outputDirectory>
			<includes>
				<include>*.yml</include>
				<include>*.properties</include>
				<include>ehcache.xml</include>
				<include>logback-spring.xml</include>
			</includes>
			<excludes>
				<exclude>yoaf-all.yml</exclude>
			</excludes>
		</fileSet>

		<!-- 程序的启动脚本 -->
		<fileSet>
			<directory>src/main/scripts</directory>
			<includes>
				<include>*.sh</include>
				<include>*.bat</include>
			</includes>
			<outputDirectory>bin</outputDirectory>
			<lineEnding>unix</lineEnding>
			<fileMode>0755</fileMode>
		</fileSet>

		<!-- 程序的sql脚本 -->
		<fileSet>
			<directory>src/main/resources/db</directory>
			<includes>
				<include>*.sql</include>
			</includes>
			<outputDirectory>sql</outputDirectory>
		</fileSet>
	</fileSets>
</assembly>
