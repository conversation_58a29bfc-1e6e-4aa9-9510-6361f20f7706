#!/bin/sh

PRG="$0"
saveddir=`pwd`
PFPJ_DAP_HOME=`dirname "$PRG"`/..
PFPJ_DAP_HOME=`cd "$PFPJ_DAP_HOME" && pwd`
cd "$saveddir"

export APP_NAME=PFPJ1.0.0_YOAF_SERVER
export MODE=service
export LOG_FOLDER="${PFPJ_DAP_HOME}"/logs
export PID_FOLDER="${LOG_FOLDER}"
export LOG_FILENAME="${APP_NAME}.out" # log console for background mode running

BOOT_JAR=`echo "${PFPJ_DAP_HOME}"/*.jar`
$BOOT_JAR stop "$@"

