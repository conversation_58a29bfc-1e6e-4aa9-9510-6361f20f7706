<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>xm-xzp</artifactId>
        <groupId>com.xm.xzp</groupId>
        <version>1.1.2</version>
        <!--<relativePath>../../xm-xzp/pom.xml</relativePath>-->
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>xm-xzp-boot</artifactId>

    <properties>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- yoaf权限框架依赖包管理 -->
            <dependency>
                <groupId>com.psbc.pfpj</groupId>
                <artifactId>yoaf-server</artifactId>
                <version>1.1.2</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!--服务发现-->
        <!--【该行报错先注释掉】<dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>-->
        <!-- Eureka客户端-->
        <!--   <dependency>
               <groupId>org.springframework.cloud</groupId>
               <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
           </dependency>-->
        <!-- apollo客户端 -->
        <!-- <dependency>
             <groupId>com.psbc</groupId>
             <artifactId>pfpj-sdk-apollo-client</artifactId>
             <version>2.5.0</version>
         </dependency>-->
        <!-- nacos客户端依赖 使用nacos注册中心时导入 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <!--<dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
            <version>2.2.6.RELEASE</version>
        </dependency>-->
        <!-- nacos配置中心依赖 -->
        <!--【该行报错先注释掉】<dependency>
            <groupId>com.psbc</groupId>
            <artifactId>pfpj-sdk-nacos-client</artifactId>
            <version>2.5.2</version>
        </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
        <!-- 认证中心模块 -->
        <dependency>
            <groupId>com.psbc.pfpj</groupId>
            <artifactId>yoaf-auth-center-starter</artifactId>
        </dependency>
        <!-- 多数据源配置 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
            <version>21.7.0.0</version>
        </dependency>

        <dependency>
            <groupId>cn.easyproject</groupId>
            <artifactId>orai18n</artifactId>
            <version>11.2.0.4</version>
        </dependency>

        <!-- 认证中心模块-统一认证 -->
        <!--  <dependency>
              <groupId>com.psbc.pfpj</groupId>
              <artifactId>yoaf-auth-center-starter-uaas</artifactId>
          </dependency>-->
        <!-- 认证中心模块-统一认证 -->
        <!--  <dependency>
              <groupId>com.psbc.pfpj</groupId>
              <artifactId>yoaf-auth-center-starter-ldap</artifactId>
          </dependency>-->
        <!-- 审计日志模块 -->
        <dependency>
            <groupId>com.psbc.pfpj</groupId>
            <artifactId>yoaf-auditlog-starter</artifactId>
        </dependency>
        <!-- 厦门新中平-功能实现模块 -->
        <dependency>
            <groupId>com.xm.xzp</groupId>
            <artifactId>xm-xzp-impl</artifactId>
        </dependency>
     <!--   <dependency>
            <groupId>com.psbc.pfpj</groupId>
            <artifactId>yoaf-admin-impl</artifactId>
            <version>1.1.2</version>
            <scope>compile</scope>
        </dependency>-->
        <!--<dependency>
            <groupId>com.psbc</groupId>
            <artifactId>DAtechPSBCApi</artifactId>
            <version>2.1</version>
        </dependency>-->
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <!-- Spring Boot应用打包插件 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.3.12.RELEASE</version>
                <!--若下面的configuration注释后，将打全量大包，含所有lib的jar包；否则为小包-->
               <configuration>
                    <layout>ZIP</layout>
                    <includes>
                        <include>
                            <groupId>nothing</groupId>
                            <artifactId>nothing</artifactId>
                        </include>
                    </includes>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <!--<configuration>
                    <executable>true</executable>
                </configuration>-->
            </plugin>
            <!-- 项目定制化打包插件 -->
            <!--【该行报错先注释掉】<plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <configuration>
                    <attach>true</attach>
                    <appendAssemblyId>false</appendAssemblyId>
                    <tarLongFileMode>gnu</tarLongFileMode>
                    <descriptors>
                        <descriptor>src/main/assembly/assembly.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>-->
        </plugins>
    </build>
    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <env>dev</env>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <env>test</env>
            </properties>
        </profile>
        <profile>
            <id>ldap</id>
            <properties>
                <env>ldap</env>
            </properties>
        </profile>
        <profile>
            <id>uaas</id>
            <properties>
                <env>uaas</env>
            </properties>
        </profile>
    </profiles>

</project>