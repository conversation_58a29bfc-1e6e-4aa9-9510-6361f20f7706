# 敏感数据保护机制总结

## 问题描述
在敏感信息显隐功能中，当用户查看脱敏数据后进行修改操作时，脱敏后的数据（包含*号）被错误地保存到数据库中，导致监管账户和账户名被永久性地修改为脱敏值。

## 解决方案

### 1. 前端保护机制

#### 1.1 表单字段配置保护
在 `acctMgmtOption.js` 中，敏感字段已设置为编辑时不可见：
```javascript
{
  label: '监管账户',
  prop: 'cpabAccId',
  editDisplay: false,  // 编辑时不显示，防止用户修改
  // ...
},
{
  label: '监管账户名',
  prop: 'acctNm', 
  editDisplay: false,  // 编辑时不显示，防止用户修改
  // ...
}
```

#### 1.2 编辑时获取原始数据
修改 `handleEdit` 方法，在编辑前先获取原始数据：
```javascript
handleEdit(row, index) {
  // 如果有oth_msg2_tx，先获取原始数据（非脱敏）再编辑
  if (row.othMsg2Tx) {
    getAcctInfoByOthMsg2Tx(row.othMsg2Tx).then(res => {
      if (res.code === '0' && res.data) {
        // 使用原始数据进行编辑，确保不会编辑脱敏后的数据
        this.form = { ...res.data };
        this.$refs.crud.rowEdit(res.data, index);
      }
    });
  }
}
```

### 2. 后端保护机制

#### 2.1 脱敏数据检测与恢复
在 `updateAcctInfo` 和 `updateAcctInfoByOthMsg2Tx` 方法中添加保护逻辑：

```java
// 检查并防止敏感字段被脱敏后的值覆盖
String originalCpabAccId = existingInfo.getCpabAccId();
String originalAcctNm = existingInfo.getAcctNm();

// 检查是否为脱敏数据（包含*号）
if (StringUtils.hasText(acctInfo.getCpabAccId()) && acctInfo.getCpabAccId().contains("*")) {
    log.warn("检测到脱敏后的公司账户数据，保持原值不变。原值：{}，脱敏值：{}", 
            originalCpabAccId, acctInfo.getCpabAccId());
    acctInfo.setCpabAccId(originalCpabAccId);
}

if (StringUtils.hasText(acctInfo.getAcctNm()) && acctInfo.getAcctNm().contains("*")) {
    log.warn("检测到脱敏后的账户名数据，保持原值不变。原值：{}，脱敏值：{}", 
            originalAcctNm, acctInfo.getAcctNm());
    acctInfo.setAcctNm(originalAcctNm);
}
```

#### 2.2 使用原始数据作为更新条件
在 `updateAcctInfo` 方法中，使用原始的账户信息作为更新条件：
```java
// 执行更新 - 使用原始值作为查询条件
boolean result = this.lambdaUpdate()
        .eq(TbZjjgAcctInfo::getCpabAccId, originalCpabAccId)
        .eq(TbZjjgAcctInfo::getAcctNm, originalAcctNm)
        .update(acctInfo);
```

### 3. 多层保护机制

#### 3.1 第一层：前端表单配置
- 敏感字段在编辑表单中不显示
- 用户无法直接修改这些字段

#### 3.2 第二层：前端数据获取
- 编辑时主动获取原始数据
- 确保编辑的是真实数据而非脱敏数据

#### 3.3 第三层：后端数据验证
- 检测传入数据是否为脱敏数据
- 自动恢复为原始值
- 记录保护操作的日志

#### 3.4 第四层：数据库更新逻辑
- 使用原始数据作为查询条件
- 防止因脱敏数据导致的更新失败

## 修改的文件列表

### 前端文件
1. `src/views/xzp/acctmgmt/index.vue`
   - 修改 `handleEdit` 方法，编辑前获取原始数据

2. `src/views/xzp/acctmgmt/infoData/acctMgmtOption.js`
   - 敏感字段已配置为 `editDisplay: false`

### 后端文件
1. `xm-xzp-impl/src/main/java/com/xm/xzp/service/impl/TbZjjgAcctInfoServiceImpl.java`
   - 修改 `updateAcctInfo` 方法，添加脱敏数据检测
   - 修改 `updateAcctInfoByOthMsg2Tx` 方法，添加脱敏数据检测

## 测试验证

### 测试场景
1. **正常编辑流程**
   - 查看原始数据 → 编辑 → 保存
   - 验证：敏感字段保持原值

2. **脱敏数据编辑流程**
   - 查看脱敏数据 → 编辑 → 保存
   - 验证：敏感字段自动恢复为原值

3. **混合数据编辑流程**
   - 部分字段脱敏，部分字段正常 → 编辑 → 保存
   - 验证：脱敏字段恢复，正常字段正常更新

### 验证要点
- [ ] 敏感字段在编辑表单中不显示
- [ ] 编辑时自动获取原始数据
- [ ] 后端检测并阻止脱敏数据保存
- [ ] 更新操作使用正确的查询条件
- [ ] 日志记录保护操作

## 日志监控

### 关键日志
1. **前端日志**
   - "已获取原始数据进行编辑"
   - "获取原始数据失败，使用当前数据编辑"

2. **后端日志**
   - "检测到脱敏后的公司账户数据，保持原值不变"
   - "检测到脱敏后的账户名数据，保持原值不变"

### 监控指标
- 脱敏数据检测次数
- 数据恢复操作次数
- 编辑操作成功率

## 注意事项

1. **兼容性**：所有修改都保持向后兼容，支持旧数据
2. **性能**：编辑时额外的API调用可能略微影响性能
3. **用户体验**：用户可能注意到编辑时的数据刷新
4. **安全性**：多层保护确保敏感数据不会被意外修改

## 未来优化建议

1. **缓存机制**：缓存原始数据，减少API调用
2. **字段级保护**：更精细的字段级别保护配置
3. **审计日志**：记录所有敏感数据访问和修改操作
4. **权限控制**：基于角色的敏感数据访问控制
