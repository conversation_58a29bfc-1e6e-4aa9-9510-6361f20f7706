<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <groupId>com.xm.xzp</groupId>
    <artifactId>xm-xzp</artifactId>
    <version>1.1.2</version>


    <name>xm-xzp</name>
    <packaging>pom</packaging>
    <description>厦门新中平</description>

    <!-- 模块 -->
    <modules>
        <module>xm-xzp-api</module>
        <module>xm-xzp-boot</module>
        <module>xm-xzp-impl</module>
    </modules>

    <!--  配置属性值 -->
    <properties>
        <!-- 通用设置 -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <java.version>1.8</java.version>

        <!-- 第三方包版本 -->
        <pfpj-dependencies.version>2.5.2</pfpj-dependencies.version>
        <pfpj-sdk.version>*******</pfpj-sdk.version>
        <mybatis-plus-boot-starter.version>3.3.0</mybatis-plus-boot-starter.version>
        <mybatis-plus-annotation.version>3.3.0</mybatis-plus-annotation.version>
        <mybatis-plus-generator.version>3.3.0</mybatis-plus-generator.version>
        <pagehelper-spring-boot-starter.version>1.3.0</pagehelper-spring-boot-starter.version>
        <hutool-all.version>5.7.15</hutool-all.version>
        <easypoi.version>4.2.0</easypoi.version>
        <nimbus-jose-jwt.version>8.22</nimbus-jose-jwt.version>
        <UserAgentUtils.version>1.21</UserAgentUtils.version>

        <!-- 安全风险包升级版本 -->
        <!-- [修改组件安全问题5.3.9.RELEASE-5.3.10.RELEASE] -->
        <spring-security-oauth2-client.version>5.3.10.RELEASE</spring-security-oauth2-client.version>
        <!-- [修改组件安全问题5.3.9.RELEASE-5.3.10.RELEASE] -->
        <spring-security.version>5.3.10.RELEASE</spring-security.version>
        <!-- [修改组件安全问题2.3-2.4.7] -->
        <json-smart.version>2.4.7</json-smart.version>
        <!-- [修改组件安全问题1.53-1.67] -->
        <bcprov-jdk15on.version>1.67</bcprov-jdk15on.version>
        <!-- [修改组件安全问题1.19-1.21] -->
        <commons-compress.version>1.21</commons-compress.version>
        <!-- [修改组件安全问题3.0.3-3.0.3.jbossorg-3] -->
        <jakarta.el.version>3.0.3.jbossorg-3</jakarta.el.version>
        <!-- [修改组件安全问题30.0-jre-30.1.1-android] -->
        <guava.version>30.1.1-android</guava.version>
        <!-- [修改组件安全问题4.1.51.Final-4.1.68.Final] -->
        <netty.version>4.1.68.Final</netty.version>
        <!-- [修改组件安全问题2.10.9.2-2.10.6] -->
        <ehcache.version>2.10.6</ehcache.version>

        <!-- 【补充版本号】 -->
        <project.version>1.1.2</project.version>
    </properties>

    <!-- 项目声明依赖管理 -->
    <dependencyManagement>
        <dependencies>
            <!-- **************************************第三方包依赖管理区******************************************* -->
            <!-- Spring Security依赖包管理 -->
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-bom</artifactId>
                <version>${spring-security.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- Java开发平台依赖包管理 -->
            <dependency>
                <groupId>com.psbc</groupId>
                <artifactId>pfpj-dependencies</artifactId>
                <version>${pfpj-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- [修改组件安全问题]oauth2客户端  -->
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-oauth2-client</artifactId>
                <version>${spring-security-oauth2-client.version}</version>
            </dependency>
            <!-- mybatis-plus依赖包 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-annotation</artifactId>
                <version>${mybatis-plus-annotation.version}</version>
            </dependency>
            <!-- mybatis-plus代码生成器 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis-plus-generator.version}</version>
            </dependency>
            <!-- pagehelper分页组件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper-spring-boot-starter.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-jdbc</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mybatis</artifactId>
                        <groupId>org.mybatis</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- hutool工具包 -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool-all.version}</version>
            </dependency>
            <!-- easypoi导入导出工具包 -->
            <dependency>
                <groupId>cn.afterturn</groupId>
                <artifactId>easypoi-base</artifactId>
                <version>${easypoi.version}</version>
            </dependency>
            <!-- 设备类型验证工具类 -->
            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>${UserAgentUtils.version}</version>
            </dependency>
            <!-- [公共-用户信息获取模块解析jwt] jwt工具包 -->
            <dependency>
                <groupId>com.nimbusds</groupId>
                <artifactId>nimbus-jose-jwt</artifactId>
                <version>${nimbus-jose-jwt.version}</version>
            </dependency>
            <!-- 统一认证SDK -->
            <dependency>
                <groupId>com.psbc</groupId>
                <artifactId>pfpj-sdk-unified-auth</artifactId>
                <version>${pfpj-sdk.version}</version>
            </dependency>

            <!-- **************************************自身项目包依赖管理区****************************************** -->
            <!-- 厦门新中平-功能Api模块 -->
            <dependency>
                <groupId>com.xm.xzp</groupId>
                <artifactId>xm-xzp-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- 厦门新中平-功能实现模块 -->
            <dependency>
                <groupId>com.xm.xzp</groupId>
                <artifactId>xm-xzp-impl</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- 审计日志-核心模块 -->
            <dependency>
                <groupId>com.psbc.pfpj</groupId>
                <artifactId>yoaf-auditlog-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- 审计日志-自动装载模块 -->
            <dependency>
                <groupId>com.psbc.pfpj</groupId>
                <artifactId>yoaf-auditlog-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- 认证授权-核心模块 -->
            <dependency>
                <groupId>com.psbc.pfpj</groupId>
                <artifactId>yoaf-auth-center-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- 认证授权-自动装载模块 -->
            <dependency>
                <groupId>com.psbc.pfpj</groupId>
                <artifactId>yoaf-auth-center-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- 认证授权-集成Ldap认证自动装载模块 -->
            <dependency>
                <groupId>com.psbc.pfpj</groupId>
                <artifactId>yoaf-auth-center-starter-ldap</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- 认证授权-集成统一认证自动装载模块 -->
            <dependency>
                <groupId>com.psbc.pfpj</groupId>
                <artifactId>yoaf-auth-center-starter-uaas</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- 公共-缓存模块 -->
            <dependency>
                <groupId>com.psbc.pfpj</groupId>
                <artifactId>yoaf-common-cache</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- 公共-定制化模块 -->
            <dependency>
                <groupId>com.psbc.pfpj</groupId>
                <artifactId>yoaf-common-custom</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- 公共-hibernate验证模块 -->
            <dependency>
                <groupId>com.psbc.pfpj</groupId>
                <artifactId>yoaf-common-hibernate-validator</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- 公共-mybatis-plus配置模块 -->
            <dependency>
                <groupId>com.psbc.pfpj</groupId>
                <artifactId>yoaf-common-mybatis-plus</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- 公共-统一接口响应模块 -->
            <dependency>
                <groupId>com.psbc.pfpj</groupId>
                <artifactId>yoaf-common-response-result</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- 公共-swagger配置模块 -->
            <dependency>
                <groupId>com.psbc.pfpj</groupId>
                <artifactId>yoaf-common-swagger</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- 公共-数据自动翻译模块 -->
            <dependency>
                <groupId>com.psbc.pfpj</groupId>
                <artifactId>yoaf-common-translate</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- 公共-用户信息获取模块 -->
            <dependency>
                <groupId>com.psbc.pfpj</groupId>
                <artifactId>yoaf-common-user-context</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- **************************************安全风险包升级管理区****************************************** -->
            <!-- [修改组件安全问题] json-smart -->
            <dependency>
                <groupId>net.minidev</groupId>
                <artifactId>json-smart</artifactId>
                <version>${json-smart.version}</version>
            </dependency>
            <!-- [修改组件安全问题]-加密散列算法工具包  -->
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15on</artifactId>
                <version>${bcprov-jdk15on.version}</version>
            </dependency>
            <!-- [修改组件安全问题] -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>${commons-compress.version}</version>
            </dependency>
            <!-- [修改组件安全问题] -->
            <dependency>
                <groupId>org.glassfish</groupId>
                <artifactId>jakarta.el</artifactId>
                <version>${jakarta.el.version}</version>
            </dependency>
            <!-- [修改组件安全问题] -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <!-- [修改组件安全问题] -->
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-buffer</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <!-- [修改组件安全问题] -->
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <!-- [修改组件安全问题] -->
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-common</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <!-- [修改组件安全问题] -->
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-handler</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <!-- [修改组件安全问题] -->
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-resolver</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <!-- [修改组件安全问题] -->
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-transport</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <!-- [修改组件安全问题] -->
            <dependency>
                <groupId>net.sf.ehcache</groupId>
                <artifactId>ehcache</artifactId>
                <version>${ehcache.version}</version>
            </dependency>
            <!-- 多数据源配置 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>3.4.0</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!-- 公共依赖 -->
    <dependencies>
        <!-- javaBean简化开发工具 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20210307</version>
        </dependency>
        <!--xml转json的依赖-->
        <dependency>
            <groupId>net.sf.json-lib</groupId>
            <artifactId>json-lib</artifactId>
            <version>2.4</version>
            <classifier>jdk15</classifier>
        </dependency>

    </dependencies>



</project>