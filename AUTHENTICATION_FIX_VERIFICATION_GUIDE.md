# 🧪 认证异常修复验证指南

## 📋 修复概述

### 问题
前端请求存款通知转发服务时出现 `TokenAccessException: 访问令牌异常` 和 `401 : [no body]` 错误。

### 解决方案
在 `BusinessQueryServiceImpl.java` 中添加认证头传递逻辑，确保 RestTemplate 转发请求时携带 Authorization 认证信息。

### 提交信息
- **提交ID**: `0ab8713`
- **分支**: `dev`
- **状态**: 已推送到远程仓库

## 🔧 部署步骤

### 1. 后端服务部署

#### 编译项目
```bash
cd /path/to/xm-xzp-*******
mvn clean package -DskipTests
```

#### 备份当前版本
```bash
# 备份现有jar文件
cp target/xm-xzp-impl.jar target/xm-xzp-impl.jar.backup.$(date +%Y%m%d_%H%M%S)
```

#### 重启服务
```bash
# 停止现有服务
pkill -f xm-xzp-impl

# 启动新版本
nohup java -jar target/xm-xzp-impl.jar > logs/application.log 2>&1 &
```

### 2. 验证服务启动
```bash
# 检查服务状态
ps aux | grep xm-xzp-impl

# 检查端口监听
netstat -tlnp | grep :9091

# 查看启动日志
tail -f logs/application.log
```

## 🧪 功能验证

### 1. 存款通知功能测试

#### 测试步骤
1. **访问前端页面**
   - 打开浏览器访问存款通知查询页面
   - URL: `http://localhost:8080/depositnotice`

2. **输入测试数据**
   ```
   缴费号码: ****************
   用户号: 12345612345678901234567890
   委托单位代码: ************
   业务代码: 101064
   子业务号: 123456
   ```

3. **执行查询**
   - 点击"查询"按钮
   - 观察是否出现认证错误

#### 预期结果
- ✅ **无401错误**: 不再出现认证异常
- ✅ **正常响应**: 目标业务系统正常处理请求
- ✅ **结果显示**: 前端正常显示查询结果或业务响应

### 2. 换卡通知功能测试

#### 测试步骤
1. **访问前端页面**
   - 打开换卡通知查询页面
   - URL: `http://localhost:8080/cardchange`

2. **输入测试数据**
   ```
   用户号: USER123456
   旧卡号: ****************
   新卡号: 6225889876543210
   委托单位代码: 123456789012
   业务代码: ABC001
   ```

3. **执行查询**
   - 点击"查询"按钮
   - 观察响应结果

#### 预期结果
- ✅ **认证通过**: 无认证相关错误
- ✅ **请求转发**: 成功转发到目标业务系统
- ✅ **响应正常**: 返回业务处理结果

## 📊 日志验证

### 1. 认证头传递日志
```bash
# 查看认证头传递日志
tail -f logs/application.log | grep "传递认证头"

# 预期输出示例
2025-08-05 19:30:15.123 DEBUG [nio-9091-exec-1] c.x.x.s.impl.BusinessQueryServiceImpl : 传递认证头: Bearer eyJ0eXAiOiJKV1...
```

### 2. 转发成功日志
```bash
# 查看转发成功日志
tail -f logs/application.log | grep "转发成功"

# 预期输出示例
2025-08-05 19:30:15.456 INFO [nio-9091-exec-1] c.x.x.s.impl.BusinessQueryServiceImpl : 存款通知转发成功，响应状态: 200 OK
```

### 3. 错误日志检查
```bash
# 检查是否还有401错误
tail -f logs/application.log | grep "401"

# 检查认证异常
tail -f logs/application.log | grep "TokenAccessException"

# 预期结果: 应该没有新的401错误和认证异常
```

## 🔍 网络验证

### 1. 抓包验证（可选）
```bash
# 使用tcpdump抓包验证请求头
sudo tcpdump -i lo -A -s 0 'port 9091 and host localhost'

# 在请求中应该能看到Authorization头
```

### 2. curl测试
```bash
# 模拟带认证的请求
curl -X POST http://localhost:9091/api/admin/xzp/spf/20004 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "busikind": "DEPOSIT",
    "tradecode": "Card",
    "merchid": "************",
    "opecd": "101064",
    "account": "****************",
    "targetUrl": "http://localhost:9091/api/ssdsf/XMWEB_SSDSF_9600",
    "action": "deposit_notice"
  }'
```

## ⚠️ 故障排查

### 1. 如果仍有401错误

#### 检查点
- [ ] 服务是否重启成功
- [ ] 新代码是否部署到位
- [ ] 前端Token是否有效
- [ ] 目标URL是否正确

#### 排查命令
```bash
# 检查服务版本
curl http://localhost:9091/actuator/info

# 检查当前代码版本
git log --oneline -1

# 检查认证配置
grep -r "Authorization" xm-xzp-impl/src/main/java/
```

### 2. 如果获取不到认证信息

#### 可能原因
- RequestContextHolder未正确获取请求上下文
- 前端请求未携带Authorization头
- Spring Security配置问题

#### 调试方法
```bash
# 增加调试日志级别
echo "logging.level.com.xm.xzp.service.impl.BusinessQueryServiceImpl=DEBUG" >> application.properties

# 重启服务并查看详细日志
tail -f logs/application.log | grep BusinessQueryServiceImpl
```

## 📈 性能监控

### 1. 响应时间监控
```bash
# 监控转发请求的响应时间
tail -f logs/application.log | grep "转发成功" | while read line; do
  echo "$(date): $line"
done
```

### 2. 错误率监控
```bash
# 统计最近1小时的错误率
grep "$(date '+%Y-%m-%d %H')" logs/application.log | grep -c "转发失败"
grep "$(date '+%Y-%m-%d %H')" logs/application.log | grep -c "转发成功"
```

## ✅ 验证清单

### 部署验证
- [ ] 后端服务成功重启
- [ ] 新代码版本已部署
- [ ] 服务端口正常监听
- [ ] 启动日志无异常

### 功能验证
- [ ] 存款通知查询功能正常
- [ ] 换卡通知查询功能正常
- [ ] 无401认证错误
- [ ] 前端正常显示结果

### 日志验证
- [ ] 认证头传递日志正常
- [ ] 转发成功日志出现
- [ ] 无新的认证异常
- [ ] 目标系统响应正常

### 性能验证
- [ ] 响应时间在可接受范围
- [ ] 无明显性能下降
- [ ] 错误率保持在低水平

## 🎯 成功标准

### 主要指标
1. **零401错误**: 不再出现认证相关的401错误
2. **正常转发**: 请求能够成功转发到目标业务系统
3. **用户体验**: 前端用户能够正常使用查询功能
4. **日志清晰**: 日志中能够看到认证头传递的记录

### 次要指标
1. **性能稳定**: 修复后性能无明显下降
2. **兼容性**: 不影响其他现有功能
3. **可维护性**: 代码清晰，便于后续维护

---

**总结**: 通过添加认证头传递逻辑，成功解决了业务转发服务的认证异常问题。按照本验证指南进行测试，确保修复效果符合预期。
