# 修改验证测试清单

## 功能测试清单

### 1. 新增功能测试
- [ ] 新增记录时是否自动生成 `oth_msg2_tx` UUID
- [ ] UUID 格式是否正确（xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx）
- [ ] 新增的记录是否能正常保存到数据库

### 2. 修改功能测试
- [ ] 有 `oth_msg2_tx` 的记录是否使用新接口 `updateAcctInfoByOthMsg2Tx`
- [ ] 没有 `oth_msg2_tx` 的旧记录是否使用原接口 `updateAcctInfo`
- [ ] 修改操作是否能正常完成

### 3. 删除功能测试
- [ ] 有 `oth_msg2_tx` 的记录是否使用新接口 `deleteAcctInfoByOthMsg2Tx`
- [ ] 没有 `oth_msg2_tx` 的旧记录是否使用原接口 `deleteAcctInfo`
- [ ] 单行删除功能是否正常
- [ ] 批量删除功能是否正常

### 4. 查看详情功能测试
- [ ] 有 `oth_msg2_tx` 的记录是否使用新接口 `getAcctInfoByOthMsg2Tx`
- [ ] 新接口调用失败时是否正确回退到使用当前行数据
- [ ] 没有 `oth_msg2_tx` 的旧记录是否直接使用当前行数据

### 5. 敏感信息显隐功能测试
- [ ] 有 `oth_msg2_tx` 的记录是否能正常切换敏感信息显示状态
- [ ] 没有 `oth_msg2_tx` 的记录是否显示错误提示
- [ ] 敏感信息切换是否使用正确的参数调用接口

## 后端接口验证

### 需要实现的新接口
1. `GET /api/admin/xzp/getAcctInfoByOthMsg2Tx/{othMsg2Tx}`
   - 根据 oth_msg2_tx 获取账户详情

2. `POST /api/admin/xzp/updateAcctInfoByOthMsg2Tx`
   - 根据 oth_msg2_tx 更新账户信息
   - 请求体包含完整的账户信息

3. `POST /api/admin/xzp/deleteAcctInfoByOthMsg2Tx`
   - 根据 oth_msg2_tx 删除账户信息
   - 参数：othMsg2Tx

4. `GET /api/admin/xzp/acctSensitiveByBizKey/{othMsg2Tx}/{sensitiveStatus}`
   - 根据 oth_msg2_tx 获取敏感信息

## 数据库验证

### 字段检查
- [ ] 数据库表是否已添加 `oth_msg2_tx` 字段
- [ ] 字段类型是否为 VARCHAR(36) 或类似
- [ ] 是否设置了唯一索引
- [ ] 是否允许为空（新增时必填，旧数据可为空）

### 数据迁移
- [ ] 现有数据是否需要生成 `oth_msg2_tx` 值
- [ ] 迁移脚本是否已准备
- [ ] 迁移后数据完整性检查

## 兼容性测试

### 旧数据兼容性
- [ ] 没有 `oth_msg2_tx` 的旧记录是否能正常查看
- [ ] 没有 `oth_msg2_tx` 的旧记录是否能正常修改
- [ ] 没有 `oth_msg2_tx` 的旧记录是否能正常删除

### 混合数据场景
- [ ] 同时存在有和没有 `oth_msg2_tx` 的记录时功能是否正常
- [ ] 批量操作时是否能正确处理混合数据

## 错误处理测试

### 接口异常处理
- [ ] 新接口调用失败时是否有正确的错误提示
- [ ] 是否有合适的回退机制
- [ ] 网络异常时是否有适当的处理

### 数据异常处理
- [ ] `oth_msg2_tx` 为空时的处理
- [ ] `oth_msg2_tx` 格式错误时的处理
- [ ] 重复的 `oth_msg2_tx` 值的处理

## 性能测试

### 查询性能
- [ ] 基于 `oth_msg2_tx` 的查询性能是否满足要求
- [ ] 是否需要添加数据库索引优化

### 批量操作性能
- [ ] 批量删除大量数据时的性能表现
- [ ] 批量操作时的内存使用情况

## 用户体验测试

### 界面交互
- [ ] 新增、修改、删除操作的用户体验是否一致
- [ ] 错误提示是否友好明确
- [ ] 操作响应时间是否合理

### 数据展示
- [ ] `oth_msg2_tx` 字段是否在详情页面正确显示
- [ ] 字段标签和格式是否合适
