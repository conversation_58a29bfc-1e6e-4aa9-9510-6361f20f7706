<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xm.xzp.mapper.XzpOpLogMapper">
    <!-- 根据id 查询日志信息-->
    <select id="selectInfoById" parameterType="java.lang.String" resultType="com.xm.xzp.model.entity.XzpOpLog">
         select
            id
            ,create_by
            ,create_time
            ,update_by
            ,update_time
            ,sys_org_code
            ,apply_type
            ,apply_id
            ,flow_id
            ,step_id
            ,step_name
            ,deal_user
            ,deal_org
            ,deal_res
            ,opinion
            ,start_time
            ,end_time
            ,spe_rele_id
            ,reserve_field1
            ,reserve_field2
            ,reserve_field3
            ,reserve_field4
            ,reserve_field5
        from xzp_op_log
        where id = #{id}
    </select>

    <!-- 分页查询日志表 -->
    <select id="selectExportLogs" resultType="com.xm.xzp.model.entity.XzpOpLog">
        select
            id
            ,create_by
            ,create_time
            ,update_by
            ,update_time
            ,sys_org_code
            ,apply_type
            ,apply_id
            ,flow_id
            ,step_id
            ,step_name
            ,deal_user
            ,deal_org
            ,deal_res
            ,opinion
            ,start_time
            ,end_time
            ,spe_rele_id
            ,reserve_field1
            ,reserve_field2
            ,reserve_field3
            ,reserve_field4
            ,reserve_field5
        from xzp_op_log
        where 1=1
        <if test="stepId != null and stepId !=''">
            and step_id like concat('%', #{stepId}, '%')
        </if>
        <if test="stepName != null and stepName !=''">
            and step_name like concat('%', #{stepName}, '%')
        </if>
        <if test="createTimeStart != null and createTimeStart !=''">
            and create_time &gt; #{createTimeStart}
            and create_time &lt; #{createTimeEnd}
        </if>

        order by create_time desc
    </select>

    <insert id="insertLog" parameterType="com.xm.xzp.model.entity.XzpOpLog">
        insert into xzp_op_log (
             create_by
            ,create_time
            ,update_by
            ,update_time
            ,sys_org_code
            ,apply_type
            ,apply_id
            ,flow_id
            ,step_id
            ,step_name
            ,deal_user
            ,deal_org
            ,deal_res
            ,opinion
            ,start_time
            ,end_time
            ,spe_rele_id
            ,reserve_field1
            ,reserve_field2
            ,reserve_field3
            ,reserve_field4
            ,reserve_field5
            )
        values (
             #{createBy}
            ,to_char(current_timestamp,'yyyy-MM-dd HH:mi:ss')
            ,#{updateBy}
            ,#{updateTime}
            ,#{sysOrgCode}
            ,#{applyType}
            ,#{applyId}
            ,#{flowId}
            ,#{stepId}
            ,#{stepName}
            ,#{dealUser}
            ,#{dealOrg}
            ,#{dealRes}
            ,#{opinion}
            ,#{startTime}
            ,#{endTime}
            ,#{speReleId}
            ,#{reserveField1}
            ,#{reserveField2}
            ,#{reserveField3}
            ,#{reserveField4}
            ,#{reserveField5}
        )
    </insert>

</mapper>
