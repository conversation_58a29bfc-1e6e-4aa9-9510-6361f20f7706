<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xm.xzp.mapper.RecSettMismatchMapper">
    <select id="recSettMismatchList"  resultType="com.xm.xzp.model.entity.RecSettMismatch">
        select
            record_type,
            tran_dt,
            merch_id,
            ope_cd,
            type_cd,
            user_id,
            record_sta,
            unitd_id,
            tran_cd,
            tran_nm,
            merch_dt,
            host_dt,
            item_id,
            trace_id,
            tran_inst_id,
            tran_sq,
            acc_card_id,
            tran_amt,
            misc_tx,
            oth_msg1,
            oth_msg2
        from
            tb_3502_xmykt_dz_result
        where 1=1
        <if test="recordType != null and recordType !=''">
            and record_type = #{recordType}
        </if>
        <if test="merchDt != null and merchDt != ''">
            and merch_dt = #{merchDt}
        </if>
        <if test="recordSta != null and recordSta != ''">
            and record_sta = #{recordSta}
        </if>
        order by tran_dt desc
    </select>

    <update id="updateRecSettMismatch" parameterType="com.xm.xzp.model.entity.RecSettMismatch">
        update tb_3502_xmykt_dz_result
        <set>
            <if test="recordSta != null">record_sta = #{recordSta},</if>
        </set>
        where tran_dt = #{tranDt}
        and ope_cd = #{opeCd}
        and tran_cd = #{tranCd}
        and merch_id = #{merchId}
        and user_id = #{userId}
        and tran_sq = #{tranSq}
    </update>
</mapper>