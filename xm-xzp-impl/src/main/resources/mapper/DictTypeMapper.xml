<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xm.xzp.mapper.DictTypeMapper">
    <resultMap id="BaseResultUserMap" type="com.xm.xzp.model.entity.DictType">
        <result column="dict_type_id" jdbcType="VARCHAR" property="dictTypeId"/>
        <result column="dict_type_name" jdbcType="VARCHAR" property="dictTypeName"/>
        <result column="data_source" jdbcType="VARCHAR" property="dataSource"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="dict_type_desc" jdbcType="VARCHAR" property="dictTypeDesc"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user" jdbcType="TIMESTAMP" property="updateUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <!--  分页查询字典类型列表  -->
    <select id="selectDictType" resultMap="BaseResultUserMap">
        select
               dict_type_id, dict_type_name, data_source, status, dict_type_desc,
               create_user,create_time,create_user,update_user,update_time
        from bc_pmctl_dict_type
        <where>
            <if test="query.dictTypeId != null and query.dictTypeId !=''">
                and dict_type_id = #{query.dictTypeId,jdbcType=VARCHAR}
            </if>
            <if test="query.dictTypeName != null and query.dictTypeName !=''">
                and dict_type_name like concat('%', #{query.dictTypeName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="query.status != null and query.status !=''">
                and status = #{query.status,jdbcType=VARCHAR}
            </if>
            <if test="query.dataSource != null and query.dataSource !=''">
                and data_source like concat('%', #{query.dataSource,jdbcType=VARCHAR}, '%')
            </if>
        </where>
        order by create_time desc
    </select>

    <!-- 查询到导出的字典信息 -->
    <select id="selectExportDict" resultType="com.xm.xzp.model.vo.ExcelDictVO">
      select
        bpdt.dict_type_id, bpdt.dict_type_name, bpdt.data_source, bpdt.status as dict_type_status,
        bpdt.dict_type_desc, bpde.dict_id, bpde.dict_name, bpde.status as dict_status,
        bpde.display_order, bpde.create_user,bpdt.create_time, bpde.update_user, bpde.update_time
      from bc_pmctl_dict_type bpdt
      left join bc_pmctl_dict_entry bpde
      on bpdt.dict_type_id = bpde.dict_type_id
      <where>
          <if test="query.dictTypeId != null and query.dictTypeId !=''">
              bpdt.dict_type_id like concat('%', #{query.dictTypeId,jdbcType=VARCHAR}, '%')
          </if>
          <if test="query.dictTypeName != null and query.dictTypeName !=''">
            and bpdt.dict_type_name like concat('%', #{query.dictTypeName,jdbcType=VARCHAR}, '%')
          </if>
          <if test="query.status != null and query.status !=''">
            and bpdt.status = #{query.status,jdbcType=VARCHAR}
          </if>
          <if test="query.dataSource != null and query.dataSource !=''">
            and bpdt.data_source like concat('%', #{query.dataSource,jdbcType=VARCHAR}, '%')
          </if>
          <if test="query.dictTypeIds != null and query.dictTypeIds.size() > 0">
              and bpdt.dict_type_id in
              <foreach collection="query.dictTypeIds" item="dictTypeId" open="(" close=")" separator="," index="index">
                  #{dictTypeId, jdbcType=VARCHAR}
              </foreach>
          </if>
      </where>
      order by bpdt.create_time
    </select>

</mapper>
