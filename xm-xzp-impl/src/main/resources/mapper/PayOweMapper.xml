<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xm.xzp.mapper.PayOweMapper">

    <update id="updateTxnStaToZero" parameterType="map">
        UPDATE tb_pay_owe
        SET txn_sta = '0'
        WHERE txn_sta = '1'
        <if test="opeCd != null and opeCd != ''">
            AND ope_cd = #{opeCd}
        </if>
        <if test="merchId != null and merchId != ''">
            AND merch_id = #{merchId}
        </if>
        <if test="wlBatchId != null and wlBatchId != ''">
            AND wl_batch_id = #{wlBatchId}
        </if>
    </update>

    <select id="selectPayOweList" resultType="com.xm.xzp.model.entity.PayOwe">
        SELECT
        p.*,
        m.prdt_nm as merch_nm,
        o.ope_nm
        FROM
        tb_pay_owe p
        LEFT JOIN tb_merch_ope m ON p.merch_id = m.merch_id
        AND p.ope_cd = m.ope_cd
        LEFT JOIN tb_ope_cd o ON p.ope_cd = o.ope_cd
        <where>
            <if test="query.startTime != null and query.startTime != ''
                      and query.endTime != null and query.endTime != ''">
                AND p.tran_dt BETWEEN #{query.startTime} AND #{query.endTime}
            </if>
            <if test="query.merchId != null and query.merchId != ''">
                AND p.merch_id = #{query.merchId}
            </if>
            <if test="query.opeCd != null and query.opeCd != ''">
                AND p.ope_cd = #{query.opeCd}
            </if>
        </where>
        ORDER BY p.tran_dt DESC
    </select>
</mapper>