package com.xm.xzp.enums;

/**
 * <p>
 * 脱敏状态枚举
 * </p>
 *
 * @version 1.0.0
 * <AUTHOR>
 * @date 2022-03-02 18:11
 */
public enum SensitiveStatusEnum {

    /**
     * 脱敏状态
     */
    SENSITIVE_STATUS("1", "脱敏"),

    /**
     * 未脱敏状态
     */
    UN_SENSITIVE_STATUS("2", "不脱敏");

    /**
     * 角色状态码
     */
    private String statusCode;

    /**
     * 角色状态名称
     */
    private String statusName;

    SensitiveStatusEnum(String statusCode, String statusName) {
        this.statusCode = statusCode;
        this.statusName = statusName;
    }

    public String statusCode() {
        return statusCode;
    }

    public String statusName() {
        return statusName;
    }


}
