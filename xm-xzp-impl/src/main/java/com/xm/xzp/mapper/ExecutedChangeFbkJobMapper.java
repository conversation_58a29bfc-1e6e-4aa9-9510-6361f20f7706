package com.xm.xzp.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xm.xzp.model.entity.JgzhData;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @version 1.0
 * @description: 定时反馈 Mapper 接口
 */
@Mapper
public interface ExecutedChangeFbkJobMapper extends BaseMapper<JgzhData> {

    /**
     * 查询新一代综管oracle数据库里的机构数据并处理成本框架机构表数据
     * @return List<Inst>
     */
    List<JgzhData> queryList();
}
