package com.xm.xzp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xm.xzp.model.entity.PayOwe;
import com.xm.xzp.model.entity.PayOweVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface PayOweMapper  extends BaseMapper<PayOwe> {
    int updateTxnStaToZero(@Param("opeCd") String opeCd, @Param("merchId") String merchId, @Param("wlBatchId") String wlBatchId);
    List<PayOweVo> selectPayOweList(@Param("query") PayOweVo payOweVo);
}