package com.xm.xzp.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xm.xzp.model.entity.XzpData;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description:
 * @Author: YDH
 * @Date:   2024-02-04
 * @Version: V1.0
 */
@Component
@DS("datasource1")
public interface XzpDataMapper extends BaseMapper<XzpData> {
    List<XzpData> queryYxjfLsxdyeList(XzpData xzpData);
}
