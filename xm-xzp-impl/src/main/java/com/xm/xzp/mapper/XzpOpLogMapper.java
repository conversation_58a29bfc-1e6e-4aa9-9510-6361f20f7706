package com.xm.xzp.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xm.xzp.model.entity.XzpOpLog;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description:
 * @Author: Lyl
 * @Date:   2024-11-04
 * @Version: V1.0
 */
@Component
public interface XzpOpLogMapper extends BaseMapper<XzpOpLog> {

    List<XzpOpLog> queryList(XzpOpLog xzpOpLog);

    XzpOpLog selectInfoById(String id);

    boolean insertLog(XzpOpLog xzpOpLog);
}
