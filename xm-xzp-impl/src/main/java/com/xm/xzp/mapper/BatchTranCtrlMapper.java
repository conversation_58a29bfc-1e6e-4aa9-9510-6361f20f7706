package com.xm.xzp.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xm.xzp.model.entity.BatchTranCtrl;
import com.xm.xzp.model.vo.BatchTranCtrlVo;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface BatchTranCtrlMapper extends BaseMapper<BatchTranCtrl> {
    List<BatchTranCtrl> batchTranCtrlList(BatchTranCtrlVo batchTranCtrl);
    int updateBatchTranCtrl(BatchTranCtrl batchTranCtrl);
}