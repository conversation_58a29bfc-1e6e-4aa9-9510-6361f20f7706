package com.xm.xzp.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xm.xzp.model.entity.MerchOpe;
import com.xm.xzp.model.entity.OpeCd;
import com.xm.xzp.model.vo.MerchOpeGroupVo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface MerchOpeMapper extends BaseMapper<MerchOpe> {
    List<MerchOpeGroupVo> selectGroupByMerchId(MerchOpeGroupVo query);
    List<MerchOpeGroupVo> selectGroupByOpeCd(OpeCd query);
}