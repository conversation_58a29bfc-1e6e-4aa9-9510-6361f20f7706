package com.xm.xzp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xm.xzp.model.entity.DictEntry;
import com.xm.xzp.model.query.DictEntryQuery;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface DictEntryMapper extends BaseMapper<DictEntry> {

    /**
     * 查询字典实体列表
     *
     * @param dictEntryQuery 查询对象
     * @return List<DictEntry>
     */
    List<DictEntry> selectDictEntry(DictEntryQuery dictEntryQuery);
}
