package com.xm.xzp.controller;

import com.github.pagehelper.PageInfo;
import com.psbc.pfpj.yoaf.response.RestResponse;
import com.psbc.pfpj.yoaf.response.util.Constants;
import com.psbc.pfpj.yoaf.response.util.ResponseUtils;
import com.xm.xzp.api.RecSettMismatchApi;
import com.xm.xzp.aspect.PMCTLLog;
import com.xm.xzp.model.entity.RecSettMismatch;
import com.xm.xzp.model.vo.RecSettMismatchVo;
import com.xm.xzp.service.IRecSettMismatchService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Slf4j
@RestController
@Component
public class RecSettMismatchController implements RecSettMismatchApi {

    @Resource
    private IRecSettMismatchService recSettMismatchService;
    @Override
    @PMCTLLog(name = "查询对账/清算不一致列表", action = "查询")
    public RestResponse<PageInfo<RecSettMismatch>> recSettMismatchList(RecSettMismatchVo recSettMismatchVo, Integer pageNum, Integer pageSize) {
        PageInfo<RecSettMismatch> pageList = recSettMismatchService.recSettMismatchList(
                recSettMismatchVo, pageNum, pageSize);
        return RestResponse.success(pageList);
    }

    @Override
    @PMCTLLog(name = "编辑对账/清算不一致信息", action = "修改")
    public RestResponse<String> recSettMismatchEdit(@RequestBody RecSettMismatch recSettMismatch) {
        log.debug("修改批量交易执行参数：{}", recSettMismatch.toString());
        boolean flag = recSettMismatchService.editRecSettMismatch(recSettMismatch);
        if (flag) {
            return RestResponse.success(Constants.DEFAULT_SUCCESS_EDIT_MESSAGE);
        } else {
            return RestResponse.fail(Constants.DEFAULT_FAILED_EDIT_MESSAGE);
        }
    }

    @ApiOperation(value = "导出对账/清算不一致信息", notes = "exportRecSettMismatch")
    @PostMapping(value = "/exportRecSettMismatch")
    //@PMCTLLog(name = "导出对账/清算不一致信息",action = "导出")
    public void exportRecSettMismatch(@RequestBody RecSettMismatchVo recSettMismatchVo, HttpServletResponse response) throws IOException {
        Workbook workbook = recSettMismatchService.exportRecSettMismatch(recSettMismatchVo);
        ResponseUtils.responseExcel(response, "对账清算不一致信息列表.xls", workbook);
    }
}
