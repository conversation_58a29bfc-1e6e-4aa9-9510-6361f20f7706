package com.xm.xzp.controller;


import com.github.pagehelper.PageInfo;
import com.psbc.pfpj.yoaf.response.RestResponse;
import com.xm.xzp.api.JgzhShApi;
import com.xm.xzp.aspect.PMCTLLog;
import com.xm.xzp.model.entity.JgzhSpData;
import com.xm.xzp.service.IJgzhShDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 监管账户变动情况反馈
 * @date 2024/12/12 10:54
 */
@Slf4j
@RestController
@Component
public class JgzhShController implements JgzhShApi {

    @Resource
    private IJgzhShDataService jgzhShDataService;

    @PMCTLLog(name = "查询监管账户变动情况反馈审核列表信息",action = "查询")
    @Override
    public RestResponse<PageInfo<JgzhSpData>> jgzhSpList(JgzhSpData jgzhSpData, Integer pageNum, Integer pageSize) {
        PageInfo<JgzhSpData> pageList = jgzhShDataService.querySpPage(jgzhSpData, pageNum, pageSize);
        return RestResponse.success(pageList);
    }

    @PMCTLLog(name = "查看监管账户变动情况反馈审核详情",action = "查询")
    @Override
    public RestResponse<JgzhSpData> jgzhSpDetail(String id) {
        JgzhSpData jgzhSpData = jgzhShDataService.getJgzhSpDetail(id);
        return RestResponse.success(jgzhSpData);
    }

    @PMCTLLog(name = "监管账户变动情况反馈审核",action = "审核")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResponse<JgzhSpData> spJgzh(JgzhSpData jgzhSpData) {
        log.info("审核结果--"+jgzhSpData.getSpRes()+","+jgzhSpData.getSpContent());
        boolean flag = jgzhShDataService.spJgzhInfo(jgzhSpData);
        if (flag) {
            return RestResponse.success("审核成功");
        } else {
            return RestResponse.fail("审核失败");
        }
    }

    @PMCTLLog(name = "监管账户变动情况反馈批量审核",action = "审核")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResponse<JgzhSpData> batchSpJgzh(JgzhSpData jgzhSpData) {
        log.info("批量修改审核结果--"+ jgzhSpData.getSpRes()+","+jgzhSpData.getSpContent());
        int a = jgzhShDataService.batchUpdateSpRes(jgzhSpData);
        log.info("批量审核条数--"+a);
        return RestResponse.success("批量审核完成");
    }

    @Override
    @PMCTLLog(name = "获取监管账户审核列表脱敏信息",action = "查询")
    public RestResponse<JgzhSpData> getJgzhSensitiveInfo(String id, String sensitiveStatus) {
        JgzhSpData em = jgzhShDataService.getJgzhSpSensitiveInfo(id, sensitiveStatus);
        return RestResponse.success(em);
    }


}

