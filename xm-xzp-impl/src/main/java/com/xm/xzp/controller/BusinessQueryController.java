package com.xm.xzp.controller;

import com.psbc.pfpj.yoaf.response.RestResponse;
import com.xm.xzp.api.BusinessQueryApi;
import com.xm.xzp.aspect.PMCTLLog;
import com.xm.xzp.model.vo.BusinessQueryVo;
import com.xm.xzp.model.vo.CardChangeNoticeVo;
import com.xm.xzp.model.vo.DepositNoticeVo;
import com.xm.xzp.service.IBusinessQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 业务查询控制器
 * <AUTHOR>
 */
@Slf4j
@RestController
public class BusinessQueryController implements BusinessQueryApi {

    @Resource
    private IBusinessQueryService businessQueryService;

    @Override
    @PMCTLLog(name = "业务查询转发", action = "转发")
    public RestResponse<Object> businessQuery(BusinessQueryVo businessQueryVo) {
        log.info("接收到业务查询请求，账户: {}, 目标URL: {}", businessQueryVo.getAccount(), businessQueryVo.getTargetUrl());
        
        try {
            // 参数验证
            if (businessQueryVo.getTargetUrl() == null || businessQueryVo.getTargetUrl().trim().isEmpty()) {
                log.warn("目标URL为空，无法执行转发");
                return RestResponse.fail("目标URL不能为空");
            }
            
            // 执行业务查询转发
            Object result = businessQueryService.executeBusinessQuery(businessQueryVo);
            
            log.info("业务查询转发完成，账户: {}", businessQueryVo.getAccount());
            return RestResponse.success(result);
            
        } catch (Exception e) {
            log.error("业务查询转发异常，账户: {}, 错误信息: {}", businessQueryVo.getAccount(), e.getMessage(), e);
            return RestResponse.fail("业务查询转发失败: " + e.getMessage());
        }
    }

    @Override
    @PMCTLLog(name = "换卡通知转发", action = "转发")
    public RestResponse<Object> cardChangeNotice(CardChangeNoticeVo cardChangeNoticeVo) {
        log.info("接收到换卡通知请求，用户号: {}, 旧卡号: {}, 新卡号: {}, 目标URL: {}",
                cardChangeNoticeVo.getPayid(), cardChangeNoticeVo.getStr31(),
                cardChangeNoticeVo.getAccount(), cardChangeNoticeVo.getTargetUrl());

        try {
            // 必填字段校验
            if (cardChangeNoticeVo.getTargetUrl() == null || cardChangeNoticeVo.getTargetUrl().trim().isEmpty()) {
                log.warn("目标URL为空，无法执行转发");
                return RestResponse.fail("目标URL不能为空");
            }

            if (cardChangeNoticeVo.getPayid() == null || cardChangeNoticeVo.getPayid().trim().isEmpty()) {
                log.warn("用户号为空");
                return RestResponse.fail("用户号不能为空");
            }

            if (cardChangeNoticeVo.getStr31() == null || cardChangeNoticeVo.getStr31().trim().isEmpty()) {
                log.warn("旧卡号为空");
                return RestResponse.fail("旧卡号不能为空");
            }

            if (cardChangeNoticeVo.getAccount() == null || cardChangeNoticeVo.getAccount().trim().isEmpty()) {
                log.warn("新卡号为空");
                return RestResponse.fail("新卡号不能为空");
            }

            if (cardChangeNoticeVo.getMerchid() == null || cardChangeNoticeVo.getMerchid().trim().isEmpty()) {
                log.warn("委托单位代码为空");
                return RestResponse.fail("委托单位代码不能为空");
            }

            if (cardChangeNoticeVo.getOpecd() == null || cardChangeNoticeVo.getOpecd().trim().isEmpty()) {
                log.warn("业务代码为空");
                return RestResponse.fail("业务代码不能为空");
            }

            if (cardChangeNoticeVo.getOrgcode() == null || cardChangeNoticeVo.getOrgcode().trim().isEmpty()) {
                log.warn("机构代码为空");
                return RestResponse.fail("机构代码不能为空");
            }

            // 执行换卡通知转发
            Object result = businessQueryService.executeCardChangeNotice(cardChangeNoticeVo);

            log.info("换卡通知转发完成，用户号: {}, 旧卡号: {}, 新卡号: {}",
                    cardChangeNoticeVo.getPayid(), cardChangeNoticeVo.getStr31(), cardChangeNoticeVo.getAccount());
            // 通过调用 String.valueOf() 确保返回的是一个字符串，以避免潜在的 HttpMessageConverter 问题
            return RestResponse.success(String.valueOf(result));

        } catch (Exception e) {
            log.error("换卡通知转发异常，用户号: {}, 旧卡号: {}, 新卡号: {}, 错误信息: {}",
                    cardChangeNoticeVo.getPayid(), cardChangeNoticeVo.getStr31(),
                    cardChangeNoticeVo.getAccount(), e.getMessage(), e);
            return RestResponse.fail("换卡通知转发失败: " + e.getMessage());
        }
    }

    @Override
    @PMCTLLog(name = "存款通知转发", action = "转发")
    public RestResponse<Object> depositNotice(DepositNoticeVo depositNoticeVo) {
        log.info("接收到存款通知请求，缴费号码: {}, 用户号: {}, 目标URL: {}",
                depositNoticeVo.getAccount(), depositNoticeVo.getPay_id(), depositNoticeVo.getTargetUrl());

        try {
            // 必填字段校验
            if (depositNoticeVo.getTargetUrl() == null || depositNoticeVo.getTargetUrl().trim().isEmpty()) {
                log.warn("目标URL为空，无法执行转发");
                return RestResponse.fail("目标URL不能为空");
            }

            if (depositNoticeVo.getAccount() == null || depositNoticeVo.getAccount().trim().isEmpty()) {
                log.warn("缴费号码为空");
                return RestResponse.fail("缴费号码不能为空");
            }

            if (depositNoticeVo.getPay_id() == null || depositNoticeVo.getPay_id().trim().isEmpty()) {
                log.warn("用户号为空");
                return RestResponse.fail("用户号不能为空");
            }

            if (depositNoticeVo.getMerchid() == null || depositNoticeVo.getMerchid().trim().isEmpty()) {
                log.warn("委托单位代码为空");
                return RestResponse.fail("委托单位代码不能为空");
            }

            if (depositNoticeVo.getOpecd() == null || depositNoticeVo.getOpecd().trim().isEmpty()) {
                log.warn("业务代码为空");
                return RestResponse.fail("业务代码不能为空");
            }

            if (depositNoticeVo.getOrgcode() == null || depositNoticeVo.getOrgcode().trim().isEmpty()) {
                log.warn("机构代码为空");
                return RestResponse.fail("机构代码不能为空");
            }

            // 执行存款通知转发
            Object result = businessQueryService.executeDepositNotice(depositNoticeVo);

            log.info("存款通知转发完成，缴费号码: {}, 用户号: {}", depositNoticeVo.getAccount(), depositNoticeVo.getPay_id());
            // 通过调用 String.valueOf() 确保返回的是一个字符串，以避免潜在的 HttpMessageConverter 问题
            return RestResponse.success(String.valueOf(result));

        } catch (Exception e) {
            log.error("存款通知转发异常，缴费号码: {}, 用户号: {}, 错误信息: {}",
                    depositNoticeVo.getAccount(), depositNoticeVo.getPay_id(), e.getMessage(), e);
            return RestResponse.fail("存款通知转发失败: " + e.getMessage());
        }
    }
}
