package com.xm.xzp.controller;

import com.github.pagehelper.PageInfo;
import com.psbc.pfpj.yoaf.response.RestResponse;
import com.xm.xzp.api.PayOweApi;
import com.xm.xzp.aspect.PMCTLLog;
import com.xm.xzp.model.entity.PayOwe;
import com.xm.xzp.model.entity.PayOweVo;
import com.xm.xzp.model.vo.PayOweUpdateVo;
import com.xm.xzp.service.IPayOweService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
public class PayOweController implements PayOweApi {

    @Resource
    private IPayOweService payOweService;

    @Override
    public RestResponse<Integer> batchUpdateTxnStaToZero(PayOweUpdateVo payOweUpdateVo) {
        log.debug("批量更新交易状态，参数：{}", payOweUpdateVo);
        int updatedCount = payOweService.batchUpdateTxnStaToZero(
                payOweUpdateVo.getOpeCd(),
                payOweUpdateVo.getMerchId(),
                payOweUpdateVo.getWlBatchId()
        );
        return RestResponse.success(updatedCount);
    }

    @Override
    @PMCTLLog(name = "查询欠费记录列表", action = "query")
    public RestResponse<PageInfo<PayOwe>> payOweList(PayOweVo payOweVo,
                                                     Integer pageNum, Integer pageSize) {
        PageInfo<PayOwe> pageList = payOweService.payOweList(payOweVo, pageNum, pageSize);
        return RestResponse.success(pageList);
    }
}