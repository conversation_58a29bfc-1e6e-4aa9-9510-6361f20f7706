package com.xm.xzp.controller;

import com.github.pagehelper.PageInfo;
import com.psbc.pfpj.yoaf.response.RestResponse;
import com.xm.xzp.api.AcctInfoApi;
import com.xm.xzp.aspect.PMCTLLog;
import com.xm.xzp.model.entity.TbZjjgAcctInfo;
import com.xm.xzp.model.vo.AcctInfoQueryVo;
import com.xm.xzp.service.ITbZjjgAcctInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 账户信息查询控制器
 * <AUTHOR>
 */
@Slf4j
@RestController
@Component
public class AcctInfoController implements AcctInfoApi {

    @Resource
    private ITbZjjgAcctInfoService tbZjjgAcctInfoService;

    @Override
    public RestResponse<List<TbZjjgAcctInfo>> suggestAcctInfo(String keyword) {
        log.debug("账户信息联想查询，参数：{}", keyword);
        List<TbZjjgAcctInfo> result = tbZjjgAcctInfoService.suggestAcctInfo(keyword);
        log.debug("账户信息联想查询结果数量：{}", result.size());
        return RestResponse.success(result);
    }

    @Override
    @PMCTLLog(name = "账户信息分页查询", action = "查询")
    public RestResponse<PageInfo<TbZjjgAcctInfo>> queryAcctInfoList(AcctInfoQueryVo queryVo, Integer pageNum, Integer pageSize) {
        log.debug("账户信息分页查询，查询条件：{}，页码：{}，每页数量：{}", queryVo, pageNum, pageSize);
        PageInfo<TbZjjgAcctInfo> pageInfo = tbZjjgAcctInfoService.queryAcctInfoPage(queryVo, pageNum, pageSize);
        log.debug("账户信息分页查询结果，总数：{}，当前页数据量：{}", pageInfo.getTotal(), pageInfo.getList().size());
        return RestResponse.success(pageInfo);
    }

    @Override
    @PMCTLLog(name = "新增账户信息", action = "新增")
    public RestResponse<String> addAcctInfo(TbZjjgAcctInfo acctInfo) {
        log.debug("新增账户信息，参数：{}", acctInfo);

        // 检查必填字段
        if (acctInfo.getCpabAccId() == null || acctInfo.getCpabAccId().trim().isEmpty()) {
            return RestResponse.fail("公司账户不能为空");
        }
        if (acctInfo.getAcctNm() == null || acctInfo.getAcctNm().trim().isEmpty()) {
            return RestResponse.fail("户名不能为空");
        }
        if (acctInfo.getOpeCd() == null || acctInfo.getOpeCd().trim().isEmpty()) {
            return RestResponse.fail("业务代码不能为空");
        }
        if (acctInfo.getMerchId() == null || acctInfo.getMerchId().trim().isEmpty()) {
            return RestResponse.fail("委托单位代码不能为空");
        }

        boolean result = tbZjjgAcctInfoService.addAcctInfo(acctInfo);
        if (result) {
            log.info("新增账户信息成功，账户：{}，户名：{}", acctInfo.getCpabAccId(), acctInfo.getAcctNm());
            return RestResponse.success("新增账户信息成功");
        } else {
            log.error("新增账户信息失败，账户：{}，户名：{}", acctInfo.getCpabAccId(), acctInfo.getAcctNm());
            return RestResponse.fail("新增账户信息失败，可能是账户信息已存在");
        }
    }

    @Override
    @PMCTLLog(name = "修改账户信息", action = "修改")
    public RestResponse<String> updateAcctInfo(TbZjjgAcctInfo acctInfo) {
        log.debug("修改账户信息，参数：{}", acctInfo);

        // 检查必填字段
        if (acctInfo.getCpabAccId() == null || acctInfo.getCpabAccId().trim().isEmpty()) {
            return RestResponse.fail("公司账户不能为空");
        }
        if (acctInfo.getAcctNm() == null || acctInfo.getAcctNm().trim().isEmpty()) {
            return RestResponse.fail("户名不能为空");
        }
        if (acctInfo.getOpeCd() == null || acctInfo.getOpeCd().trim().isEmpty()) {
            return RestResponse.fail("业务代码不能为空");
        }
        if (acctInfo.getMerchId() == null || acctInfo.getMerchId().trim().isEmpty()) {
            return RestResponse.fail("委托单位代码不能为空");
        }

        boolean result = tbZjjgAcctInfoService.updateAcctInfo(acctInfo);
        if (result) {
            log.info("修改账户信息成功，账户：{}，户名：{}", acctInfo.getCpabAccId(), acctInfo.getAcctNm());
            return RestResponse.success("修改账户信息成功");
        } else {
            log.error("修改账户信息失败，账户：{}，户名：{}", acctInfo.getCpabAccId(), acctInfo.getAcctNm());
            return RestResponse.fail("修改账户信息失败，可能是记录不存在");
        }
    }

    @Override
    @PMCTLLog(name = "删除账户信息", action = "删除")
    public RestResponse<String> deleteAcctInfo(String cpabAccId, String acctNm) {
        log.debug("删除账户信息，公司账户：{}，户名：{}", cpabAccId, acctNm);

        // 检查必填字段
        if (cpabAccId == null || cpabAccId.trim().isEmpty()) {
            return RestResponse.fail("公司账户不能为空");
        }
        if (acctNm == null || acctNm.trim().isEmpty()) {
            return RestResponse.fail("户名不能为空");
        }

        boolean result = tbZjjgAcctInfoService.deleteAcctInfo(cpabAccId, acctNm);
        if (result) {
            log.info("删除账户信息成功，公司账户：{}，户名：{}", cpabAccId, acctNm);
            return RestResponse.success("删除账户信息成功");
        } else {
            log.error("删除账户信息失败，公司账户：{}，户名：{}", cpabAccId, acctNm);
            return RestResponse.fail("删除账户信息失败，可能是记录不存在");
        }
    }

    @Override
    public RestResponse<TbZjjgAcctInfo> getAcctSensitiveInfo(String cpabAccId, String acctNm, String sensitiveStatus) {
        log.debug("获取账户敏感信息，公司账户：{}，户名：{}，敏感状态：{}", cpabAccId, acctNm, sensitiveStatus);

        TbZjjgAcctInfo result = tbZjjgAcctInfoService.getAcctSensitiveInfo(cpabAccId, acctNm, sensitiveStatus);
        if (result != null) {
            log.info("获取账户敏感信息成功，公司账户：{}，户名：{}", cpabAccId, acctNm);
            return RestResponse.success(result);
        } else {
            log.error("获取账户敏感信息失败，公司账户：{}，户名：{}", cpabAccId, acctNm);
            return RestResponse.fail("获取账户敏感信息失败，可能是记录不存在");
        }
    }

    @Override
    public RestResponse<TbZjjgAcctInfo> getAcctSensitiveInfoByBizKey(String othMsg2Tx, String sensitiveStatus) {
        log.debug("根据唯一标识获取账户敏感信息，唯一标识：{}，敏感状态：{}", othMsg2Tx, sensitiveStatus);

        TbZjjgAcctInfo result = tbZjjgAcctInfoService.getAcctSensitiveInfoByOthMsg2Tx(othMsg2Tx, sensitiveStatus);
        if (result != null) {
            log.info("根据唯一标识获取账户敏感信息成功，唯一标识：{}", othMsg2Tx);
            return RestResponse.success(result);
        } else {
            log.error("根据唯一标识获取账户敏感信息失败，唯一标识：{}", othMsg2Tx);
            return RestResponse.fail("获取账户敏感信息失败，可能是记录不存在");
        }
    }

    @Override
    public RestResponse<TbZjjgAcctInfo> getAcctInfoByOthMsg2Tx(String othMsg2Tx) {
        log.debug("根据唯一标识获取账户详情，唯一标识：{}", othMsg2Tx);

        TbZjjgAcctInfo result = tbZjjgAcctInfoService.getAcctInfoByOthMsg2Tx(othMsg2Tx);
        if (result != null) {
            log.info("根据唯一标识获取账户详情成功，唯一标识：{}", othMsg2Tx);
            return RestResponse.success(result);
        } else {
            log.error("根据唯一标识获取账户详情失败，唯一标识：{}", othMsg2Tx);
            return RestResponse.fail("获取账户详情失败，可能是记录不存在");
        }
    }

    @Override
    @PMCTLLog(name = "根据唯一标识修改账户信息", action = "修改")
    public RestResponse<String> updateAcctInfoByOthMsg2Tx(TbZjjgAcctInfo acctInfo) {
        log.debug("根据唯一标识修改账户信息，参数：{}", acctInfo);

        // 检查必填字段
        if (acctInfo.getOthMsg2Tx() == null || acctInfo.getOthMsg2Tx().trim().isEmpty()) {
            return RestResponse.fail("唯一标识不能为空");
        }
        if (acctInfo.getCpabAccId() == null || acctInfo.getCpabAccId().trim().isEmpty()) {
            return RestResponse.fail("公司账户不能为空");
        }
        if (acctInfo.getAcctNm() == null || acctInfo.getAcctNm().trim().isEmpty()) {
            return RestResponse.fail("户名不能为空");
        }
        if (acctInfo.getOpeCd() == null || acctInfo.getOpeCd().trim().isEmpty()) {
            return RestResponse.fail("业务代码不能为空");
        }
        if (acctInfo.getMerchId() == null || acctInfo.getMerchId().trim().isEmpty()) {
            return RestResponse.fail("委托单位代码不能为空");
        }

        boolean result = tbZjjgAcctInfoService.updateAcctInfoByOthMsg2Tx(acctInfo);
        if (result) {
            log.info("根据唯一标识修改账户信息成功，唯一标识：{}", acctInfo.getOthMsg2Tx());
            return RestResponse.success("修改成功");
        } else {
            log.error("根据唯一标识修改账户信息失败，唯一标识：{}", acctInfo.getOthMsg2Tx());
            return RestResponse.fail("修改失败，可能是记录不存在或已被删除");
        }
    }

    @Override
    @PMCTLLog(name = "根据唯一标识删除账户信息", action = "删除")
    public RestResponse<String> deleteAcctInfoByOthMsg2Tx(String othMsg2Tx) {
        log.debug("根据唯一标识删除账户信息，唯一标识：{}", othMsg2Tx);

        if (othMsg2Tx == null || othMsg2Tx.trim().isEmpty()) {
            return RestResponse.fail("唯一标识不能为空");
        }

        boolean result = tbZjjgAcctInfoService.deleteAcctInfoByOthMsg2Tx(othMsg2Tx);
        if (result) {
            log.info("根据唯一标识删除账户信息成功，唯一标识：{}", othMsg2Tx);
            return RestResponse.success("删除成功");
        } else {
            log.error("根据唯一标识删除账户信息失败，唯一标识：{}", othMsg2Tx);
            return RestResponse.fail("删除失败，可能是记录不存在或已被删除");
        }
    }
}