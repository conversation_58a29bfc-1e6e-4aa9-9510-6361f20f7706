package com.xm.xzp.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.psbc.pfpj.yoaf.response.RestResponse;
import com.psbc.pfpj.yoaf.response.util.Constants;
import com.psbc.pfpj.yoaf.response.util.ResponseUtils;
import com.xm.xzp.api.JgzhApi;
import com.xm.xzp.aspect.PMCTLLog;
import com.xm.xzp.model.entity.JgzhData;
import com.xm.xzp.service.IJgzhDataService;
import com.xm.xzp.service.IJgzhShDataService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 监管账户变动情况反馈
 * @date 2024/10/22 10:54
 */
@Slf4j
@RestController
@Component
public class JgzhController implements JgzhApi {

    @Resource
    private IJgzhDataService jgzhDataService;
    @Resource
    private IJgzhShDataService jgzhShDataService;

    /**
     * 查询监管账户列表信息
     * */
    @PMCTLLog(name = "查询监管账户变动情况反馈列表信息",action = "查询")
    @Override
    public RestResponse<PageInfo<JgzhData>> queryJgzhList(JgzhData jgzhData, Integer pageNum, Integer pageSize) {
        PageInfo<JgzhData> pageList = jgzhDataService.queryPage(jgzhData, pageNum, pageSize);
        return RestResponse.success(pageList);
    }


    /**
     * 查询监管账户详情
     * */
    @PMCTLLog(name = "查询监管账户变动情况反馈详情",action = "查询")
    @Override
    public RestResponse<JgzhData> viewJgzh(String id) {
        JgzhData jgzhData = jgzhDataService.getJgzh(id);
        return RestResponse.success(jgzhData);
    }

    /**
     * 新增监管账户
     * */
    @PMCTLLog(name = "新增监管账户变动情况反馈信息",action = "新增" )
    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResponse<String> addJgzhInfo(JgzhData jgzhData) {
        //判断执行流水是否存在，是的话返回修改
        LambdaQueryWrapper<JgzhData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(JgzhData::getSerialno, jgzhData.getSerialno());
        List<JgzhData> list = jgzhDataService.list(queryWrapper);
        if(list.size()>0){
            log.error("新增监管账户被执行变动情况反馈信息失败，流水号重复！");
            return RestResponse.fail("新增监管账户被执行变动情况反馈信息失败，流水号"+jgzhData.getSerialno()+"重复！");
        }
        jgzhData.setDataSource("1");//1-页面录入
        //log.debug("新增监管账户执行参数：{}", jgzhData.toString());
        boolean flag = jgzhDataService.addJgzh(jgzhData);
        if (flag) {
            return RestResponse.success(Constants.DEFAULT_SUCCESS_SAVE_MESSAGE);
        } else {
            return RestResponse.fail("保存失败！");
        }

    }

    /**
     * 删除
     * */
    @Override
    @PMCTLLog(name = "删除监管账户变动情况反馈信息",action = "删除")
    @Transactional(rollbackFor = Exception.class)
    public RestResponse<String> removeJgzh(String id) {
        boolean flag = jgzhDataService.removeJgzh(id);
        if (flag) {
            return RestResponse.success(Constants.DEFAULT_SUCCESS_DELETE_MESSAGE);
        } else {
            return RestResponse.success(Constants.DEFAULT_FAILED_DELETE_MESSAGE);
        }
    }
    /**
     * 批量删除
     * */
    @Override
    @PMCTLLog(name = "批量删除监管账户变动情况反馈信息",action = "删除")
    @Transactional(rollbackFor = Exception.class)
    public RestResponse<Boolean> removeBatchJgzh(List<String> ids) {
        boolean flag = jgzhDataService.removeBatchJgzh(ids);
        if (flag) {
            return RestResponse.success(Constants.DEFAULT_SUCCESS_DELETE_MESSAGE);
        } else {
            return RestResponse.success(Constants.DEFAULT_FAILED_DELETE_MESSAGE);
        }
    }

    /**
     * 修改监管账户
     * */
    @Override
    @PMCTLLog(name = "修改监管账户变动情况反馈信息",action = "修改")
    @Transactional(rollbackFor = Exception.class)
    public RestResponse<JgzhData> editJgzh(JgzhData jgzhData) {
        log.debug("修改监管账户执行参数：{}", jgzhData.toString());
        boolean flag = jgzhDataService.editJgzh(jgzhData);
        if (flag) {
            return RestResponse.success(Constants.DEFAULT_SUCCESS_EDIT_MESSAGE);
        } else {
            return RestResponse.fail(Constants.DEFAULT_FAILED_EDIT_MESSAGE);
        }
    }

    @Override
    public RestResponse<JgzhData> batchUpdateInfo(JgzhData jgzhData) {
        return null;
    }



    @Override
    @PMCTLLog(name = "全量导入监管账户变动情况反馈信息",action = "导入")
    @Transactional(rollbackFor = Exception.class)
    public RestResponse<String> importJgzh(MultipartFile file) throws Exception {
        log.info("导入监管账户变动情况反馈数据》》》");
        jgzhDataService.importJgzh(file);
        return RestResponse.success(Constants.DEFAULT_SUCCESS_IMPORT_MESSAGE);
    }

    @Override
    @PMCTLLog(name = "增量导入监管账户变动情况反馈信息",action = "导入")
    @Transactional(rollbackFor = Exception.class)
    public RestResponse<String> importJgzhAdd(MultipartFile file) throws Exception {
        log.info("增量导入监管账户变动情况反馈数据》》》");
        jgzhDataService.importJgzhAdd(file);
        return RestResponse.success(Constants.DEFAULT_SUCCESS_IMPORT_MESSAGE);
    }


    @Override
    @PMCTLLog(name = "获取监管账户脱敏信息",action = "查询")
    public RestResponse<JgzhData> getJgzhSensitiveInfo(String id, String sensitiveStatus) {
        JgzhData em = jgzhDataService.getJgzhSensitiveInfo(id, sensitiveStatus);
        return RestResponse.success(em);
    }


    @ApiOperation(value = "下载监管账户变动情况反馈导入模板", notes = "downJgzhTemplate")
    @GetMapping(value = "/template/actions/download", produces = "application/octet-stream")
    @PMCTLLog(name = "下载监管账户变动情况反馈导入模板",action = "下载")
    void downJgzhTemplate(HttpServletResponse response) throws IOException {
        jgzhDataService.downJgzhTemplate(response);
    }

    @ApiOperation(value = "导出监管账户变动情况反馈数据", notes = "exportJgzh", produces = "application/octet-stream")
    @PostMapping(value = "/actions/export", produces = "application/octet-stream")
    @PMCTLLog(name = "导出监管账户变动情况反馈数据",action = "导出")
    public void exportJgzhData(@RequestBody JgzhData jgzhData, HttpServletResponse response) throws IOException {
        Workbook workbook = jgzhDataService.exportJgzh(jgzhData);
        ResponseUtils.responseExcel(response, "监管账户信息列表.xls", workbook);
    }


}

