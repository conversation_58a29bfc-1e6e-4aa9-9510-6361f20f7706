package com.xm.xzp.controller;

import com.psbc.pfpj.yoaf.response.RestResponse;
import com.xm.xzp.api.MerchOpeApi;
import com.xm.xzp.model.entity.OpeCd;
import com.xm.xzp.model.vo.MerchOpeGroupVo;
import com.xm.xzp.service.IMerchOpeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
public class MerchOpeController implements MerchOpeApi {

    @Resource
    private IMerchOpeService merchOpeService;

    @Override
    public RestResponse<List<MerchOpeGroupVo>> listGroupByMerchId(MerchOpeGroupVo merchOpeGroupVo) {
        List<MerchOpeGroupVo> result = merchOpeService.listGroupByMerchId(merchOpeGroupVo);
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<List<MerchOpeGroupVo>> listGroupByOpeCd(OpeCd merchOpeGroupVo) {
        List<MerchOpeGroupVo> result = merchOpeService.listGroupByOpeCd(merchOpeGroupVo);
        return RestResponse.success(result);
    }
}