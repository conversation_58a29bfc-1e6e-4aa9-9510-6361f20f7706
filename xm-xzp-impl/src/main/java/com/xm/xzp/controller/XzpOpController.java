package com.xm.xzp.controller;


import com.github.pagehelper.PageInfo;
import com.psbc.pfpj.yoaf.response.RestResponse;
import com.psbc.pfpj.yoaf.response.util.ResponseUtils;
import com.xm.xzp.api.xzpOpLogApi;
import com.xm.xzp.aspect.PMCTLLog;
import com.xm.xzp.model.entity.XzpOpLog;
import com.xm.xzp.service.IXzpOpLogService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 监管账户变动情况反馈
 * @date 2024/4/22 10:54
 */
@Slf4j
@RestController
@Component
public class XzpOpController implements xzpOpLogApi {

    @Resource
    private IXzpOpLogService xzpOpLogService;



    /**
     * 导出操作日志
     * */
    @ApiOperation(value = "导出日志数据", notes = "exportLogs", produces = "application/octet-stream")
    @PostMapping(value = "/exportLogs", produces = "application/octet-stream")
    @PMCTLLog(name = "导出日志数据",action = "query")
    public void exportLogs(@RequestBody XzpOpLog xzpOpLog, HttpServletResponse response) throws IOException {
        Workbook workbook = xzpOpLogService.exportLogs(xzpOpLog);
        ResponseUtils.responseExcel(response, "日志信息列表.xls", workbook);
    }

    /**
     * 查看中平操作日志列表
     * */
    @Override
    public RestResponse<PageInfo<XzpOpLog>> xzpLogList(XzpOpLog xzpOpLog, Integer pageNum, Integer pageSize) {
        PageInfo<XzpOpLog> pageList = xzpOpLogService.queryLogPage(xzpOpLog, pageNum, pageSize);
        return RestResponse.success(pageList);
    }

    /**
     * 查看中平操作日志详情
     * */
    @Override
    public RestResponse<XzpOpLog> logDetail(String id) {
        XzpOpLog logData = xzpOpLogService.getXzpLog(id);
        return RestResponse.success(logData);
    }

    @PMCTLLog(name = "获取新中平外联操作日志列表脱敏信息",action = "查询")
    @Override
    public RestResponse<XzpOpLog> getLogSensitiveInfo(String id, String sensitiveStatus) {
        XzpOpLog em = xzpOpLogService.getLogSensitiveInfo(id, sensitiveStatus);
        return RestResponse.success(em);
    }
}

