package com.xm.xzp.controller;

import com.github.pagehelper.PageInfo;
import com.psbc.pfpj.yoaf.response.RestResponse;
import com.xm.xzp.api.BatchProcessLogApi;
import com.xm.xzp.aspect.PMCTLLog;
import com.xm.xzp.model.entity.BatchProcessLog;
import com.xm.xzp.model.vo.BatchProcessLogVo;
import com.xm.xzp.service.IBatchProcessLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@Component
public class BatchProcessLogController implements BatchProcessLogApi {

    @Resource
    private IBatchProcessLogService batchProcessLogService;

    @Override
    @PMCTLLog(name = "查询批量处理日志", action = "query")
    public RestResponse<PageInfo<BatchProcessLog>> batchProcessLogList(BatchProcessLogVo batchProcessLog,
                                                                       Integer pageNum, Integer pageSize) {
        PageInfo<BatchProcessLog> pageList = batchProcessLogService.batchProcessLogList(
                batchProcessLog, pageNum, pageSize);
        return RestResponse.success(pageList);
    }
}