package com.xm.xzp.util;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import com.xm.xzp.model.entity.XzpData;
import com.xm.xzp.service.IXzpDataService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 生成抵质押品查询批次号
 * 生成规则 DZYP+YYYYMMDD+001 固定14长度
 * @date 2024/2/26 16:47
 */
@Component
public class CreateQueryNo {

    @Resource
    private IXzpDataService xzpDataService;

/*    public String createQueryNo() {
        String pre = "DZYP-";
        String dataStr = DateUtil.format(DateUtil.date(), "yyyyMMdd") + "-";

        //获取数据库种已存在的批次
        LambdaQueryWrapper<XzpData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.likeRight(XzpData::getQueryNo, pre + dataStr);
        queryWrapper.orderByDesc(XzpData::getQueryNo).last("limit 1");
        List<XzpData> list = xzpDataService.list(queryWrapper);
        //日期部分

        String lastStr = "001";
        if (!list.isEmpty() && !Objects.equals(list.get(0).getQueryNo(), "")) {
            String lastNo = list.get(0).getQueryNo().substring(13);
            String[] lasts = {"000", "00", "0"};
            String i = (Integer.parseInt(lastNo) + 1) + "";
            lastStr = lasts[i.length()] + i;
        }
        return pre + dataStr + lastStr;
    }*/


}
