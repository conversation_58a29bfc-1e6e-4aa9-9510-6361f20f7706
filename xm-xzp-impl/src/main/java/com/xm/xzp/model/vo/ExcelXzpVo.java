package com.xm.xzp.model.vo;


import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 导出数据对象
 * </p>
 *
 * @version 1.0.0
 * <AUTHOR>
 * @date 2021-10-28 13:57
 */
@ExcelTarget("xzp")
@Data
public class ExcelXzpVo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
//    @Excel(name = "主键")
//    private String id;
    @Excel(name = "入库时间")
    private String impTime;
    @Excel(name = "他项类型")
    private String othType;
    @Excel(name = "库存序号")
    private String stockNum;
    @Excel(name = "他项权证号")
    private String othWarrantNum;
    @Excel(name = "他项登记日期")
    private String othRecordDate;
    @Excel(name = "借款人姓名")
    private String borrowName;
    @Excel(name = "借款人身份证")
    private String borrowIdCard;
    @Excel(name = "经办机构")
    private String handleOrg;

    @Excel(name = "经办客户经理")
    private String handleCustManager;
    @Excel(name = "贷款种类")
    private String loanType;
    @Excel(name = "借款合同号")
    private String loanContractNum;
    @Excel(name = "贷款年限")
    private String loanLife;
    @Excel(name = "入账价值")
    private String recordedValue;
    @Excel(name = "封包编号")
    private String packetNum;
    @Excel(name = "交接人")
    private String successor;
    @Excel(name = "购房合同号")
    private String purchaseContractNum;
    @Excel(name = "备注信息")
    private String noteInfo;
    @Excel(name = "补登记标志")
    private String suppRegistMark;
    @Excel(name = "库存状态")
    private String stockStatus;
    @Excel(name = "注销后抵质押品状态")
    private String pledgeStatusAfterlogout;
    @Excel(name = "产权证号")
    private String propertycard;
    @Excel(name = "产权人姓名")
    private String propertyownerName;
    @Excel(name = "产权人身份证号")
    private String propertyownerIdCard;
    @Excel(name = "产权地址")
    private String propertyrightAddr;
    @Excel(name = "产权证本数")
    private String propertycardNum;
    @Excel(name = "产权证填发日期")
    private String propertycardIssueDate;
    @Excel(name = "份数")
    private String undetermined;
//    @Excel(name = "预留字段")
//    private String reserve;
//    @Excel(name = "预留字段")
//    private String reserveA;
//    @Excel(name = "预留字段")
//    private String reserveB;
//    @Excel(name = "预留字段")
//    private String reserveC;
//
//    @Excel(name = "预留字段")
//    private String reserveD;

}
