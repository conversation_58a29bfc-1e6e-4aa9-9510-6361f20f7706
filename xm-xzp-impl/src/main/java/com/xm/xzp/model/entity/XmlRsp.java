package com.xm.xzp.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.poi.ss.formula.functions.T;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: xml请求响应内容
 * @create 2024-11-26 10:43
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuppressWarnings("unchecked")
public class XmlRsp implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 编码 0:错误信息  1: 执行成功
     * head
     */
    private String statecode;
    /**
     * 返回信息
     * head
     */
    private String msg;
    /**
     * 返回 数据 statecode为1时必填
     * 执行流水
     * body
     */
    private String serialno;


    /**
     * 返回 数据 statecode为1时必填
     * 是否成功 0:不成功  1: 成功
     * body
     */
    private String issuccess;

    /**
     * 返回 数据 code为1时必填
     */
    private T data;

    /**
     * 快速返回操作成功响应结果(带响应数据)
     */
   /* public static <E> XmlRsp<E> success(E data) {
        return new XmlRsp<>("1", "操作成功", data);
    }

    public static XmlRsp error(String message) {
        return new XmlRsp("0", message, null);
    }
*/


}
