package com.xm.xzp.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> Ye
 * @description: TODO
 * @create 2024-09-14 10:43
 */
@Data
@TableName("bc_pmctl_audit_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "bc_pmctl_audit_log对象", description = "系统日志表")
public class AuditLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "id主键")
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;


    /**
     * 系统编号
     */
    @ApiModelProperty(value = "系统编号")
    private String systemCode ;


    /**
     * 请求地址
     */
    @ApiModelProperty(value = "请求地址")
    private String requestUri;

    /**
     * 请求方式
     */
    @ApiModelProperty(value = "请求方式")
    private String requestType;
    /**
     * 操作类型 字典:yoaf-request-type
     */
    @ApiModelProperty(value = "操作类型 字典:yoaf-request-type")
    private String action;
    /**
     * 交易名称
     */
    @ApiModelProperty(value = "交易名称")
    private String message;
    /**
     * 执行方法
     */
    @ApiModelProperty(value = "执行方法")
    private String executeMethod;
    /**
     * 内容
     */
    @ApiModelProperty(value = "内容")
    private String content;
    /**
     * 状态  success-成功 failure-失败
     */
    @ApiModelProperty(value = "状态  success-成功 failure-失败")
    private String status;
    /**
     * 操作IP
     */
    @ApiModelProperty(value = "操作IP")
    private String operateIp;

    /**
     * 操作IP
     */
    @ApiModelProperty(value = "操作IP")
    private Date recordTime;

    /**
     * 交易耗时
     */
    @ApiModelProperty(value = "交易耗时")
    private Long proceedTime;

    /**
     * 操作IP
     */
    @ApiModelProperty(value = "总耗时")
    private Long totalTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建者姓名
     */
    @ApiModelProperty(value = "创建者姓名")
    private String createNickname;


}
