package com.xm.xzp.aspect;

import com.psbc.pfpj.yoaf.context.model.entity.ContextUser;
import com.psbc.pfpj.yoaf.context.util.UserInfoContext;
import com.psbc.pfpj.yoaf.response.util.IPUtils;
import com.xm.xzp.mapper.IBcPmctlAuditLogMapper;
import com.xm.xzp.model.entity.AuditLog;
import com.xm.xzp.util.LogConstants;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.net.UnknownHostException;
import java.util.Date;

/**
 * <AUTHOR> Ye
 * @description: 插入 bc_pmctl_audit_log日志
 * @create 2024-09-14 9:27
 */
@Slf4j
@Component
@Aspect
public class LogAspect {

    @Resource
    private IBcPmctlAuditLogMapper bcPmctlAuditLogMapper;

    @Pointcut("@annotation(com.xm.xzp.aspect.PMCTLLog)")
    public void pointCut() {
    }

    @Around("pointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        long time1 = System.currentTimeMillis();

        //原始方法
        Object result = point.proceed();



        long time2 = System.currentTimeMillis();
        //插入日志表
        AuditLog auditLog = new AuditLog();
        auditLog.setProceedTime(time2 - time1);
        getAuditLogInfo(point, result, auditLog, time1);

        return result;
    }


    public void getAuditLogInfo(ProceedingJoinPoint point, Object result, AuditLog auditLog, long startTime) throws UnknownHostException {
        Object[] orgs = point.getArgs();
        String reqParam = "";//请求参数
        String opUser = "";//操作人工号
        String opUserName = "";//操作人姓名
        for(Object o:orgs){
           // log.info("切入方法的参数=========="+o.toString());
            reqParam = o.toString();
        }
        Signature signature = point.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();
        String action = "";
        String message = "";
        if(method!=null){
            PMCTLLog pmctlLog = method.getAnnotation(PMCTLLog.class);
            action = pmctlLog.action();
            message = pmctlLog.name();
            //log.info("切入方法的注解信息:"+pmctlLog.toString());
        }
        // 获取当前登录用户信息
        ContextUser contextUser = null;
        try{
                contextUser = UserInfoContext.currentUser();
                opUser = contextUser.getUserName();
                opUserName = contextUser.getUserNickname();
        }catch(Exception e){
                log.info("无法获取用户登录信息，需要从传入参数获取操作人！"+opUser);
                opUser = "default";
                opUserName = "default";
        }
        //获取request对象
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            auditLog.setRequestUri(request.getRequestURI());
            auditLog.setRequestType(request.getMethod());
            auditLog.setOperateIp(IPUtils.getRealIPAddress(request));
            auditLog.setRecordTime(new Date());
        }
        auditLog.setSystemCode(LogConstants.SYSTEM_CODE_XZP);
        auditLog.setAction(action);
        auditLog.setMessage(message);
        //MethodSignature signature = (MethodSignature) point.getSignature();
        Class<?> targetCls = point.getTarget().getClass();
        auditLog.setExecuteMethod(targetCls.getName() + "." + signature.getName());
      //  log.info("切入点的所在模板对象："+targetCls.getName());
      //  log.info("切入点的方法名："+signature.getName());
        auditLog.setContent("请求参数："+reqParam+",响应结果："+result.toString());
        auditLog.setStatus(LogConstants.ACTION_RES_SUCCESS);
        auditLog.setCreateUser(opUser);
        auditLog.setCreateNickname(opUserName);
        long time3 = System.currentTimeMillis();
        auditLog.setTotalTime(time3 - startTime);
        //log.info("写入日志的参数："+auditLog.toString());
        bcPmctlAuditLogMapper.insert(auditLog);

    }


}
