package com.xm.xzp.autoconfigure;



import com.xm.xzp.config.AdminSecurityConfig;

import com.xm.xzp.controller.XzpController;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <p>
 * 后台管理模块自动配置类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022-06-28 17:35
 */
@Configuration
@ConditionalOnProperty(prefix = "xm.xzp",name = "enabled", havingValue = "true", matchIfMissing = true)
@MapperScan({"com.xm.xzp.mapper"})
@EnableConfigurationProperties(AdminSecurityConfig.class)
@Slf4j
public class AdminAutoConfiguration {

    /**
     * 抵质押品查询表 前端控制器
     *
     * @return xzpController
     */
    @Bean
    @ConditionalOnMissingBean
    public XzpController XzpController() {
        return new XzpController();
    }

}
