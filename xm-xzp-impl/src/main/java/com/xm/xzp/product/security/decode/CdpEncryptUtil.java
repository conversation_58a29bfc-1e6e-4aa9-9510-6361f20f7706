package com.xm.xzp.product.security.decode;


import com.xm.xzp.product.security.exception.EncryptFailureException;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

import sun.misc.BASE64Encoder;

public class CdpEncryptUtil {
    public CdpEncryptUtil() {
    }

    private static byte[] aesEncryptToBytes(String content, String encryptKey) throws Exception {
        Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
        int blockSize = cipher.getBlockSize();
        byte[] dataBytes = content.getBytes("utf-8");
        int plaintextLength = dataBytes.length;
        if (plaintextLength % blockSize != 0) {
            plaintextLength += blockSize - plaintextLength % blockSize;
        }

        byte[] plaintext = new byte[plaintextLength];
        System.arraycopy(dataBytes, 0, plaintext, 0, dataBytes.length);
        SecretKeySpec keySpec = new SecretKeySpec(encryptKey.getBytes(), "AES");
        IvParameterSpec ivSpec = new IvParameterSpec(encryptKey.getBytes());
        cipher.init(1, keySpec, ivSpec);
        return cipher.doFinal(plaintext);
    }

    public static String aesEncrypt(String content, String encryptKey) throws EncryptFailureException {
        try {
            return (new BASE64Encoder()).encode(aesEncryptToBytes(content, encryptKey));
        } catch (Exception var3) {
            throw new EncryptFailureException("加密失败！");
        }
    }
}
