package com.xm.xzp.product.security.sign;


import com.xm.xzp.product.security.exception.SignFailureException;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import sun.misc.BASE64Encoder;

@SuppressWarnings("unchecked")
public class CdpSignUtil {
    public CdpSignUtil() {
    }

    private static byte[] md5(byte[] bytes) throws Exception {
        MessageDigest md = MessageDigest.getInstance("MD5");
        md.update(bytes);
        return md.digest();
    }

    private static byte[] md5(String msg) throws Exception {
        return StringUtils.isBlank(msg) ? null : md5(msg.getBytes());
    }

    private static String md5Encrypt(String msg) throws Exception {
        return StringUtils.isBlank(msg) ? null : (new BASE64Encoder()).encode(md5(msg));
    }

    public static String sign(Map<String, String> param) throws SignFailureException {
        try {
            Set<String> paramKey = param.keySet();
            String[] strs = (String[])(new ArrayList(paramKey)).toArray(new String[paramKey.size()]);
            Arrays.sort(strs);
            StringBuilder sb = new StringBuilder();
            String[] var4 = strs;
            int var5 = strs.length;

            for(int var6 = 0; var6 < var5; ++var6) {
                String str = var4[var6];
                sb.append(str).append("=").append((String)param.get(str)).append("&");
            }

            String result = sb.toString();
            if (StringUtils.isNotBlank(result) && result.endsWith("&")) {
                return md5Encrypt(result.substring(0, result.length() - 1));
            } else {
                throw new SignFailureException("未收到用于签名的参数，签名失败！");
            }
        } catch (Exception var8) {
            throw new SignFailureException("签名失败！");
        }
    }
}
