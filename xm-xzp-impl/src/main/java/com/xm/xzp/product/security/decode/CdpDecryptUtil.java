package com.xm.xzp.product.security.decode;


import com.xm.xzp.product.security.exception.DecryptFailureException;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.lang.StringUtils;
import sun.misc.BASE64Decoder;

public class CdpDecryptUtil {
    public CdpDecryptUtil() {
    }

    private static String aesDecryptByBytes(byte[] encryptBytes, String decryptKey) throws Exception {
        Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
        SecretKeySpec keySpec = new SecretKeySpec(decryptKey.getBytes(), "AES");
        IvParameterSpec ivSpec = new IvParameterSpec(decryptKey.getBytes());
        cipher.init(2, keySpec, ivSpec);
        byte[] decryptBytes = cipher.doFinal(encryptBytes);
        String result = new String(decryptBytes);
        if (StringUtils.isNotBlank(result)) {
            result = result.trim();
        }

        return result;
    }

    public static String aesDecrypt(String encryptStr, String decryptKey) throws DecryptFailureException {
        try {
            return StringUtils.isBlank(encryptStr) ? null : aesDecryptByBytes(base64Decode(encryptStr), decryptKey);
        } catch (Exception var3) {
            throw new DecryptFailureException("解密失败！");
        }
    }

    private static byte[] base64Decode(String base64Code) throws Exception {
        return StringUtils.isBlank(base64Code) ? null : (new BASE64Decoder()).decodeBuffer(base64Code);
    }
}

