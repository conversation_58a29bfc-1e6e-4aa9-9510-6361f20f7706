package com.xm.xzp.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xm.xzp.mapper.SupAcctPayInstMapper;
import com.xm.xzp.model.entity.SupAcctPayInst;
import com.xm.xzp.model.vo.SupAcctPayInstVo;
import com.xm.xzp.service.ISupAcctPayInstService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class SupAcctPayInstServiceImpl extends ServiceImpl<SupAcctPayInstMapper, SupAcctPayInst> implements ISupAcctPayInstService {
    @Resource
    private SupAcctPayInstMapper supAcctPayInstMapper;

    @Override
    @DS("datasource2")
    public PageInfo<SupAcctPayInst> supAcctPayInstList(SupAcctPayInstVo supAcctPayInstVo, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<SupAcctPayInst> list = this.list(supAcctPayInstQueryWrapper(supAcctPayInstVo));
        return new PageInfo<>(list);
    }

    /**
     * 构建查询条件
     *
     * @param supAcctPayInstVo 查询条件对象
     * @return QueryWrapper
     */
    private QueryWrapper<SupAcctPayInst> supAcctPayInstQueryWrapper(SupAcctPayInstVo supAcctPayInstVo) {
        QueryWrapper<SupAcctPayInst> queryWrapper = new QueryWrapper<>();
        if (supAcctPayInstVo != null) {
            LambdaQueryWrapper<SupAcctPayInst> lambdaQueryWrapper = queryWrapper.lambda();
            //业务代码
            if ( StringUtils.isNotBlank(supAcctPayInstVo.getOpeCd()) ) {
                lambdaQueryWrapper.eq(SupAcctPayInst::getOpeCd, supAcctPayInstVo.getOpeCd());
            }
            //委托单位代码
            if (StringUtils.isNotBlank(supAcctPayInstVo.getMerchId())) {
                lambdaQueryWrapper.eq(SupAcctPayInst::getMerchId, supAcctPayInstVo.getMerchId());
            }
            //指令类型
            if (StringUtils.isNotBlank(supAcctPayInstVo.getTransType())) {
                lambdaQueryWrapper.eq(SupAcctPayInst::getTransType, supAcctPayInstVo.getTransType());
            }
            //指令流水号
            if (StringUtils.isNotBlank(supAcctPayInstVo.getTranSq())) {
                lambdaQueryWrapper.eq(SupAcctPayInst::getTranSq, supAcctPayInstVo.getTranSq());
            }
            if ( StringUtils.isNotBlank(supAcctPayInstVo.getStartDt()) && StringUtils.isNotBlank(supAcctPayInstVo.getEndDt())) {
                lambdaQueryWrapper.le(SupAcctPayInst::getTranDt, supAcctPayInstVo.getEndDt());
                lambdaQueryWrapper.ge(SupAcctPayInst::getTranDt, supAcctPayInstVo.getStartDt());
            }

        }
        queryWrapper.orderByDesc("tran_dt");
        return queryWrapper;
    }
}
