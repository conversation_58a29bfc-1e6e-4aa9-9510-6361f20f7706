package com.xm.xzp.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.psbc.pfpj.yoaf.response.exception.BizException;
import com.xm.xzp.mapper.MerchSubOpeMapper;
import com.xm.xzp.model.entity.MerchSubOpe;
import com.xm.xzp.model.vo.MerchSubOpeGroupVo;
import com.xm.xzp.model.vo.MerchSubOpeVo;
import com.xm.xzp.service.IMerchSubOpeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MerchSubOpeServiceImpl extends ServiceImpl<MerchSubOpeMapper, MerchSubOpe>
        implements IMerchSubOpeService {

    @Resource
    private MerchSubOpeMapper merchSubOpeMapper;

    @Override
    @DS("datasource2")
    public PageInfo<MerchSubOpe> merchSubOpeList(MerchSubOpeVo merchSubOpe, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<MerchSubOpe> list = this.list(getMerchSubOpeQueryWrapper(merchSubOpe));
        return new PageInfo<>(list);
    }

    @Override
    @DS("datasource2")
    public boolean saveOrUpdateMerchSubOpe(MerchSubOpe merchSubOpe) {
        if (StringUtils.isBlank(merchSubOpe.getMerchId()) 
            || StringUtils.isBlank(merchSubOpe.getOpeCd())
            || StringUtils.isBlank(merchSubOpe.getSubOpeId())) {
            throw new BizException("必填参数不能为空");
        }

        merchSubOpe.setLastModifyDt(new Date());
        QueryWrapper<MerchSubOpe> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("merch_id", merchSubOpe.getMerchId())
                    .eq("ope_cd", merchSubOpe.getOpeCd())
                    .eq("sub_ope_id", merchSubOpe.getSubOpeId());
        return this.saveOrUpdate(merchSubOpe, queryWrapper);
    }

    @Override
    @DS("datasource2")
    public boolean deleteMerchSubOpe(MerchSubOpe merchSubOpe) {
        if (StringUtils.isBlank(merchSubOpe.getMerchId()) 
            || StringUtils.isBlank(merchSubOpe.getOpeCd())
            || StringUtils.isBlank(merchSubOpe.getSubOpeId())) {
            throw new BizException("必填参数不能为空");
        }

        LambdaQueryWrapper<MerchSubOpe> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MerchSubOpe::getMerchId, merchSubOpe.getMerchId())
                   .eq(MerchSubOpe::getOpeCd, merchSubOpe.getOpeCd())
                   .eq(MerchSubOpe::getSubOpeId, merchSubOpe.getSubOpeId());

        return this.remove(queryWrapper);
    }

    private QueryWrapper<MerchSubOpe> getMerchSubOpeQueryWrapper(MerchSubOpeVo merchSubOpe) {
        QueryWrapper<MerchSubOpe> queryWrapper = new QueryWrapper<>();
        if (merchSubOpe != null) {
            LambdaQueryWrapper<MerchSubOpe> lambdaQueryWrapper = queryWrapper.lambda();
            if (StringUtils.isNotBlank(merchSubOpe.getMerchId())) {
                lambdaQueryWrapper.eq(MerchSubOpe::getMerchId, merchSubOpe.getMerchId());
            }
            if (StringUtils.isNotBlank(merchSubOpe.getOpeCd())) {
                lambdaQueryWrapper.eq(MerchSubOpe::getOpeCd, merchSubOpe.getOpeCd());
            }
            if (StringUtils.isNotBlank(merchSubOpe.getSubOpeId())) {
                lambdaQueryWrapper.eq(MerchSubOpe::getSubOpeId, merchSubOpe.getSubOpeId());
            }
            if(StringUtils.isNotBlank(merchSubOpe.getSubOpeId())){
                lambdaQueryWrapper.eq(MerchSubOpe::getSubOpeId, merchSubOpe.getSubOpeId());
            }
            if (StringUtils.isNotBlank(merchSubOpe.getSubOpeNm())) {
                lambdaQueryWrapper.like(MerchSubOpe::getSubOpeNm, merchSubOpe.getSubOpeNm());
            }
            if (StringUtils.isNotBlank(merchSubOpe.getSpMerchId())) {
                lambdaQueryWrapper.eq(MerchSubOpe::getSpMerchId, merchSubOpe.getSpMerchId());
            }
        }
        queryWrapper.lambda().orderByDesc(MerchSubOpe::getLastModifyDt);
        return queryWrapper;
    }

    @Override
    @DS("datasource2")
    public List<MerchSubOpeGroupVo> listGroupBySubOpeId(MerchSubOpeGroupVo merchSubOpeGroupVo) {
        LambdaQueryWrapper<MerchSubOpe> queryWrapper = new LambdaQueryWrapper<>();

        if (merchSubOpeGroupVo != null) {
            if (StringUtils.isNotBlank(merchSubOpeGroupVo.getSubOpeId())) {
                queryWrapper.likeRight(MerchSubOpe::getSubOpeId, merchSubOpeGroupVo.getSubOpeId());
            }
        }

        queryWrapper.groupBy(MerchSubOpe::getSubOpeId,
                MerchSubOpe::getSubOpeNm);
        queryWrapper.select(
                MerchSubOpe::getSubOpeId,
                MerchSubOpe::getSubOpeNm
        );

        return this.list(queryWrapper).stream()
                .map(item -> {
                    MerchSubOpeGroupVo vo = new MerchSubOpeGroupVo();
                    vo.setSubOpeId(item.getSubOpeId());
                    vo.setSubOpeNm(item.getSubOpeNm());
                    return vo;
                })
                .collect(Collectors.toList());
    }
}