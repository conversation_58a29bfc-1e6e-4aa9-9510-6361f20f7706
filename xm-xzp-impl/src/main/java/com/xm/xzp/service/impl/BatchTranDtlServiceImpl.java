package com.xm.xzp.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xm.xzp.mapper.BatchTranDtlMapper;
import com.xm.xzp.model.entity.BatchTranDtl;
import com.xm.xzp.model.vo.BatchTranDtlDisplayVo;
import com.xm.xzp.model.vo.BatchTranDtlVo;
import com.xm.xzp.service.IBatchTranDtlService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BatchTranDtlServiceImpl extends ServiceImpl<BatchTranDtlMapper, BatchTranDtl> implements IBatchTranDtlService {

    @Resource
    private BatchTranDtlMapper batchTranDtlMapper;

    @Override
    @DS("datasource2")
    public PageInfo<BatchTranDtlDisplayVo> batchTranDtlList(BatchTranDtlVo batchTranDtlVo, Integer pageNum, Integer pageSize) {
        log.debug("查询批量交易明细，参数：{}", batchTranDtlVo);
        PageHelper.startPage(pageNum, pageSize);
        List<BatchTranDtl> list = this.list(getBatchTranDtlQueryWrapper(batchTranDtlVo));

        // 转换为展示VO
        List<BatchTranDtlDisplayVo> displayVoList = list.stream()
                .map(this::convertToDisplayVo)
                .collect(Collectors.toList());

        PageInfo<BatchTranDtlDisplayVo> pageInfo = new PageInfo<>(displayVoList);
        pageInfo.setTotal(new PageInfo<>(list).getTotal());
        return pageInfo;
    }
    /**
     * 转换为展示VO对象
     */
    private BatchTranDtlDisplayVo convertToDisplayVo(BatchTranDtl entity) {
        BatchTranDtlDisplayVo displayVo = new BatchTranDtlDisplayVo();
        BeanUtils.copyProperties(entity, displayVo);
        return displayVo;
    }
    /**
     * 构建查询条件
     */
    private QueryWrapper<BatchTranDtl> getBatchTranDtlQueryWrapper(BatchTranDtlVo batchTranDtlVo) {
        QueryWrapper<BatchTranDtl> queryWrapper = new QueryWrapper<>();
        if (batchTranDtlVo != null) {
            LambdaQueryWrapper<BatchTranDtl> lambdaQueryWrapper = queryWrapper.lambda();

            // 时间区间查询
            if (StringUtils.isNotBlank(batchTranDtlVo.getStartTime())
                    && StringUtils.isNotBlank(batchTranDtlVo.getEndTime())) {
                lambdaQueryWrapper.between(BatchTranDtl::getTranDt,
                        batchTranDtlVo.getStartTime(), batchTranDtlVo.getEndTime());
            }

            // 商户号查询
            if (StringUtils.isNotBlank(batchTranDtlVo.getMerchId())) {
                lambdaQueryWrapper.eq(BatchTranDtl::getMerchId, batchTranDtlVo.getMerchId());
            }

            // 操作码查询
            if (StringUtils.isNotBlank(batchTranDtlVo.getOpeCd())) {
                lambdaQueryWrapper.eq(BatchTranDtl::getOpeCd, batchTranDtlVo.getOpeCd());
            }

            // 批次号查询
            if (StringUtils.isNotBlank(batchTranDtlVo.getBatchId())) {
                lambdaQueryWrapper.eq(BatchTranDtl::getBatchId, batchTranDtlVo.getBatchId());
            }

            // 账卡号查询
            if (StringUtils.isNotBlank(batchTranDtlVo.getAccCardId())) {
                lambdaQueryWrapper.eq(BatchTranDtl::getAccCardId, batchTranDtlVo.getAccCardId());
            }
        }

        // 按交易日期降序排序
        queryWrapper.orderByDesc("tran_dt");
        return queryWrapper;
    }
}