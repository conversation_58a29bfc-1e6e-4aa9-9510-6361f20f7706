package com.xm.xzp.service.impl;

import com.xm.xzp.model.vo.BusinessQueryVo;
import com.xm.xzp.model.vo.CardChangeNoticeVo;
import com.xm.xzp.model.vo.DepositNoticeVo;
import com.xm.xzp.service.IBusinessQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * 业务查询服务实现类
 * <AUTHOR>
 */
@Slf4j
@Service
public class BusinessQueryServiceImpl implements IBusinessQueryService {

    @Autowired
    private RestTemplate restTemplate;

    @Override
    public Object executeBusinessQuery(BusinessQueryVo businessQueryVo) {
        log.info("开始执行业务查询转发，目标URL: {}", businessQueryVo.getTargetUrl());
        
        try {
            // 构建请求参数
            Map<String, Object> requestBody = buildRequestBody(businessQueryVo);
            
            // 设置请求头，包含认证信息
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 获取当前请求的认证信息并传递
            String authorization = getCurrentAuthorization();
            if (authorization != null) {
                headers.set("Authorization", authorization);
                log.debug("传递认证头: {}", authorization.substring(0, Math.min(20, authorization.length())) + "...");
            } else {
                log.warn("未获取到认证信息，可能导致目标服务认证失败");
            }

            // 创建请求实体
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
            
            log.debug("转发请求参数: {}", requestBody);
            
            // 发送POST请求到目标URL
            ResponseEntity<Object> response = restTemplate.exchange(
                    businessQueryVo.getTargetUrl(),
                    HttpMethod.POST,
                    requestEntity,
                    Object.class
            );
            
            log.info("业务查询转发成功，响应状态: {}", response.getStatusCode());
            log.debug("转发响应结果: {}", response.getBody());
            
            return response.getBody();
            
        } catch (Exception e) {
            log.error("业务查询转发失败，目标URL: {}, 错误信息: {}", businessQueryVo.getTargetUrl(), e.getMessage(), e);
            
            // 返回错误响应
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", "500");
            errorResponse.put("message", "业务查询转发失败: " + e.getMessage());
            errorResponse.put("success", false);
            
            return errorResponse;
        }
    }

    @Override
    public Object executeCardChangeNotice(CardChangeNoticeVo cardChangeNoticeVo) {
        log.info("开始执行换卡通知转发，目标URL: {}", cardChangeNoticeVo.getTargetUrl());
        
        try {
            // 构建请求参数
            Map<String, Object> requestBody = buildRequestBody(cardChangeNoticeVo);
            
            // 设置请求头，包含认证信息
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 获取当前请求的认证信息并传递
            String authorization = getCurrentAuthorization();
            if (authorization != null) {
                headers.set("Authorization", authorization);
                log.debug("传递认证头: {}", authorization.substring(0, Math.min(20, authorization.length())) + "...");
            } else {
                log.warn("未获取到认证信息，可能导致目标服务认证失败");
            }

            // 创建请求实体
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
            
            log.debug("转发请求参数: {}", requestBody);
            
            // 发送POST请求到目标URL
            ResponseEntity<String> response = restTemplate.exchange(
                    cardChangeNoticeVo.getTargetUrl(),
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            
            log.info("换卡通知转发成功，响应状态: {}", response.getStatusCode());
            log.debug("转发响应结果: {}", response.getBody());
            
            return response.getBody();
            
        } catch (Exception e) {
            log.error("换卡通知转发失败，目标URL: {}, 错误信息: {}", cardChangeNoticeVo.getTargetUrl(), e.getMessage(), e);
            
            // 返回错误响应
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", "500");
            errorResponse.put("message", "换卡通知转发失败: " + e.getMessage());
            errorResponse.put("success", false);
            
            return errorResponse;
        }
    }

    @Override
    public Object executeDepositNotice(DepositNoticeVo depositNoticeVo) {
        log.info("开始执行存款通知转发，目标URL: {}", depositNoticeVo.getTargetUrl());
        
        try {
            // 构建请求参数
            Map<String, Object> requestBody = buildRequestBody(depositNoticeVo);
            
            // 设置请求头，包含认证信息
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 获取当前请求的认证信息并传递
            String authorization = getCurrentAuthorization();
            if (authorization != null) {
                headers.set("Authorization", authorization);
                log.debug("传递认证头: {}", authorization.substring(0, Math.min(20, authorization.length())) + "...");
            } else {
                log.warn("未获取到认证信息，可能导致目标服务认证失败");
            }

            // 创建请求实体
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
            
            log.debug("转发请求参数: {}", requestBody);
            
            // 发送POST请求到目标URL
            ResponseEntity<String> response = restTemplate.exchange(
                    depositNoticeVo.getTargetUrl(),
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            
            log.info("存款通知转发成功，响应状态: {}", response.getStatusCode());
            log.debug("转发响应结果: {}", response.getBody());
            
            return response.getBody();
            
        } catch (Exception e) {
            log.error("存款通知转发失败，目标URL: {}, 错误信息: {}", depositNoticeVo.getTargetUrl(), e.getMessage(), e);
            
            // 返回错误响应
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", "500");
            errorResponse.put("message", "存款通知转发失败: " + e.getMessage());
            errorResponse.put("success", false);
            
            return errorResponse;
        }
    }

    /**
     * 构建转发请求的请求体（业务查询）
     */
    private Map<String, Object> buildRequestBody(BusinessQueryVo businessQueryVo) {
        Map<String, Object> requestBody = new HashMap<>();
        
        requestBody.put("busikind", businessQueryVo.getBusikind());
        requestBody.put("tradecode", businessQueryVo.getTradecode());
        requestBody.put("merchid", businessQueryVo.getMerchid());
        requestBody.put("opecd", businessQueryVo.getOpecd());
        requestBody.put("empname", businessQueryVo.getEmpname());
        requestBody.put("empcode", businessQueryVo.getEmpcode());
        requestBody.put("orgcode", businessQueryVo.getOrgcode());
        requestBody.put("orgdegree", businessQueryVo.getOrgdegree());
        requestBody.put("account", businessQueryVo.getAccount());
        requestBody.put("bgn_date", businessQueryVo.getBgn_date());
        requestBody.put("end_date", businessQueryVo.getEnd_date());
        requestBody.put("action", businessQueryVo.getAction());
        
        return requestBody;
    }

    /**
     * 构建转发请求的请求体（换卡通知）
     */
    private Map<String, Object> buildRequestBody(CardChangeNoticeVo cardChangeNoticeVo) {
        Map<String, Object> requestBody = new HashMap<>();

        // 构建消息头（head）- 必填对象
        Map<String, Object> head = new HashMap<>();
        head.put("busikind", cardChangeNoticeVo.getBusikind());
        head.put("tradecode", cardChangeNoticeVo.getTradecode());  // 固定为换卡通知业务代码
        head.put("merchid", cardChangeNoticeVo.getMerchid());
        head.put("opecd", cardChangeNoticeVo.getOpecd());
        head.put("empname", cardChangeNoticeVo.getEmpname());
        head.put("empcode", cardChangeNoticeVo.getEmpcode());
        head.put("orgcode", cardChangeNoticeVo.getOrgcode());
        head.put("sendseqno", generateSeqNo());  // 生成发送方流水号

        // 构建消息体（body）- 必填对象
        Map<String, Object> body = new HashMap<>();
        body.put("payid", cardChangeNoticeVo.getPayid());
        body.put("str31", cardChangeNoticeVo.getStr31());
        body.put("account", cardChangeNoticeVo.getAccount());

        requestBody.put("head", head);
        requestBody.put("body", body);

        return requestBody;
    }

    /**
     * 构建转发请求的请求体（存款通知）
     */
    private Map<String, Object> buildRequestBody(DepositNoticeVo depositNoticeVo) {
        Map<String, Object> requestBody = new HashMap<>();

        // 构建消息头（head）- 必填对象
        Map<String, Object> head = new HashMap<>();
        head.put("busikind", depositNoticeVo.getBusikind());
        head.put("tradecode", depositNoticeVo.getTradecode());  // 固定为存款通知业务代码
        head.put("merchid", depositNoticeVo.getMerchid());
        head.put("opecd", depositNoticeVo.getOpecd());
        head.put("sub_ope_cd", depositNoticeVo.getSub_ope_cd());
        head.put("empname", depositNoticeVo.getEmpname());
        head.put("empcode", depositNoticeVo.getEmpcode());
        head.put("orgcode", depositNoticeVo.getOrgcode());
        head.put("orgdegree", depositNoticeVo.getOrgdegree());
        head.put("account", depositNoticeVo.getAccount());
        head.put("pay_id", depositNoticeVo.getPay_id());
        head.put("targetUrl", depositNoticeVo.getTargetUrl());
        head.put("action", depositNoticeVo.getAction());
        head.put("sendseqno", generateSeqNo());  // 生成发送方流水号

        // 构建消息体（body）- 必填对象
        Map<String, Object> body = new HashMap<>();
        body.put("pay_id", depositNoticeVo.getPay_id());
        body.put("account", depositNoticeVo.getAccount());
        body.put("sub_ope_cd", depositNoticeVo.getSub_ope_cd());

        requestBody.put("head", head);
        requestBody.put("body", body);

        return requestBody;
    }

    /**
     * 生成发送方流水号
     * 格式：yyyyMMddHHmmss + 6位随机数
     * @return 20位流水号
     */
    private String generateSeqNo() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))
               + String.format("%06d", new Random().nextInt(1000000));
    }

    /**
     * 获取当前请求的认证信息
     * @return Authorization头信息
     */
    private String getCurrentAuthorization() {
        try {
            // 从当前HTTP请求中获取Authorization头
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
            String authorization = request.getHeader("Authorization");
            if (authorization != null && !authorization.trim().isEmpty()) {
                log.debug("成功获取到认证信息");
                return authorization;
            } else {
                log.warn("当前请求中未找到Authorization头");
                return null;
            }
        } catch (Exception e) {
            log.warn("获取当前请求认证信息失败: {}", e.getMessage());
            return null;
        }
    }
}
