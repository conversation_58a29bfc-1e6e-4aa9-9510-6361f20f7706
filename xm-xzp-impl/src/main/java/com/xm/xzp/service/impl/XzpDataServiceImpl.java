package com.xm.xzp.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xm.xzp.mapper.XzpDataMapper;
import com.xm.xzp.model.entity.XzpData;
import com.xm.xzp.service.IXzpDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR> Ye
 */
@Service
@Slf4j
public class XzpDataServiceImpl extends ServiceImpl<XzpDataMapper, XzpData> implements IXzpDataService {

    @Resource
    private XzpDataMapper xzpDataMapper;
    @Override
    public List<XzpData> queryYxjfLsxdyeList(XzpData xzpData) {
        return xzpDataMapper.queryYxjfLsxdyeList(xzpData);
    }
}
