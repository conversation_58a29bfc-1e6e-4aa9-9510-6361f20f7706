package com.xm.xzp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.xm.xzp.model.entity.BatchTranCtrl;
import com.xm.xzp.model.vo.BatchTranCtrlVo;

public interface IBatchTranCtrlService extends IService<BatchTranCtrl> {

    /**
     * 分页查询批量交易记录
     *
     * @param batchTranCtrl 查询条件
     * @param pageNum       当前页
     * @param pageSize      每页数量
     * @return 分页结果
     */
    PageInfo<BatchTranCtrl> batchTranCtrlList(BatchTranCtrlVo batchTranCtrl, Integer pageNum, Integer pageSize);

    /**
     * 编辑批量交易
     *
     * @param batchTranCtrl 批量交易信息
     * @return 是否成功
     */
    boolean editBatchTranCtrl(BatchTranCtrl batchTranCtrl);
}
