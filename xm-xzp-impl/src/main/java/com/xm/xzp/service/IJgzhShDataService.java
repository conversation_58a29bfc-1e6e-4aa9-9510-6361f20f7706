package com.xm.xzp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.xm.xzp.model.entity.JgzhData;
import com.xm.xzp.model.entity.JgzhSpData;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public interface IJgzhShDataService extends IService<JgzhSpData> {

    /**
     * 分页查询审核数据
     *
     * @param jgzhSpData  页面参数
     * @param pageNum  页码
     * @param pageSize 每页多少条
     * @return IPage<JgzhSpData>
     */
    PageInfo<JgzhSpData> querySpPage(JgzhSpData jgzhSpData, Integer pageNum, Integer pageSize);

    public int addSh(JgzhData jgzhData);

    boolean spJgzhInfo(JgzhSpData jgzhSpData);

    JgzhSpData getJgzhSpDetail(String id);

    int batchUpdateSpRes(JgzhSpData jgzhSpData);

    JgzhSpData getJgzhSpSensitiveInfo(String id, String sensitiveStatus);
}
