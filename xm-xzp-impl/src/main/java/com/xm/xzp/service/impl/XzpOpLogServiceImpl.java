package com.xm.xzp.service.impl;


import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.psbc.pfpj.yoaf.context.util.UserInfoContext;
import com.xm.xzp.enums.SensitiveStatusEnum;
import com.xm.xzp.mapper.XzpOpLogMapper;
import com.xm.xzp.model.entity.JgzhData;
import com.xm.xzp.model.entity.XzpOpLog;
import com.xm.xzp.service.IXzpOpLogService;
import com.xm.xzp.util.AdminConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class XzpOpLogServiceImpl extends ServiceImpl<XzpOpLogMapper, XzpOpLog> implements IXzpOpLogService {

    @Resource
    private XzpOpLogMapper xzpOpLogMapper;


    @Override
    public PageInfo<XzpOpLog> queryLogPage(XzpOpLog xzpOpLog, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<XzpOpLog> list = this.list(getLogQueryWrapper(xzpOpLog));
       // List<XzpOpLog> list = xzpOpLogMapper.queryList(xzpOpLog);
        if (!list.isEmpty()) {
            System.out.println("查询日志条数-->"+list.size());
        }
        // 默认隐藏信息
        for (XzpOpLog em : list) {
            String opuserno = em.getCreateBy();
            if (StringUtils.isNotEmpty(opuserno)) {
                opuserno = opuserno.replaceAll("(?<=.{1}).", "*");
                em.setCreateBy(opuserno);
            }
        }
        return new PageInfo<>(list);
    }

    /**
     * 操作日志查询条件
     * */
    private Wrapper<XzpOpLog> getLogQueryWrapper(XzpOpLog xzpOpLog) {
        LambdaQueryWrapper<XzpOpLog> queryWrapper = new LambdaQueryWrapper<>();

        if (xzpOpLog.getStepId() != null && !xzpOpLog.getStepId().isEmpty()) {
            queryWrapper.like(XzpOpLog::getStepId, xzpOpLog.getStepId());
        }
        if (xzpOpLog.getStepName() != null && !xzpOpLog.getStepName().isEmpty()) {
            queryWrapper.like(XzpOpLog::getStepName, xzpOpLog.getStepName());
        }
        //操作时间范围
        if (xzpOpLog.getCreateTimeEnd() != null) {
//            SimpleDateFormat targetFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
//            String formattedDate = targetFormat.format(xzpOpLog.getCreateTimeEnd());
            log.info("结束时间："+xzpOpLog.getCreateTimeEnd());
            queryWrapper.le(XzpOpLog::getCreateTime, xzpOpLog.getCreateTimeEnd());
        }
        if (xzpOpLog.getCreateTimeStart() != null) {
//            SimpleDateFormat targetFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
//            String formattedDate = targetFormat.format(xzpOpLog.getCreateTimeStart());
            log.info("开始时间："+xzpOpLog.getCreateTimeStart());
            queryWrapper.ge(XzpOpLog::getCreateTime, xzpOpLog.getCreateTimeStart());
        }
        queryWrapper.orderByDesc(XzpOpLog::getCreateTime);
        return queryWrapper;
    }

    @Override
    public int addLog(XzpOpLog xzpOpLog) {
        //log.info("新增日志:[{}]", xzpOpLog);
        try{
//            String dt = new SimpleDateFormat("yyyyMMddHHmmssSSS").format(new Date());
//            String userName = UserInfoContext.currentUserName();
//            xzpOpLog.setCreateTime(dt);//创建时间
//            xzpOpLog.setCreateBy(userName);//创建人
            boolean a = xzpOpLogMapper.insertLog(xzpOpLog);
            if(a){
                log.info("日志新增成功");
            }
        }catch (Exception e){
            log.error("日志新增异常：{}", e.getMessage());
            return 0;
        }
        return 1;
    }

    @Override
    public Workbook exportLogs(XzpOpLog xzpOpLog) {
        TemplateExportParams params = new TemplateExportParams("doc/xzp/中平操作日志导出模板.xls");
        log.info("获取导出数据>>>>>>>>");
        List<XzpOpLog> xzpLogs =  this.list(getLogQueryWrapper(xzpOpLog));
        Map<String, Object> map = new HashMap<>(xzpLogs.size());

        map.put("xzpLogs", xzpLogs);
        //记录日志
//        XzpOpLog xzpOpLog = new XzpOpLog();
//        xzpOpLog.setStepId("导出");
//        xzpOpLog.setStepName("导出监管账户被执行反馈信息");
//        xzpOpLog.setDealRes("成功");
//        xzpOpLog.setOpinion("导出监管账户被执行反馈信息成功");

//        String dt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
//        String userName ="default";//异常情况下工号使用默认值
//        try{
//            userName = UserInfoContext.currentUserName();
//        }catch(Exception e){
//            log.error("[exportLogs]获取用户名异常，"+e);
//        }
//        xzpOpLog.setDealUser(userName);
//        xzpOpLog.setCreateBy(userName);
//        xzpOpLog.setStartTime(dt);
//        xzpOpLog.setCreateTime(dt);
//        xzpOpLog.setApplyType("jgzh");
//        xzpOpLog.setFlowId("xzp_jgzh_info");
//        xzpOpLogService.addLog(xzpOpLog);
        return ExcelExportUtil.exportExcel(params, map);
    }

    @Override
    public XzpOpLog getXzpLog(String id) {
        log.info("查询监管账户被执行信息项目:[id:{}]", id);
        XzpOpLog xzpOpLog = xzpOpLogMapper.selectInfoById(id);
        return xzpOpLog;
    }

    @Override
    public XzpOpLog getLogSensitiveInfo(String id, String sensitiveStatus) {
        log.info("显示新中平外联日志敏感信息:[id:{}, sensitiveStatus:{}]", id, sensitiveStatus);
        // 查询人员信息
        LambdaQueryWrapper<XzpOpLog> queryWrapper = new LambdaQueryWrapper<>();
        // 设置查询字段
        queryWrapper.eq(XzpOpLog::getId, id);
        XzpOpLog em = xzpOpLogMapper.selectOne(queryWrapper);

        // 组装用户返回信息
        XzpOpLog sensitiveInfoVO = new XzpOpLog();
        BeanUtils.copyProperties(em, sensitiveInfoVO);
        // 设置脱敏
        if (SensitiveStatusEnum.UN_SENSITIVE_STATUS.statusCode().equals(sensitiveStatus)) {
            sensitiveInfoVO.setSensitiveStatus(SensitiveStatusEnum.SENSITIVE_STATUS.statusCode());

           /* String opuserno = sensitiveInfoVO.getCreateBy();
            if (StringUtils.isNotEmpty(opuserno)) {
                opuserno = opuserno.replaceAll(AdminConstants.ID_CARD_DES_REGEX, AdminConstants.ID_CARD_DES_SIGN);
                sensitiveInfoVO.setCreateBy(opuserno);
            }
*/

            String opuserno = sensitiveInfoVO.getCreateBy();
            if (StringUtils.isNotEmpty(opuserno)) {
                opuserno = opuserno.replaceAll("(?<=.{1}).", "*");
                sensitiveInfoVO.setCreateBy(opuserno);
            }

        } else {
            // 设置未脱敏
            sensitiveInfoVO.setSensitiveStatus(SensitiveStatusEnum.UN_SENSITIVE_STATUS.statusCode());
        }

        return sensitiveInfoVO;
    }
}
