package com.xm.xzp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.xm.xzp.model.entity.JgzhData;
import com.xm.xzp.model.entity.JgzhSpData;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public interface IJgzhDataService extends IService<JgzhData> {

    /**
     * 分页查询数据
     *
     * @param jgzhData  页面参数
     * @param pageNum  页码
     * @param pageSize 每页多少条
     * @return IPage<DzypData>
     */
    PageInfo<JgzhData> queryPage(JgzhData jgzhData, Integer pageNum, Integer pageSize);

    JgzhData getJgzh(String id);

    boolean addJgzh(JgzhData jgzhData);

    boolean removeJgzh(String id);

    boolean removeBatchJgzh(List<String> ids);

    boolean editJgzh(JgzhData jgzhData);

    void importJgzh(MultipartFile file) throws Exception;

    void importJgzhAdd(MultipartFile file) throws Exception;

    void downJgzhTemplate(HttpServletResponse response) throws IOException;

    Workbook exportJgzh(JgzhData jgzhData);

    JgzhData queryJgzhByParam(JgzhData queryData);

    JgzhData getJgzhSensitiveInfo(String id, String sensitiveStatus);
}
