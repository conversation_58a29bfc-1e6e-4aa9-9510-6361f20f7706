package com.xm.xzp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.xm.xzp.model.entity.PayOwe;
import com.xm.xzp.model.entity.PayOweVo;

public interface IPayOweService extends IService<PayOwe> {
    int batchUpdateTxnStaToZero(String opeCd, String merchId, String wlBatchId);
    PageInfo<PayOwe> payOweList(PayOweVo payOweVo, Integer pageNum, Integer pageSize);
}