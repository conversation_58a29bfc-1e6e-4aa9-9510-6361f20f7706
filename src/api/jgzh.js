import request from '@/utils/request';
import { parseStrEmpty } from "@/utils/youi";

//新中平接口相关

//查询敏感信息
export function querySensitive (id,sensitiveStatus) {
  return request({
    url: `/api/admin/xzp/jgzh/sensitive/get/${id}/${sensitiveStatus}`,
    method: 'get',
  })
}

// 新中平-监管账户信息变动反馈情况查询 - 列表查询;
export function list (data) {
  let url = `/api/admin/xzp/jgzhList?pageSize=${data.pageSize}&pageNum=${data.currentPage}`;
  return request({
    url: url,
    method: 'post',
    data: data,
  });
}

// 新中平-监管账户信息变动反馈-新增数据
export function addJgzhInfo (data) {
  const {
      id = "",
      serialno = "",
      bankid = "",
      accountname = "",
      accountno = "",
      executetype = "",
      executeamount = "",
      executedept = "",
      executedate = "",
      releaseserialno = "",
      releasetime = "",
      note = ""
  } = data || {}
  return request({
    url: '/api/admin/xzp/addJgzhInfo',
    method: 'post',
    data: {
      id,
      serialno,
      bankid,
      accountname,
      accountno,
      executetype,
      executeamount,
      executedept,
      executedate,
      releaseserialno,
      releasetime,
      note
    }
  });
}

// 新中平-监管账户信息变动反馈-修改
export function editJgzh (data) {
  const {
      id =  data.$id,
      serialno  = data.$serialno,
      bankid  = data.$bankid,
      accountname = data.$accountname,
      accountno = data.$accountno,
      executetype = data.$executetype,
      executeamount = data.$executeamount,
      executedept = data.$executedept,
      executedate = data.$executedate,
      releaseserialno = data.$releaseserialno,
      releasetime = data.$releasetime,
      note = data.$note,
      jgzhStatus = data.$jgzhStatus
  } = data || {}
  return request({
    url: `/api/admin/xzp/editJgzh`,
    method: 'post',
    data: {
      id,
     serialno,
     bankid,
     accountname,
     accountno,
     executetype,
     executeamount,
     executedept,
     executedate,
     releaseserialno,
     releasetime,
     note,
     jgzhStatus
    }
  });
}

export function editJgzhInfo (data) {
  const {
      id =  data.$id,
      jgzhStatus = data.$jgzhStatus
  } = data || {}
  return request({
    url: `/api/admin/xzp/editJgzh`,
    method: 'post',
    data: {
      id,
     jgzhStatus
    }
  });
}

// 新中平-监管账户信息变动反馈-详情
export function viewJgzh (id) {
  return request({
    url: '/api/admin/xzp/JgzhDetail/' + parseStrEmpty(id),
    method: 'get'
  })

}

// 监管账户-批量修改状态
export function batchUpdateJgzh (data) {
  return request({
    url: `/api/admin/xzp/JgzhBatch`,
    method: 'post',
    data: data,
  });
}

//导出监管账户数据接口
export function exportJgzh (params) {
  return request({
    url: '/api/admin/xzp/actions/export',
    method: 'post',
    data: params,
    responseType: 'blob',
  });
}

//下载新中平-监管账户信息变动反馈导入模板
export function downloadExample (data) {
  return request({
    url: '/api/admin/xzp//template/actions/download',
    method: 'get',
    data: data,
    responseType: 'blob',
  });
}

//批量导入数据接口
export function importJgzh (data) {
  return request({
    url: '/api/admin/xzp/actions/import',
    method: 'post',
    data: data,
    contentType: false,
    processData: false,
    headers: {
      'Content-Type': 'multipart/form-data;',
    },
  });
}
//增量导入数据接口
export function importJgzhAdd (data) {
  return request({
    url: '/api/admin/xzp/actions/import_add',
    method: 'post',
    data: data,
    contentType: false,
    processData: false,
    headers: {
      'Content-Type': 'multipart/form-data;',
    },
  });
}

// 删除用户
export function delJgzh (id) {
  return request({
    url: '/api/admin/xzp/removeJgzh/' + id,
    method: 'post',
  })
}

// 批量删除用户
export function delJgzhs (ids) {
  return request({
    url: '/api/admin/xzp/removeBatchJgzh',
    method: 'post',
    data: ids
  })
}
