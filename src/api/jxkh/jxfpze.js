import request from '@/utils/request';

/**
 * 2022/3/2 liyang
 * @param {* roleId:角色编号,roleName:角色名称,status:状态,pageNum：当前页数,pageSize：每页几条} query
 */
/**
 * 2022/3/2 liyang
 * @param {* roleId:角色编号,roleName:角色名称,status:状态,pageNum：当前页数,pageSize：每页几条} query
 */
export function listSalarySub(data) {
    const {
        name = '',
        category = '',
        state = '',
        currentPage = 1,
        pageSize = 10,
    } = data || {};
    return request({
        url: `/api/admin/jxkh/kmwh/getPageSalarySub?pageNum=${currentPage}&pageSize=${pageSize}`,
        method: 'post',
        data: {
            name,
            category,
            state,
        },
    });
}

export function listJxfpze(data) {
    const {
        deptId = '',
        type = '',
        year = '',
        yearQuar = '',
        yearMonth = '',
        currentPage = 1,
        pageSize = 10,
    } = data || {};
    return request({
        url: `/api/admin/jxkh/kmwh/getPageJxkhJxfpze?pageNum=${currentPage}&pageSize=${pageSize}`,
        method: 'post',
        data: {
            deptId,
            type,
            year,
            yearQuar,
            yearMonth,
        },
    });
}

// 新增科目
export function addJxfpze(data) {
    return request({
        url: '/api/admin/jxkh/kmwh/addJxkhJxfpze',
        method: 'post',
        data: data,
    });
}

// 修改科目
export function updateJxfpze(data) {
    return request({
        url: '/api/admin/jxkh/kmwh/editJxkhJxfpze',
        method: 'post',
        data: data,
    });
}

// 角色状态修改
export function changeRoleStatus(roleId, status) {
    const data = {
        roleId,
        status,
    };
    return request({
        url: '/system/role/changeStatus',
        method: 'put',
        data: data,
    });
}

// 删除科目
export function delJxfpze(id) {
    return request({
        url: '/api/admin/jxkh/kmwh/removeJxkhJxfpze/' + id,
        method: 'post',
    });
}

//下载模板
export function downloadExample(data) {
    return request({
        url: '/api/admin/jxkh/kmwh/exportJxkhJxfpze',
        method: 'post',
        responseType: 'blob',
        data: data,
    });
}

//导入模板
export function importEx(data) {
    return request({
        url: '/api/admin/jxkh/kmwh/importEx',
        method: 'post',
        data: data,
        contentType: false,
        processData: false,
        headers: {
            'Content-Type': 'multipart/form-data;',
        },
    });
}

export function getSettingJxze(data) {
    const {
        deptId = '',
        type = '',
        year = '',
        yearQuar = '',
        yearMonth = '',
        batchCode = '',
    } = data || {};
    return request({
        url: `/api/admin/jxkh/kmwh/getSettingJxze`,
        method: 'post',
        data: {
            deptId,
            type,
            year,
            yearQuar,
            yearMonth,
            batchCode,
        },
    });
}

// 新增绩效分配总额
export function addBatch(data) {
    return request({
        url: '/api/admin/jxkh/kmwh/addBatch',
        method: 'post',
        data: data,
    });
}
