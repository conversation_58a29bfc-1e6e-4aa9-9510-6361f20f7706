import request from "@/utils/request";

/**
 * 2024/12/06
 * @param {* pageNum：当前页数,pageSize：每页几条} query
 */
export function list (data) {
  const { currentPage = 1,pageSize = 10 } = data || {}
  return request({
    url:`/api/admin/jxkh/gzwh/actions/page?pageNum=${currentPage}&pageSize=${pageSize}`,
    method: 'post',
    data: data
  })
}

//下载综合绩效考核-绩效批次导入模板
export function downloadExample (data) {
  return request({
    url: '/api/admin/jxkh/gzwh/actions/queryGenerateModel',
    method: 'post',
    data: data,
    responseType: 'blob',
  });
}

//导入工资明细
export function importGz(data) {
  return request({
    url: '/api/admin/jxkh/gzwh/actions/importGz',
    method: 'post',
    data: data,
    contentType:false,
    processData:false,
    headers:{
      'Content-Type':'multipart/form-data;'
    }
  })
}

// 修改批次状态
export function changeState (id) {
  return request({
    url: '/api/admin/jxkh/gzwh/actions/changeState/'+ id,
    method: 'post',
  })
}




// 删除科目
export function delEmpByBatchIdAndEmpId (batchId,empId) {
  return request({
    url: '/api/admin/jxkh/gzwh/actions/remove/' + batchId+'?empId='+empId,
    method: 'post'
  })
}