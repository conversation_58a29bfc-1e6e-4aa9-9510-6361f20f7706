import request from '@/utils/request';

export function list(data) {
    const {
        batchId = data.batchId,
        empName = data.empName,
        batchCode = data.batchCode,
        currentPage = 1,
        pageSize = 10,
    } = data || {};
    return request({
        url: `/api/admin/jxkh/list?pageNum=${currentPage}&pageSize=${pageSize}`,
        method: 'post',
        data: {
            batchId,
            empName,
            batchCode,
        },
    });
}

export function batchAdd(data) {
    return request({
        url: '/api/admin/jxkh/batchAdd',
        method: 'post',
        data: data,
    });
}

export function batchAddJxkhVo(data) {
    return request({
        url: '/api/admin/jxkh/batchAddJxkhVo',
        method: 'post',
        data: data,
    });
}

export function jxgzglList(data) {
    const {
        batchId = data.batchId,
        empName = data.empName,
        type = data.type,
        year = data.year,
        yearMonth = data.yearMonth,
        yearQuar = data.yearQuar,
        currentPage = 1,
        pageSize = 10,
    } = data || {};
    return request({
        url: `/api/admin/jxkh/jxgzgl/list?pageNum=${currentPage}&pageSize=${pageSize}`,
        method: 'post',
        data: {
            batchId,
            empName,
            type,
            year,
            yearMonth,
            yearQuar,
        },
    });
}

export function lockData(data) {
    const {
        batchId = data.batchId,
        empName = data.empName,
        type = data.type,
        year = data.year,
        yearMonth = data.yearMonth,
        yearQuar = data.yearQuar,
        currentPage = 1,
        pageSize = 10,
    } = data || {};
    return request({
        url: `/api/admin/jxkh/lockData?pageNum=${currentPage}&pageSize=${pageSize}`,
        method: 'post',
        data: {
            batchId,
            empName,
            type,
            year,
            yearMonth,
            yearQuar,
        },
    });
}

//导出数据接口
export function exportData(params) {
    return request({
        url: '/api/admin/jxkh/actions/export',
        method: 'post',
        data: params,
        responseType: 'blob',
    });
}
