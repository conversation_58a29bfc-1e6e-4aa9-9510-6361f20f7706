import request from '@/utils/request'

// 欠费库记录查询
export function getPayOweList(data) {
  const { currentPage = 1, pageSize = 10 } = data || {}
  return request({
    url: `/api/admin/xzp/payOweList?pageNum=${currentPage}&pageSize=${pageSize}`,
    method: 'post',
    data: data
  })
}

// 获取业务代码下拉数据
export function getOpeCdList(data) {
  return request({
    url: `/api/admin/xzp/getOpeCdList`,
    method: 'get',
    params: data
  })
}

// 获取委托单位代码下拉数据
export function getMerchIdList(query) {
  return request({
    url: '/api/admin/xzp/getMerchIdList',
    method: 'get',
    params: query
  })
}

// 欠费库记录脱敏
export function payOweSensitive(data) {
  return request({
    url: '/api/admin/xzp/merch/payOweSensitive', // 这是一个假设的URL，请根据实际情况修改
    method: 'post',
    data: data
  })
} 