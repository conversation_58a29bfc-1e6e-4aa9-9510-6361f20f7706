import request from '@/utils/request';
import { parseStrEmpty } from "@/utils/youi";


// 更新批量交易控制
export function updateBatchTranCtrl(data) {
  return request({
    url: '/api/admin/xzp/batchTranCtrlEdit',
    method: 'post',
    data: data
  });
}

// 批量交易控制表-分页查询
export function batchTranCtrlList(data) {
  const { currentPage = 1, pageSize = 10 } = data || {}
  return request({
    url: `/api/admin/xzp/batchTranCtrlList?pageNum=${currentPage}&pageSize=${pageSize}`,
    method: 'post',
    data: data
  });
}

// 批量调整外联批次状态
export function batchUpdateTxnStaToZero(data) {
  return request({
    url: '/api/admin/xzp/batchUpdateTxnStaToZero',
    method: 'post',
    data
  });
}