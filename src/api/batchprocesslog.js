import request from '@/utils/request';

// 外联批量处理日志-分页查询
export function batchProcessLogList(data) {
  const { currentPage = 1, pageSize = 10 } = data || {}
  return request({
    url: `/api/admin/xzp/log/batchProcessLogList?pageNum=${currentPage}&pageSize=${pageSize}`,
    method: 'post',
    data: data
  });
}

// 批量调整外联批次状态
export function batchUpdateTxnStaToZero(data) {
  return request({
    url: '/api/admin/xzp/batchUpdateTxnStaToZero',
    method: 'post',
    data
  });
}