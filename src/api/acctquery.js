import request from '@/utils/request';

// 账户信息查询联想接口
export function suggestAcctInfo (keyword) {
  return request({
    url: '/api/admin/xzp/suggestAcctInfo',
    method: 'get',
    params: {
      keyword: keyword
    }
  })
}

// 业务查询接口
export function businessQuery (data) {
  return request({
    url: '/api/admin/xzp/spf/20002',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data: data
  })
}