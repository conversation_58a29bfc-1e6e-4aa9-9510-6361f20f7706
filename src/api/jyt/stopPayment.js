import request from '@/utils/request';

// 止付保护措施-列表查询
export function listData(data) {
    const { currentPage = 1, pageSize = 10 } = data || {};
    return request({
        url: `/api/admin/jyt/stopPayment/list?pageNum=${currentPage}&pageSize=${pageSize}`,
        method: 'post',
        data: data,
    });
}
 

// 预警数据-报送
export function downFile(data) {
    return request({
        url: '/api/admin/jyt/stopPayment/downFile',
        method: 'post',
        data: data,
        responseType: 'blob',
    });
}
