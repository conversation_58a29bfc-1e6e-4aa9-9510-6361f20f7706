import request from '@/utils/request';

// 预警数据-列表查询
export function listData(data) {
    const { currentPage = 1, pageSize = 10 } = data || {};
    return request({
        url: `/api/admin/jyt/warnDataPush/list?pageNum=${currentPage}&pageSize=${pageSize}`,
        method: 'post',
        data: data,
    });
}

// 预警数据-报送
export function pushData(data) {
    return request({
        url: '/api/admin/jyt/warnDataPush/push',
        method: 'post',
        data: data,
    });
}
