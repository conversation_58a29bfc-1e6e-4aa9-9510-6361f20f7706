import request from '@/utils/request';
import { parseStrEmpty } from "@/utils/youi";

// 开户-列表查询
export function listData(data) {
    const { currentPage = 1, pageSize = 10 } = data || {};
    return request({
        url: `/api/admin/jyt/openAccRisk/list?pageNum=${currentPage}&pageSize=${pageSize}`,
        method: 'post',
        data: data,
    });
}

// 开户风险-查询
export function pushQueryData(data) {
    return request({
        url: '/api/admin/jyt/openAccRisk/openaccRiskQuery',
        method: 'post',
        data: data,
    });
}

// 开户风险-查询
export function pushFbkData(data) {
    return request({
        url: '/api/admin/jyt/openAccRisk/openaccFeedback',
        method: 'post',
        data: data,
    });
}


// 开户风险反馈-详情
export function viewOpenacc (id) {
  return request({
    url: '/api/admin/jyt/openAccRisk/openaccDetail/' + parseStrEmpty(id),
    method: 'get'
  })
}
