import request from '@/utils/request';
import { parseStrEmpty } from "@/utils/youi";

//新中平-监管账户信息变动反馈情况-审核

// 新中平-监管账户信息变动反馈情况查询 - 审批列表查询;
export function jgzhSpList (data) {
  let url = `/api/admin/xzp/jgzhSpList?pageSize=${data.pageSize}&pageNum=${data.currentPage}`;
  return request({
    url: url,
    method: 'post',
    data: data,
  });
}



// 新中平-监管账户信息变动反馈-审核
export function spJgzh (data) {
  const {
      id =  data.$id,
      spRes  = data.$spRes,
      spContent = data.$spContent
  } = data || {}
  return request({
    url: `/api/admin/xzp/spJgzh`,
    method: 'post',
    data: {
      id,
      spRes,
      spContent
    }
  });
}

// 新中平-监管账户信息变动反馈-审核详情
export function jgzhSpDetail (id) {
  return request({
    url: '/api/admin/xzp/jgzhSpDetail/' + parseStrEmpty(id),
    method: 'get'
  })

}

// 监管账户-批量修改状态
export function spJgzhBatch (data) {
  return request({
    url: `/api/admin/xzp/spJgzhBatch`,
    method: 'post',
    data: data,
  });
}

//导出监管账户数据接口
export function exportJgzh (params) {
  return request({
    url: '/api/admin/xzp/actions/export',
    method: 'post',
    data: params,
    responseType: 'blob',
  });
}

//下载新中平-监管账户信息变动反馈导入模板
export function downloadExample (data) {
  return request({
    url: '/api/admin/xzp/template/actions/download',
    method: 'get',
    data: data,
    responseType: 'blob',
  });
}

//批量导入数据接口
export function importJgzh (data) {
  return request({
    url: '/api/admin/xzp/actions/import',
    method: 'post',
    data: data,
    contentType: false,
    processData: false,
    headers: {
      'Content-Type': 'multipart/form-data;',
    },
  });
}
//增量导入数据接口
export function importJgzhAdd (data) {
  return request({
    url: '/api/admin/xzp/actions/import_add',
    method: 'post',
    data: data,
    contentType: false,
    processData: false,
    headers: {
      'Content-Type': 'multipart/form-data;',
    },
  });
}

// 删除用户
export function delJgzh (id) {
  return request({
    url: '/api/admin/xzp/removeJgzh/' + id,
    method: 'post',
  })
}

// 批量删除用户
export function delJgzhs (ids) {
  return request({
    url: '/api/admin/xzp/removeBatchJgzh',
    method: 'post',
    data: ids
  })
}

//查询敏感信息
export function querySensitive (id,sensitiveStatus) {
  return request({
    url: `/api/admin/xzp/jgzhSp/sensitive/get/${id}/${sensitiveStatus}`,
    method: 'get',
  })
}
