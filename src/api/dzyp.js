import request from '@/utils/request';
import { parseStrEmpty } from "@/utils/youi";

// 抵质押品查询 - 列表查询;
export function list (data) {
  let url = `/api/admin/dzyp/list?pageSize=${data.pageSize}&pageNum=${data.currentPage}`;
  return request({
    url: url,
    method: 'post',
    data: data,
  });
}

// 抵质押品-新增数据
export function addDzypInfo (data) {
  const {
    id = "",
      stockNum = "",
      impTime = "",
      othType = "",
      othWarrantNum = "",
      othRecordDate = "",
      borrowName = "",
      borrowIdCard = "",
      handleOrg = "",
      handleCustManager = "",
      loanType = "",
      loanContractNum = "",
      loanLife = "",
      recordedValue = "",
      packetNum = "",
      successor = "",
      purchaseContractNum = "",
      noteInfo = "",
      stockStatus = "",
      suppRegistMark = "",
      pledgeStatusAfterlogout = "",
      propertycard = "",
      propertyownerName = "",
      propertyownerIdCard = "",
      propertyrightAddr = "",
      propertycardNum = "",
      propertycardIssueDate = "",
      undetermined = ""
  } = data || {}
  return request({
    url: '/api/admin/dzyp/addDzypInfo',
    method: 'post',
    data: {
      id,
      stockNum,
      impTime,
      othType,
      othWarrantNum,
      othRecordDate,
      borrowName,
      borrowIdCard,
      handleOrg,
      handleCustManager,
      loanType,
      loanContractNum,
      loanLife,
      recordedValue,
      packetNum,
      successor,
      purchaseContractNum,
      noteInfo,
      stockStatus,
      suppRegistMark,
      pledgeStatusAfterlogout,
      propertycard,
      propertyownerName,
      propertyownerIdCard,
      propertyrightAddr,
      propertycardNum,
      propertycardIssueDate,
      undetermined
    }
  });
}

// 抵质押品查询-修改库存状态
export function editDzyp (data) {
  const {
     id = "",
      stockStatus = data.$stockStatus,
      suppRegistMark = data.$suppRegistMark,
      pledgeStatusAfterlogout = data.$pledgeStatusAfterlogout,
      noteInfo = data.$noteInfo,
      propertycardNum = data.$propertycardNum,
      undetermined = data.$undetermined
  } = data || {}
  return request({
    url: `/api/admin/dzyp/editDzyp`,
    method: 'post',
    data: {
      id,
      stockStatus,
      suppRegistMark,
      pledgeStatusAfterlogout,
      noteInfo,
      propertycardNum,
      undetermined
    }
  });
}

// 抵质押品查询-详情
export function viewDzyp (id) {
  return request({
    url: '/api/admin/dzyp/viewDzyp/' + parseStrEmpty(id),
    method: 'get'
  })

}

// 抵质押品查询-批量修改状态
export function batchUpdateDzyp (data) {
  return request({
    url: `/api/admin/dzyp/editDzypBatch`,
    method: 'post',
    data: data,
  });
}

//导出抵质押品数据接口
export function exportDzyp (params) {
  return request({
    url: '/api/admin/dzyp/actions/export',
    method: 'post',
    data: params,
    responseType: 'blob',
  });
}

//下载抵质押品信息导入模板
export function downloadExample (data) {
  return request({
    url: '/api/admin/dzyp/template/actions/download',
    method: 'get',
    data: data,
    responseType: 'blob',
  });
}

//批量导入数据接口
export function importDzyp (data) {
  return request({
    url: '/api/admin/dzyp/actions/import',
    method: 'post',
    data: data,
    contentType: false,
    processData: false,
    headers: {
      'Content-Type': 'multipart/form-data;',
    },
  });
}
//增量导入数据接口
export function importDzypAdd (data) {
  return request({
    url: '/api/admin/dzyp/actions/import_add',
    method: 'post',
    data: data,
    contentType: false,
    processData: false,
    headers: {
      'Content-Type': 'multipart/form-data;',
    },
  });
}

// 删除用户
export function delDzyp (id) {
  return request({
    url: '/api/admin/dzyp/removeDzyp/' + id,
    method: 'post',
  })
}

// 批量删除用户
export function delDzyps (ids) {
  return request({
    url: '/api/admin/dzyp/removeBatchDzyp',
    method: 'post',
    data: ids
  })
}


//查询敏感信息
export function querySensitive (id,sensitiveStatus) {
  return request({
    url: `/api/admin/dzyp/sensitive/get/${id}/${sensitiveStatus}`,
    method: 'get',
  })
}

