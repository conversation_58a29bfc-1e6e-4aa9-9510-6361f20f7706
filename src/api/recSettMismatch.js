import request from '@/utils/request';


// 更新批量交易控制
export function updateRecSettMismatch(data) {
  return request({
    url: '/api/admin/xzp/recSettMismatchEdit',
    method: 'post',
    data: data
  });
}

// 对账/清算不一致表-分页查询
export function recSettMismatchList(data) {
  const { currentPage = 1, pageSize = 10 } = data || {}
  return request({
    url: `/api/admin/xzp/recSettMismatchList?pageNum=${currentPage}&pageSize=${pageSize}`,
    method: 'post',
    data: data
  });
}

//导出接口
export function exportRecSettMismatch(data) {
  return request({
    url: '/api/admin/xzp/exportRecSettMismatch',
    method: 'post',
    data: data,
    responseType: 'blob',
    timeout: 20000
  })
}