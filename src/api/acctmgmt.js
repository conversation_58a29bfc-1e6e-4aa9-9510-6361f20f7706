import request from '@/utils/request';

// 账户信息联想查询
export function suggestAcctInfo(keyword) {
  return request({
    url: '/api/admin/xzp/suggestAcctInfo',
    method: 'get',
    params: {
      keyword: keyword
    }
  });
}

// 账户信息分页查询
export function queryAcctInfoList(data) {
  return request({
    url: '/api/admin/xzp/queryAcctInfoList',
    method: 'post',
    params: {
      pageNum: data.current || 1,
      pageSize: data.size || 10
    },
    data: data
  });
}

// 新增账户信息
export function addAcctInfo(data) {
  return request({
    url: '/api/admin/xzp/addAcctInfo',
    method: 'post',
    data: data
  });
}

// 修改账户信息
export function updateAcctInfo(data) {
  return request({
    url: '/api/admin/xzp/updateAcctInfo',
    method: 'post',
    data: data
  });
}

// 删除账户信息
export function deleteAcctInfo(cpabAccId, acctNm) {
  return request({
    url: '/api/admin/xzp/deleteAcctInfo',
    method: 'post',
    params: {
      cpabAccId: cpabAccId,
      acctNm: acctNm
    }
  });
}

// 查看账户信息详情
export function getAcctInfo(id) {
  return request({
    url: `/api/admin/xzp/getAcctInfo/${id}`,
    method: 'get'
  });
}

// 获取账户敏感信息（脱敏/不脱敏）
export function getAcctSensitiveInfo(cpabAccId, acctNm, sensitiveStatus) {
  return request({
    url: `/api/admin/xzp/acctSensitive/${encodeURIComponent(cpabAccId)}/${encodeURIComponent(acctNm)}/${sensitiveStatus}`,
    method: 'get'
  });
}

// 根据业务标识获取账户敏感信息（脱敏/不脱敏）- 使用oth_msg2_tx作为唯一标识
export function getAcctSensitiveInfoByBizKey(othMsg2Tx, sensitiveStatus) {
  return request({
    url: `/api/admin/xzp/acctSensitiveByBizKey/${encodeURIComponent(othMsg2Tx)}/${sensitiveStatus}`,
    method: 'get'
  });
}

// 根据oth_msg2_tx获取账户详情
export function getAcctInfoByOthMsg2Tx(othMsg2Tx) {
  return request({
    url: `/api/admin/xzp/getAcctInfoByOthMsg2Tx/${encodeURIComponent(othMsg2Tx)}`,
    method: 'get'
  });
}

// 根据oth_msg2_tx删除账户信息
export function deleteAcctInfoByOthMsg2Tx(othMsg2Tx) {
  return request({
    url: '/api/admin/xzp/deleteAcctInfoByOthMsg2Tx',
    method: 'post',
    params: {
      othMsg2Tx: othMsg2Tx
    }
  });
}

// 根据oth_msg2_tx修改账户信息
export function updateAcctInfoByOthMsg2Tx(data) {
  return request({
    url: '/api/admin/xzp/updateAcctInfoByOthMsg2Tx',
    method: 'post',
    data: data
  });
}