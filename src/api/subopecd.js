import request from '@/utils/request'

// 子业务参数管理查询
export function merchSubOpeList(data) {
  const { currentPage = 1, pageSize = 10 } = data || {}
  return request({
    url: `/api/admin/xzp/merch/merchSubOpeList?pageNum=${currentPage}&pageSize=${pageSize}`,
    method: 'post',
    data: data
  })
}

// 子业务参数管理新增或修改
export function saveOrUpdateMerchSubOpe(data) {
  return request({
    url: '/api/admin/xzp/merch/saveOrUpdateMerchSubOpe',
    method: 'post',
    data: data
  })
}

// 子业务参数管理删除
export function deleteMerchSubOpe(data) {
  return request({
    url: '/api/admin/xzp/merch/deleteMerchSubOpe',
    method: 'post',
    data: data
  })
} 