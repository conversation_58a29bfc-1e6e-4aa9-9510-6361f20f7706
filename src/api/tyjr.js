import request from '@/utils/request';

/**
 * 2024/3/7
 *  查询统一接入系统列表
 */
export function queryJoinSysList(data) {
    return request({
        url: `/api/admin/tyjr/queryJoinSysList`,
        method: 'post',
        data,
    });
}

/**
 * 2024/3/2
 *  对接接入系统
 */
export function getCurUserToken(data) {
    return request({
        url: `/api/admin/tyjr/getCurUserToken`,
        method: 'post',
        data,
    });
}

/**
 * 2024/4/28
 *  将yoaf框架生成的redis的用户信息存到另一个onlineUser的key里，使得二者信息保持同步
 */
export function syncYoafToken(data) {
  return request({
    url: `/api/admin/tyjr/syncYoafToken`,
    method: 'post',
    data,
  });
}

/**
 * 查询一个字典项
 */
export function getDictEntry(dictTypeId, dictId) {
    return request({
        url:
            `/api/dict/entries/actions/get?dictTypeId=` +
            dictTypeId +
            '&dictId=' +
            dictId,
        headers: {
            isToken: false,
        },
        method: 'get',
    });
}

/**
 *  空请求(用于首页点击子系统链接前调该接口判断超时自动跳到登录页)
 */
export function requestNull() {
    return request({
        url: `/api/admin/tyjr/requestNull`,
        method: 'post',
    });
}

// 统一接入 - 列表查询;
export function list(data) {
    let url = `/api/admin/tyjr/list?pageSize=${data.pageSize}&pageNum=${data.currentPage}`;
    return request({
        url: url,
        method: 'post',
        data: data,
    });
}

// 新增系统
export function add(data) {
    return request({
        url: '/api/admin/tyjr/add',
        method: 'post',
        data: data,
    });
}

// 编辑系统
export function edit(data) {
    return request({
        url: '/api/admin/tyjr/edit',
        method: 'put',
        data: data,
    });
}

// 删除/批量删除
export function remove(ids) {
    return request({
        url: `/api/admin/tyjr/delete?ids=${ids}`,
        method: 'get',
    });
}

// 获取自动生成的系统编号
export function getAutoSysNo() {
    return request({
        url: '/api/admin/tyjr/autoSysNo',
        method: 'get',
    });
}
