import request from "@/utils/request";

/**
 * 2022/3/2
 * @param {* roleId:角色编号,roleName:角色名称,status:状态,pageNum：当前页数,pageSize：每页几条} query
 */
//工作组列表
export function listRole(data) {
  const {
    groupId = "",
    groupName = "",
    status = "",
    currentPage = 1,
    pageSize = 10
  } = data || {};
  return request({
    url: `/api/admin/groups/actions/page?pageNum=${currentPage}&pageSize=${pageSize}`,
    method: "post",
    data: {
      groupId,
      groupName,
      status
    }
  });
}

// 新增工作组...
export function addRole(data) {
  return request({
    url: "/api/admin/groups/actions/add",
    method: "post",
    data: data
  });
}

// 修改够工作组
export function updateRole(data) {
  return request({
    url: "/api/admin/groups/actions/edit",
    method: "post",
    data: data
  });
}

// 删除工作组
export function delRole(groupId) {
  return request({
    url: "/api/admin/groups/actions/remove/" + groupId,
    method: "post"
  });
}
//批量删除工作组
export function delmultiple(groupId) {
 // console.log(groupId,'参数.....');
  return request({
    url: "/api/admin/groups/actions/remove",
    method: "post",
    data: groupId
  });
}

// 查询角色已授权用户列表
export function allocatedUserList(query) {
  const {
    groupId = "",
    userName = "",
    userNickname = "",
    userStat = "",
    currentPage = 1,
    pageSize = 10
  } = query || {};
  return request({
    url: `/api/admin/groups/users/actions/page?pageNum=${currentPage}&pageSize=${pageSize}`,
    method: "post",
    data: {
      groupId,
      userName,
      userNickname,
      userStat
    }
  });
}
// 查询角色未授权用户列表
export function unocatedUserList(query) {
  const {
    groupId = "",
    userName = "",
    userNickname = "",
    userStat = "",
    currentPage = 1,
    pageSize = 10
  } = query || {};
  return request({
    url: `/api/admin/groups/no/users/actions/page?pageNum=${currentPage}&pageSize=${pageSize}`,
    method: "post",
    data: {
      groupId,
      userName,
      userNickname,
      userStat
    }
  });
}

// 保存角色下的用户
export function addRoleUserList(query) {
  return request({
    url: "/api/admin/groups/users/actions/add",
    method: "post",
    data: query
  });
}
// 删除工作组已配置用户列表
export function delRoleUserList(data) {
  return request({
    url: "/api/admin/groups/users/actions/remove",
    method: "post",
    data: data
  });
}





