import request from '@/utils/request'
/** 
*2022/7/14
*@param {* userName:用户名,pageNum:当前页数,pageSize:每页几条} query
*/ 
export function listRecord (data) {
    const { userName,currentPage = 1,pageSize = 10 } = data || {}
    return request({
        url:`/api/auth/center/user/page?pageNum=${currentPage}&pageSize=${pageSize}`,
        method: 'post',
        data: {
            userName
        }
    })
}


//强制下线
export function insertingCoil (query) {
    const { recordId = "", userName = "", device = "",} = query || {}
    return request({
        url:'/api/auth/center/actions/offline',
        method:'post',
        data:{
            recordId,
            userName,
            device
        }
    })
}