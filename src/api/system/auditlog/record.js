import request from '@/utils/request'

/**
 * 2022/4/7
 * @param {*pageNum：当前页数,pageSize：每页几条} data 
 */
// 分页查询审计日志列表
export function listRecord(data) {
  const { currentPage = 1, pageSize = 10 } = data || {}

  return request({
    url: `/api/audit/logs/actions/page?pageNum=${currentPage}&pageSize=${pageSize}`,
    method: 'post',
    data: data
  })
}

// 根据id查询一条审计日志
export function getRecordId(Id) {
  return request({
    url: '/api/audit/logs/actions/get/' + Id,
    method: 'get'
  })
}