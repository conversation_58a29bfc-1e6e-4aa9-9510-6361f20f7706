import request from '@/utils/request'


/**
 * 2022/7/7 
 * 
 * @param {*funcName:资源名称,operRoleId:操作角色编号,operRoleName:操作角色名称,pageNum：当前页数,pageSize：每页几条} query 
 */
// 分页查询本地授权列表
export function listAuths (data) {
    const { currentPage = 1, pageSize = 10, operRoleId = '', operRoleName = '',funcName = '' } = data || {}
    return request({
      url: `/api/admin/auths/actions/page?pageNum=${currentPage}&pageSize=${pageSize}`,
      method: 'post',
      data: {
        operRoleId,
        operRoleName,
        funcName
      }
    })
  }

  // 查询一条本地授权配置
export function getAuths (data) {
    const { operRoleId = "", authRoleId = "", funcId = ""  } = data || {}
    return request({
      url: `/api/admin/auths/actions/get?operRoleId=${operRoleId}&authRoleId=${authRoleId}&funcId=${funcId}`,
      method: 'get',
    })
  }

//   新增一条本地授权配置
export function addAuths (data) {
    const { operRoleId = "", authRoleId = "", funcId = "" } = data || {}
    return request({
      url: '/api/admin/auths/actions/add',
      method: 'post',
      data: {
          operRoleId,
          authRoleId,
          funcId
      }
    })
  }

  // 删除一条本地授权配置
export function removeAuths (data) {
    const { operRoleId = "", authRoleId = "", funcId = ""  } = data || {}
    return request({
      url: `/api/admin/auths/actions/remove/${operRoleId}/${authRoleId}/${funcId}`,
      method: 'post',
    })
  }

  // 批量删除本地授权配置
export function removesAuths (data) {
    return request({
      url: `/api/admin/auths/actions/remove`,
      method: 'post',
      data
    })
  }
  
// 本地授权角色列表查询
export function authRolesList () {
    return request({
      url: `/api/admin/auths/roles/actions/list`,
      method: 'post',
    })
  }

  // 本地授权功能列表查询
export function authFuncList () {
    return request({
      url: `/api/admin/auths/functions/actions/list`,
      method: 'post',
    })
  }

   // 本地授权认证
export function authToken (data) {
  return request({
    url: `/api/auth/center/local/auth/token`,
    method: 'post',
    data
  })
}