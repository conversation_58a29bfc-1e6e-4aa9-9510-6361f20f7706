import request from "@/utils/request";

/**
 * 2022/3/2
 * @param {* postId:岗位编号,postName:岗位名称,status:岗位状态,pageNum：当前页数,pageSize：每页几条} query
 */
//获取岗位列表
export function listJop(data) {
  const {
    postId = "",
    postName = "",
    status = "",
    currentPage = 1,
    pageSize = 10
  } = data || {};
  return request({
    url: `/api/admin/posts/actions/page?pageNum=${currentPage}&pageSize=${pageSize}`,
    method: "post",
    data: {
      postId,
      postName,
      status
    }
  });
}

// 新增岗位
export function addJop(data) {
  return request({
    url: "/api/admin/posts/actions/add",
    method: "post",
    data: data
  });
}

// 修改岗位
export function updateJop(data) {
  return request({
    url: "/api/admin/posts/actions/edit",
    method: "post",
    data: data
  });
}

// 删除岗位
export function delJop(postId) {
  return request({
    url: "/api/admin/posts/actions/remove/" + postId,
    method: "post"
  });
}

//批量删除岗位
export function delmultiple(groupId) {
  console.log(groupId,'参数.....');
  return request({
    url: "/api/admin/posts/actions/remove",
    method: "post",
    data: groupId
  });
}
// 查询角色已授权用户列表
export function allocatedUserList(query) {
  const {
    postId = "",
    userName = "",
    userNickname = "",
    userStat = "",
    currentPage = 1,
    pageSize = 10
  } = query || {};
  return request({
    url: `/api/admin/posts/users/actions/page?pageNum=${currentPage}&pageSize=${pageSize}`,
    method: "post",
    data: {
      postId,
      userName,
      userNickname,
      userStat
    }
  });
}
// 分页查询非岗位下用户接口
export function unocatedUserList(query) {
  const {
    postId = "",
    userName = "",
    userNickname = "",
    userStat = "",
    currentPage = 1,
    pageSize = 10
  } = query || {};
  return request({
    url: `/api/admin/posts/no/users/actions/page?pageNum=${currentPage}&pageSize=${pageSize}`,
    method: "post",
    data: {
      postId,
      userName,
      userNickname,
      userStat
    }
  });
}

// 保存岗位下用户接口
export function addRoleUserList(query) {
  return request({
    url: "/api/admin/posts/users/actions/add",
    method: "post",
    data: query
  });
}
//删除岗位下用户接口
export function delRoleUserList(data) {
  return request({
    url: "/api/admin/posts/users/actions/remove",
    method: "post",
    data: data
  });
}

//5.20.查询用户敏感信息
export function usersSensitive(userName, sensitiveStatus) {
  return request({
    url: `/api/admin/users/sensitive/actions/get/${userName}/${sensitiveStatus}`,
    method: "get"
  });
}
