import request from '@/utils/request';

//*************** 移动端菜单维护 ********************
// 移动端菜单列表查询
export function mobileMenuList(data) {
    let url = `/api/xmqywx/zhyy/menu/list?pageSize=${data.pageSize}&pageNum=${data.currentPage}`;
    return request({
        url: url,
        method: 'post',
        data: data,
    });
}

// 新增移动端菜单
export function addMobileMenu(data) {
    return request({
        url: '/api/xmqywx/zhyy/menu/add',
        method: 'post',
        data: data,
    });
}

// 编辑移动端菜单
export function editMobileMenu(data) {
    return request({
        url: '/api/xmqywx/zhyy/menu/edit',
        method: 'put',
        data: data,
    });
}

// 删除/批量删除移动端菜单
export function deleteMobileMenu(ids) {
    return request({
        url: `/api/xmqywx/zhyy/menu/delete?ids=${ids}`,
        method: 'get',
    });
}

//*************** 三方配置维护 ********************
// 三方配置列表查询
export function thirdConfigList(data) {
  let url = `/api/xmqywx/zhyy/thirdConfig/list?pageSize=${data.pageSize}&pageNum=${data.currentPage}`;
  return request({
    url: url,
    method: 'post',
    data: data,
  });
}

// 新增三方配置
export function addThirdConfig(data) {
  return request({
    url: '/api/xmqywx/zhyy/thirdConfig/add',
    method: 'post',
    data: data,
  });
}

// 编辑三方配置
export function editThirdConfig(data) {
  return request({
    url: '/api/xmqywx/zhyy/thirdConfig/edit',
    method: 'put',
    data: data,
  });
}

// 删除/批量删除三方配置
export function deleteThirdConfig(ids) {
  return request({
    url: `/api/xmqywx/zhyy/thirdConfig/delete?ids=${ids}`,
    method: 'get',
  });
}

//查询三方配置敏感信息
export function thirdConfigSensitive (agentId,sensitiveStatus) {
  return request({
    url: `/api/xmqywx/zhyy/thirdConfig/sensitive/actions/get/${agentId}/${sensitiveStatus}`,
    method: 'get',
  })
}

