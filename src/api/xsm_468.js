import request from '@/utils/request';

// 新市民查询-新增待授权数据
export function add(data) {
    return request({
        url: '/api/admin/xsm/468/add',
        method: 'post',
        data: data,
    });
}

// 新市民查询 - 列表查询;
export function list(data) {
    let url = `/api/admin/xsm/468/list?pageSize=${data.pageSize}&pageNum=${data.currentPage}`;
    return request({
        url: url,
        method: 'post',
        data: data,
    });
}

// 新市民查询-批量确认授权查询
export function authorization(data) {
    return request({
        url: `/api/admin/xsm/468/authorization`,
        method: 'post',
        data: data,
    });
}



//批量导入数据接口
export function importXsm(data) {
    return request({
        url: '/api/admin/xsm/468/actions/import',
        method: 'post',
        data: data,
        contentType: false,
        processData: false,
        headers: {
            'Content-Type': 'multipart/form-data;',
        },
    });
}

//导出新市民数据接口
export function exportXsm(params) {
    return request({
        url: '/api/admin/xsm/468/actions/export',
        method: 'post',
        data: params,
        responseType: 'blob',
    });
}


//查询数据敏感信息
export function dataSensitive(id, sensitiveStatus) {
    return request({
        url: `/api/admin/xsm/468/sensitive/${id}/${sensitiveStatus}`,
        method: 'get',
    });
}