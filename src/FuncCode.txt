 {
"admin:inst:add":	"新增一个机构"
admin:inst:remove	"删除一个机构"
admin:inst:edit	"更新一个机构"
admin:inst:get	"查询机构详情"
admin:inst:page	"分页查询机构列表"
admin:inst:tree	"查询机构树"
admin:inst:dept:tree	"查询机构部门树"
admin:inst:page:children	"查询子机构列表"
admin:inst:export	"导出机构信息"
admin:inst:export	"导入机构信息"
admin:dept:add	"新增一个部门"
admin:dept:remove	"删除一个部门"
admin:dept:remove:batch	"批量删除部门"
admin:dept:edit	"更新一个部门"
admin:dept:get	"查询部门详情"
admin:dept:page	"分页查询部门列表"
admin:dept:page:users	"分页查询部门下的用户列表"
admin:dept:add:users	"添加部门下的用户"
admin:dept:remove:users	"删除部门下的用户"
admin:dept:export	"导出部门信息"
admin:dept:import	"导入部门信息"
admin:user:add	"新增一个用户"
admin:user:remove	"删除一个用户"
admin:user:remove:batch	"批量删除用户"
admin:user:edit	"更新一个用户"
admin:user:get	"查询用户详情"
admin:user:page	"分页查询用户列表"
admin:user:page:channel:users	查询渠道未注册用户信息
admin:user:export	导出用户信息
admin:user:import	导入用户信息
admin:user:passwd:reset	重置密码
admin:post:add	新增一个岗位
admin:post:remove	删除一个岗位
admin:post:remove:batch	批量删除岗位
admin:post:edit	更新一个岗位
admin:post:page	分页查询岗位列表
admin:post:page:users	分页查询岗位下的用户列表
admin:post:add:users	保存岗位下的用户
admin:post:remove:users	删除岗位下的用户
admin:group:add	新增一个工作组
admin:group:remove	删除一个工作组
admin:group:remove:batch	批量删除工作组
admin:group:edit	更新一个工作组
admin:group:page	分页查询工作组列表
admin:group:page:users	分页查询工作组下的用户列表
admin:group:add:users	保存工作组下的用户
admin:group:remove:users	删除工作组下的用户
admin:auths:add	新增一个本地授权配置
admin:auths:remove	删除一个本地授权配置
admin:auths:remove:batch	批量删除本地授权配置
admin:auths:get	查询一条本地授权配置
admin:auths:page	分页查询本地授权配置列表
admin:auths:roles	本地授权表单查询角色列表
admin:auths:functions	本地授权表单查询功能列表
admin:role:add	新增一个角色
admin:role:remove	删除一个角色
admin:role:remove:batch	批量删除角色
admin:role:edit	更新一个角色
admin:role:page	分页查询角色列表
admin:role:page:users	分页查询角色下的用户列表
admin:role:add:users	保存角色下的用户
admin:role:remove:users	删除角色下的用户
admin:role:res:tree	查询菜单资源树
admin:role:res	查询当前角色下资源信息
admin:role:add:res	保存角色资源信息
admin:menu:add	新增一个菜单
admin:menu:remove	删除一个菜单
admin:menu:edit	更新一个菜单
admin:menu:get	查询一个菜单
admin:menu:tree	查询菜单树
admin:function:add	新增一个功能
admin:function:remove	删除一个功能
admin:function:remove:batch	批量删除功能
admin:function:edit	更新一个功能
admin:function:get	查询一个功能
admin:function:page	分页查询功能列表
admin:black-white:add	新增一个黑白名单
admin:black-white:remove	删除一个黑白名单
admin:black-white:remove	批量删除黑白名单
admin:black-white:edit	更新一个黑白名单
admin:black-white:on-off	启用/停用一个黑白名单
admin:black-white:get	查询一个黑白名单
admin:black-white:page	分页查询功能列表
admin:auditlog:config:add	新增一个日志配置
admin:auditlog:config:remove	删除一个日志配置
admin:auditlog:config:remove	批量删除日志配置
admin:auditlog:config:edit	更新一个日志配置
admin:auditlog:config:on-off	启用/停用一个日志配置
admin:auditlog:config:get	查询一个日志配置
admin:auditlog:config:page	分页查询审计日志配置列表
admin:auditlog:get	查询一条审计日志
admin:auditlog:page	分页查询审计日志列表
admin:auditlog:export	导出审计日志信息
admin:auditlog:import	导入审计日志信息
dict:types:add	新增一个业务字典类型
dict:types:remove	删除一个业务字典类型
dict:types:remove:batch	批量删除业务字典类型
dict:types:edit	更新一个字典类型
dict:types:get	查询一个字典类型
dict:types:page	分页查询业务字典类型列表
dict:types:export	导出业务字典类型信息
dict:types:import	导入业务字典类型信息
dict:entries:add	新增一个业务字典项
dict:entries:remove	删除一个业务字典项
dict:entries:remove:batch	批量删除业务字典项
dict:entries:edit	更新一个字典项
dict:entries:get	查询一个字典项
dict:entries:page	分页查询业务字典项列表

}