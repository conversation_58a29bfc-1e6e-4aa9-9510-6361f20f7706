import validate from "@/utils/validate"

export default {
  labelWidth: 80,
  formHeight: 200,
  submitText: '确认',
  top:"20vh",
  column: [
    {
      label: "账号",
      prop: "anthusername",
      span: 24,
      row: true,
      rules: [{
        required: true,
        message: "请输入账号",
        trigger: "blur"
      },{
        validator: validate.userIdValidate, trigger: 'blur'
      }
    ],
    },
    {
      label: "密码",
      prop: "password",
      type:'password',
      span: 24,
      row: true,
      rules: [{
        required: true,
        message: "请输入密码",
        trigger: "blur"
      }
      // , { validator: validate.enhancedPWD, trigger: 'blur' }
    ],
    },
  ]
}
