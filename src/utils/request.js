import axios from 'axios'
import { MessageBox, Message, Loading } from 'element-ui'
import store from '@/store'
import { getAuthorization, getRefreshToken, getAuthMode} from '@/utils/auth'
import errorCode from '@/utils/errorCode'
import { encrypt, UIAEncrypt, base64Str } from "@/utils/smencrypt.js";
import anthsoption from "@/utils/anthsoption";
import DialogForm from "@/components/dialog-form";
import { tansParams, blobValidate } from "@/utils/youi";
import cache from '@/plugins/cache'
import { saveAs } from 'file-saver'
import {uaasTokenRefresh} from "../api/login";
import { formatDate } from "@/utils/index"

// **********************************************【变量/常量】定义区-start**********************************************
// 当前下载实例对象
let downloadLoadingInstance;

// 是否显示重新登录窗口
let isReloginShow = false;

// 是否正在刷新令牌（防止重复刷新令牌）
let isRefreshing = false;

// 刷新令牌请求在短时间内最大发送次数
const MAX_REFRESH_COUNT = 3

// 刷新令牌请求次数统计时间间隔（秒）
const REFRESH_TIME_INTERVAL = 30

// 当前刷新次数
let currentRefreshCount = 0

// 上次刷新时间
let lastRefreshTime = null

// 访问令牌失效请求回调缓存容器（用于刷新令牌后自动回放接口）
let requestTempList = []

// 本地授权请求回调缓存容器
let requestLocalAuth = null

// 本地授权请求取消回调容器
let requestLocalAuthCancel = null;

// 本地授权弹框状态：false-关闭；true-打开
let localAuthDialogShow = false;

// 本地授权弹框函数：close-关闭函数;done-完成函数
let localAuthDialogFunc = null;

// axios默认请求报文类型：json
axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8'

// 创建axios实例
const service = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: process.env.VUE_APP_BASE_API,
  // 超时
  timeout: 600000
})

//
// **********************************************【变量/常量】定义区-end**********************************************

// **********************************************【方法/函数】定义区-start**********************************************
/**
 * 添加访问令牌失效请求回调至容器
 * @param callback
 */
function addRequestTempList(callback) {
  requestTempList.push(callback)
}

/**
 * 刷新令牌后回放所有缓存接口
 * @param newToken
 */
function requestTempListFetched(newToken) {
  // 遍历缓存容器进行请求回放
  requestTempList.forEach((callback) => {
    callback(newToken)
  })
  requestTempList = []
}

/**
 * 放置本地授权回调请求至缓存
 * @param callback
 */
function setRequestLocalAuth(callback) {
  requestLocalAuth = callback;
}

/**
 * 本地授权确定后回放缓存接口
 * @param localAuthData
 */
function requestLocalAuthFetched(localAuthData) {
  requestLocalAuth(localAuthData);
  requestLocalAuth = null
}

/**
 * 放置本地授权取消回调请求至缓存
 * @param callback
 */
function setRequestLocalAuthCancelResolve(callback) {
  requestLocalAuthCancel = callback
}

/**
 * 跳转至登录页面
 */
function locationLogin() {
  if (!isReloginShow) {
    // 设置显示登录页面
    isReloginShow = true
    MessageBox.confirm('登录状态已过期，需要重新登录', '系统提示', {
        confirmButtonText: '重新登录',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      currentRefreshCount = 0
      isRefreshing = false
      lastRefreshTime = null
      requestTempList = []
      resetLocalAuth()
      // 关闭显示登录页面
      isReloginShow = false
      // 调用退出清理客户端缓存，并跳转至登录页
      store.dispatch('FedLogOut').then(() => {
        // 刷新token错误时，需要跳转到登录页面
        window.location = '/login'
        window.location.reload(true)
      })
    }).catch(() => {
      // 关闭显示登录页面
      isRefreshing = false
      lastRefreshTime = null
      requestTempList = []
      isReloginShow = false
      resetLocalAuth()
    });
  }
  // 状态设置为失败（resolve():完成->then(); reject()）
  return Promise.reject('无效的会话，或者会话已过期，请重新登录。')
}

/**
 * 客户端无感刷新缓存令牌
 *
 * @param res 请求对象
 * @returns {Promise<unknown>}
 */
function refreshToken(res) {
  // 当前没有请求正在刷新令牌，则客户端调用接口主动刷新令牌
  if (!isRefreshing) {
    isRefreshing = true

    // 防止后端实例不一致，在REFRESH_TIME_INTERVAL（100s）内刷新次数大于MAX_REFRESH_COUNT（3）次则重新登录
    if (lastRefreshTime) {
      const internal = (Date.now() - lastRefreshTime) / 1000
      if (internal < REFRESH_TIME_INTERVAL) {
        currentRefreshCount = currentRefreshCount + 1
        if (currentRefreshCount > MAX_REFRESH_COUNT) {
          Message({
            message: '客户端刷新令牌过于频繁，现跳转至登录',
            type: 'error'
          })
          return locationLogin();
        }
      } else {
        currentRefreshCount = 0
        lastRefreshTime = Date.now()
      }
    } else {
      lastRefreshTime = Date.now()
    }

    if (getRefreshToken()) {
      // 本地缓存存在刷新令牌，则刷新
      store.dispatch('tokenRefresh').then((res) => {
        if (res) {
          // 当刷新成功后，重新发送缓存请求
          requestTempListFetched(res)
        }
      }).catch(() => {
        // 出现异常
      }).finally(() => {
        isRefreshing = false
      })
    } else {
      // 本地缓存不存在刷新令牌，则跳至登录
      return locationLogin()
    }
  }

  // 添加当前请求到缓存队列，等待刷新获取到新的令牌后进行请求回放
  return new Promise((resolve) => {
    // 只有当token刷新成功后，就会调用通过addSubscriber函数添加的缓存接口
    addRequestTempList((authorization) => {
      // 表示用新的token去替换掉原来的token
      res.config.headers.Authorization = `${authorization}`
      // 替换掉url -- 因为baseUrl会扩展请求url
      res.config.url = res.config.url.replace(res.config.baseURL, '')
      // 用重新封装的config去请求，就会将重新请求后的返回
      resolve(service(res.config))
    })
  })
}

/**
 * 进行本地授权
 * @param res
 */
function localAuth(res) {
  // 确定提交
  const resolve = (onFulfilled) => {
    // 本地授权弹框关闭在后面回调
    localAuthDialogFunc = onFulfilled
    // 点击提交，回放缓存请求
    requestLocalAuthFetched(onFulfilled?.data)
  }

  // 点击取消
  const reject = (onRejected) => {
    // 关闭弹框
    onRejected.close()
    // 变量还原
    localAuthDialogShow = false
    requestLocalAuth = null
    localAuthDialogFunc = null
    // 回调函数返回响应结果
    requestLocalAuthCancel(res.data)
    requestLocalAuthCancel = null
  }

  // Y401ACT300-本地授权第一次拦截
  if (!localAuthDialogShow) {
    localAuthDialogShow = true
    // 弹出本地授权框
    DialogForm.show({
      title: '本地授权',
      width: "30%",
      option: anthsoption,
      tipInfo: res?.data?.message || '请选择合适的角色对应账号进行本地授权'
    }, resolve, reject)

  } else {
    // 弹异常框
    Message({
      message: res?.data?.message || '本地授权请求异常',
      type: 'error'
    })
    // 本地授权表单还原到编辑状态
    if (localAuthDialogFunc) {
      localAuthDialogFunc.done()
    }
  }

  // 进入本地授权异常情况的无限回调
  return new Promise((resolve) => {
    // 用于请求重放回调函数
    // if (requestLocalAuth!=null)
    setRequestLocalAuth((localAuthData) => {
      res.config.headers['Local-Auth-Username'] = localAuthData?.anthusername;
      if(getAuthMode() === 'uaas') {
        res.config.headers['Local-Auth-Password'] = base64Str(UIAEncrypt(localAuthData.password, localAuthData.anthusername));
      } else {
        res.config.headers['Local-Auth-Password'] = encrypt(localAuthData?.password);
      }
      // 替换掉url -- 因为baseUrl会扩展请求url
      res.config.url = res.config.url.replace(res.config.baseURL, '')
      // 用重新封装的config去请求，就会将重新请求后的返回
      resolve(service(res.config))
    })
    // 用于取消本地授权回调函数
    setRequestLocalAuthCancelResolve((data) => {
      resolve(data)
    })
  })
}

/**
 * 还原本地授权相关变量
 */
function resetLocalAuth() {
  // 关闭弹框
  if (localAuthDialogShow && localAuthDialogFunc?.close) {
    localAuthDialogFunc?.close()
  }
  // 本地授权请求回调缓存容器
  requestLocalAuth = null
  // 本地授权请求取消回调容器
  requestLocalAuthCancel = null;
  // 本地授权弹框状态：false-关闭；true-打开
  localAuthDialogShow = false
  // 本地授权弹框函数：close-关闭函数;done-完成函数
  localAuthDialogFunc = null;
}

/**
 * 提示异常信息
 * @param msg 异常信息
 * @param res 返回结果
 * @returns {*}
 */
function messageError(msg, res) {
  Message({
    message: msg,
    type: 'error'
  })
  return res
}
// **********************************************【方法/函数】定义区-end**********************************************


// **********************************************【请求拦截器】定义区-start**********************************************
let uaasTokenRefreshFlag = false //调邮连刷新token的标志位
service.interceptors.request.use(config => {
  // 是否需要设置令牌
  const isToken = (config.headers || {}).isToken === false
  // 是否需要防止数据重复提交
  const isRepeatSubmit = (config.headers || {}).repeatSubmit === false

  // 需要Token认证
  if (!isToken) {
    // 从缓存中获取认证请求头
    const authorization = getAuthorization()
    if(authorization) {
      config.headers['Authorization'] = authorization
    } else {
      // 刷新令牌
      if (!isRefreshing) {
        isRefreshing = true
        if (getRefreshToken()) {
          // 本地缓存存在刷新令牌，则刷新
          store.dispatch('tokenRefresh').then(() => {
            config.headers['Authorization'] = getAuthorization()
          }).catch(() => {
            // 出现异常
          }).finally(() => {
            isRefreshing = false
          })
        } else {
          // 本地缓存不存在刷新令牌，则跳至登录
          return locationLogin()
        }
      }
    }
  }

  // 处理GET请求的URL请求参数
  if (config.method === 'get' && config.params) {
    let url = config.url + '?' + tansParams(config.params);
    url = url.slice(0, -1);
    config.params = {};
    config.url = url;
  }
  // 处理重复提交请求
  if (!isRepeatSubmit && (config.method === 'post' || config.method === 'put')) {
    const requestObj = {
      url: config.url,
      data: typeof config.data === 'object' ? JSON.stringify(config.data) : config.data,
      time: new Date().getTime()
    }
    const repeatSubmitMark = cache.local.getJSON('RepeatSubmitMark')
    if (repeatSubmitMark === undefined || repeatSubmitMark === null || repeatSubmitMark === '') {
      cache.local.setJSON('RepeatSubmitMark', requestObj)
    } else {
      // 请求地址
      const s_url = repeatSubmitMark.url;
      // 请求数据
      const s_data = repeatSubmitMark.data;
      // 请求时间
      const s_time = repeatSubmitMark.time;
      // 间隔时间(ms)，小于此时间视为重复提交
      const interval = 200;
      if (s_data === requestObj.data && requestObj.time - s_time < interval && s_url === requestObj.url) {
        const message = '数据正在处理，请勿重复提交';
        console.warn(`[${s_url}]: ` + message)
        return Promise.reject(new Error(message))
      } else {
        cache.local.setJSON('RepeatSubmitMark', requestObj)
      }
    }
  }

 /* //若当前是邮连跳转过来的，则需要检测判断调统一前置刷新接口，保持token不掉线
  const channel = localStorage.getItem('channel')
  const userToken = localStorage.getItem('userToken')
  const uaas_token_refreshTimestamp = localStorage.getItem('uaas_token_refreshTimestamp') //时间戳

  if(config.url != '/api/admin/tyjr/uaas/refresh' && channel=='uaas' &&  Date.now() > uaas_token_refreshTimestamp){
    if(uaasTokenRefreshFlag==false){
      uaasTokenRefreshFlag = true //表示开始调uaasTokenRefresh
      uaasTokenRefresh({userToken:userToken}).then(res => {
        localStorage.setItem('psbc-uaas-token', res.data.uaas_newToken); //更新邮连域下的token
        localStorage.setItem('userToken', res.data.uaas_newToken); //将邮连的userToken存到本地缓存里
        localStorage.setItem('uaas_token_expireSeconds', res.data.uaas_token_expireSeconds);
        localStorage.setItem('uaas_token_startTime', formatDate(res.data.uaas_token_startTimestamp));
        localStorage.setItem('uaas_token_invalidTime', formatDate(res.data.uaas_token_invalidTimestamp));
        localStorage.setItem('uaas_token_refreshTime', formatDate(res.data.uaas_token_refreshTimestamp));
        localStorage.setItem('uaas_token_refreshTimestamp', res.data.uaas_token_refreshTimestamp);
        uaasTokenRefreshFlag = false  //表示结束调uaasTokenRefresh
      });
    }
  }
*/
  return config
}, error => {
  console.error('Request interceptor exception', error)
  return Promise.reject(error)
})
// **********************************************【请求拦截器】定义区-end**********************************************


// **********************************************【响应拦截器】定义区-start**********************************************
service.interceptors.response.use(res => {
  // 响应数据格式
  const responseType = res?.request?.responseType || ''
  // 响应报文类型
  const contentType = res?.headers['content-type'] || 'application/json;charset=utf-8'
  // 响应数据
  const responseData = res?.data
  // 响应编码
  const responseCode = responseData?.code
  // 响应提示信息
  const responseMessage = responseData?.message;

  // 处理下载文件类型响应
  if (responseType === 'blob' || responseType === 'arraybuffer') {
    if (contentType === 'application/octet-stream' || contentType === 'application/xml') {
      resetLocalAuth()
      return res
    }
  }
  // 处理JSON报文类型响应
  if (responseCode === '0' || responseCode === 0) {
    // 处理成功响应
    resetLocalAuth()
    return responseData
  } else if (responseCode === '-1' || responseCode === -1) {
    // 处理失败响应
    resetLocalAuth()
    return messageError(responseMessage, responseData)
  } else {
    // 其他情况
    resetLocalAuth()
    return responseData
  }
}, error => {
  // 请求响应异常情况处理
  // 响应对象
  const response = error?.response || {}
  // HTTP状态码
  const httpStatus = response?.status
  // 响应数据
  const responseData = response?.data || {}
  // 响应编码
  const responseCode = responseData?.code
  // 响应提示信息
  const responseMessage = responseData?.message;

  // 401 认证相关异常
  if (httpStatus === 401) {
    if (responseCode === 'Y401ACL100') {
      return messageError(responseMessage || '账户不存在', responseData)
    } else if (responseCode === 'Y401ACL200') {
      return messageError(responseMessage || '账户名或密码输入错误', responseData)
    } else if (responseCode === 'Y401ACL301') {
      return messageError(responseMessage || '账户已停用,请联系管理员', responseData)
    } else if (responseCode === 'Y401ACL302') {
      return messageError(responseMessage || '账户已注销,请联系管理员', responseData)
    } else if (responseCode === 'Y401ACL303') {
      return messageError(responseMessage || '账户已锁定,请稍后重试', responseData)
    } else if (responseCode === 'Y401ACT100') {
      // Y401ACT100：访问令牌无效或过期，则无感刷新令牌
      if (response.config?.url === '/logout') {
        return response
      } else {
        return refreshToken(response)
      }
    } else if (responseCode === 'Y401ACT110') {
      return locationLogin();
    } else if (responseCode === 'Y401ACT200') {
      // Y401ACT200：刷新令牌无效或过期，则跳转登录页面
      return locationLogin();
    } else if (responseCode === 'Y401ACT310') {
      // Y401ACT310：首次本地授权认证
      return localAuth(response)
    } else if (responseCode === 'Y401ACT320') {
      return localAuth(response)
    } else {
      return messageError('401未知认证异常', Promise.reject(error))
    }
  } else if (httpStatus === 403) {
    // 403 授权相关异常
    if (responseCode === 'Y403ACA100') {
      // Y403ACA100：无权限访问
      return messageError(responseMessage || '403无权限访问', responseData)
    } else if (responseCode === 'Y403ACA200') {
      // Y403ACA200：权限不足,该资源需要{0}角色授权【本地授权】
      return localAuth(response)
    } else if (responseCode === 'Y403ACA310') {
      // Y403ACA310：你的IP被列入黑名单
    } else if (responseCode === 'Y403ACA320') {
      // Y403ACA320：你的IP未列入白名单
    } else {
      return messageError('403未知授权异常', Promise.reject(error))
    }
  } else if (httpStatus === 500) {
    return messageError('500服务器异常', Promise.reject(error))
  } else if (responseMessage === 'Network Error') {
    return messageError('网络连接异常', Promise.reject(error))
  } else if (responseMessage && responseMessage.include('timeout')) {
    return messageError('请求超时异常', Promise.reject(error))
  } else {
    console.error('未知异常', error)
    return responseData
  }
})
// **********************************************【响应拦截器】定义区-end**********************************************

// 通用下载方法
export function download (url, params, filename) {
  downloadLoadingInstance = Loading.service({ text: "正在下载数据，请稍候", spinner: "el-icon-loading", background: "rgba(0, 0, 0, 0.7)", })
  return service.post(url, params, {
    transformRequest: [(params) => { return tansParams(params) }],
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    responseType: 'blob'
  }).then(async (data) => {
    const isLogin = await blobValidate(data);
    if (isLogin) {
      const blob = new Blob([data])
      saveAs(blob, filename)
    } else {
      const resText = await data.text();
      const rspObj = JSON.parse(resText);
      const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default']
      Message.error(errMsg);
    }
    downloadLoadingInstance.close();
  }).catch((r) => {
    console.error(r)
    Message.error('下载文件出现错误，请联系管理员！')
    downloadLoadingInstance.close();
  })
}

export default service
