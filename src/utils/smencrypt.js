
import { sm2Encrypt, doEncrypt } from '@/assets/lib/sm2.js'
import CryptoJS from '@/assets/lib/crypto-js.js';
const publicKey = "04C0747B45A580A19F47855EFB0DC8533AF7568BB59AC18EC62E5D975A26BE84C361E3AFD7D69C67F69C2AC756EE3EEF2A60BB1E08276C249EC169038792370D15"
// 统一认证模式，国密加密前后字符串
const userStartStr = '{dhjdfu34i34u34-zmew8732dfhjd-'
const userEndStr = 'dfhjdf8347sdhxcye-ehjcbeww34}'
// 统一认证模式，国密加密公钥
const uaasKey = '04DD889EFF3AE4F321A75318A56C9ED6FC28F4FECA23039FD8DD45255F6AE81F26A95E8904EA908F6A5D903E1ADE7625F6054824D9C19A2F8AF7A47B81D2AE6B4C'
export const constant = {
  uaasKey,
};
// 统一身份认证系统前端国密改造
export function UIAEncrypt(password,username) {
  let str = password + userStartStr + username + userEndStr;
  return doEncrypt(str,constant.uaasKey,1);
}
export function base64Str(str) {
  return CryptoJS.enc.Base64.stringify(CryptoJS.enc.Hex.parse(str));
}
// 加密
export function encrypt (txt) {
  return sm2Encrypt(txt, publicKey, 1); // 对数据进行加密
}


