import { instDeptTree,instTree ,instDeptTreeChild} from '@/api/system/dept'
const instInfo = {
  state: {
  },

  mutations: {

  },

  actions: {
    // 获取机构部门树
    instDeptTree ({ commit, state }) {
      return new Promise((resolve, reject) => {
        instDeptTree().then(response => {
          const res = response.data
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 获取机构树
    instTree ({ commit, state },type) {
      return new Promise((resolve, reject) => {
        instTree(type).then(response => {
          const res = response.data
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 7.21查询子机构或子部门列表接口
    instDeptTreeChild ({ commit, state },data) {
      return new Promise((resolve, reject) => {
        instDeptTreeChild(data).then(response => {
          const res = response.data
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },
  }
}

export default instInfo
