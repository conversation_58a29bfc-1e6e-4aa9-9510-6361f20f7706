import { getUser } from '@/api/system/user'
const userInfo = {
  state: {
  },

  mutations: {

  },

  actions: {
    // 获取用户信息
    getUser ({ commit, state }, userName) {
      return new Promise((resolve, reject) => {
        getUser(userName).then(response => {
          console.log(res, 'res000000')
          const res = response.data
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    }
  }
}

export default userInfo
