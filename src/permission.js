import router from "./router";
import store from "./store";
import { Message } from "element-ui";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import { getAuthorization, removeLogonCredentials } from "@/utils/auth";

NProgress.configure({ showSpinner: false });

const whiteList = ["/login", "/auth-redirect", "/bind", "/register"];
let isUserToken = window.location.search.includes("userToken");

router.beforeEach((to, from, next) => {
  NProgress.start();

  //start 20240424添加此段内容，解决邮连跳转登录访问/login?userToken=XXX时会弹出“登录状态超时，重新登录”的弹窗提示
  if (to.path === "/login" && isUserToken ) {
    // 调用退出清理客户端缓存
    store.dispatch('FedLogOut').then(() => {})
  }
  //end

  if (getAuthorization()) {
    to.meta.title && store.dispatch("settings/setTitle", to.meta.title);
    /* has token*/
    if (to.path === "/login") {
      next({ path: "/" });
      NProgress.done();
    } else {
      if (store.getters.roles.length === 0) {
        // 判断当前用户是否已拉取完user_info信息
        store.dispatch("GetInfo")
          .then(() => {
            store.dispatch("GenerateRoutes").then(accessRoutes => {
              // 根据roles权限生成可访问的路由表
              // console.log(accessRoutes,'可访问的路由表')
              router.addRoutes(accessRoutes); // 动态添加可访问路由表
              next({ ...to, replace: true }); // hack方法 确保addRoutes已完成
            });
          })
          .catch(err => {
            store.dispatch("LogOut").then(() => {
              Message.error(err);
              next({ path: "/" });
            });
          });
      } else {
        next();
      }
    }
  } else {
    // 统一认证跳转登录
    if (isUserToken) {
      store.dispatch("UaasTokenLogin", { userToken: to.query.userToken })
        .then(() => {
          next({ path: "/" });
        })
        .catch(err => {
          isUserToken = false;
          next({ path: "/" });
        });
    }
    // 没有token
    else if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next();
    } else {
      next(`/login?redirect=${to.fullPath}`); // 否则全部重定向到登录页
      NProgress.done();
    }
  }
});

router.afterEach(() => {
  NProgress.done();
});
