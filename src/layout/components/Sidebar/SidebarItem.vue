<template>
  <div v-if="!item.hidden">
    <!-- 菜单类型1：目录 -->
    <el-submenu v-if="item.menuType === '1'" ref="subMenu" :index="resolvePath(item)" popper-append-to-body>
      <template slot="title">
        <item v-if="item.meta" :icon="item.meta && item.meta.icon" :title="item.meta.title" :item-level="itemLevel" />
      </template>
      <sidebar-item
        v-for="child in item.children"
        :key="child.path + Math.random()"
        :is-nest="true"
        :item="child"
        :base-path="getBasePath(child)"
        :item-level="itemLevel + 1"
        class="nest-menu"
      />
    </el-submenu>
    <!-- 菜单类型2、3：菜单、外链 -->
    <template v-if="item.menuType === '2' || item.menuType === '3'">
      <app-link :to="resolvePath(item)" :menuType="item.menuType">
        <el-menu-item :index="resolvePath(item)" :key="Math.random()" :class="{'submenu-title-noDropdown':!isNest}">
          <item :icon="item.meta&&item.meta.icon" :title="item.meta.title" :item-level="itemLevel">
          </item>
        </el-menu-item>
      </app-link>
    </template>
  </div>
  <div v-else-if="item.children && item.children.length > 0">
    <sidebar-item
      v-for="child in item.children"
      :key="child.path + Math.random()"
      :is-nest="true"
      :item="child"
      :base-path="getBasePath(child)"
      :item-level="itemLevel"
      class="nest-menu"
    />
  </div>
</template>

<script>
import path from 'path'
import Item from './Item'
import AppLink from './Link'
import FixiOSBug from './FixiOSBug'

export default {
  name: 'SidebarItem',
  components: { Item, AppLink },
  mixins: [FixiOSBug],
  props: {
    // route object
    item: {
      type: Object,
      required: true
    },
    isNest: {
      type: Boolean,
      default: false
    },
    // 当前菜单路由拼装地址 或 外链地址
    basePath: {
      type: String,
      default: ''
    },
    // 当前菜单层级
    itemLevel: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {}
  },
  methods: {
    /**
     * 拼装base地址
     *
     * @param menuItem 菜单路由对象
     * @returns {string}
     */
    getBasePath(menuItem) {
      // 当前菜单为外链直接返回外链地址
      if (menuItem.menuType === '3') {
        return menuItem.menuUrl
      }
      // base地址为外链则直接返回
      if (/^(https?:)/.test(this.basePath)) {
        return this.basePath;
      }
      // 拼装路由地址（一级路由 + 二级路由 + 三级路由）
      return path.resolve(this.basePath, menuItem.path)
    },
    /**
     * 返回菜单路由地址（to/index）
     *
     * @param menuItem 菜单路由对象
     * @returns {string}
     */
    resolvePath(menuItem) {
      // routeQuery 地址参数： ?param=XX
      // { path: path.resolve(this.basePath, routePath), query: query }
      if (menuItem.menuType === '3') {
        return menuItem.menuUrl;
      }
      return this.basePath;
    }
  }
}
</script>
