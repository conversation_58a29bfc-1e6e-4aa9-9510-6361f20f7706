<script>
  export default {
    name: 'MenuItem',
    functional: true,
    props: {
      icon: {
        type: String,
        default: ''
      },
      title: {
        type: String,
        default: ''
      },
      // 当前菜单层级
      itemLevel: {
        type: Number,
        default: 1
      }
    },
    render(h, context) {
      const { icon, title, itemLevel } = context.props
      const vnodes = []

      if (icon) {
        vnodes.push(<svg-icon class-name={icon} icon-class={icon} />)
      }

      // if (title) {
      //   vnodes.push(
      //     <span slot='title'>{(title)}</span>
      //   )}
      if (title) {
        const titleLength = title.length
        let countLength = 11
        if (icon) {
          countLength = countLength - 2
        }
        if (itemLevel) {
          countLength = countLength - (itemLevel - 1) * 2
        }

        if (titleLength > countLength) {
          vnodes.push(<span slot='title' title={(title)}> {(title)} </span>)
        } else {
          vnodes.push(<span slot='title'>{(title)}</span>)
        }
      }
      return vnodes
    }
  }
</script>
