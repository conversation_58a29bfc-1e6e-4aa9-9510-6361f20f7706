<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    :before-close="handleClose"
    :close-on-click-modal= false
    :close-on-press-escape= false
    style="margin-top: 15vh"
    append-to-body
  >
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="100px"
      label-suffix=" : "
      :hide-required-asterisk="false"
    >
      <el-form-item label="旧密码" prop="passwd" style="width: 60%;">
        <el-input type="password" v-model.trim="ruleForm.passwd" show-password></el-input>
      </el-form-item>
      <el-form-item label="新密码" prop="newPasswd" style="width: 60%;">
        <el-input type="password" v-model.trim="ruleForm.newPasswd" show-password></el-input>
      </el-form-item>
      <el-form-item label="确认密码" prop="checkPass" style="width: 60%;">
        <el-input type="password" v-model.trim="ruleForm.checkPass" show-password></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submit">提 交</el-button>
        <el-button @click="cancel" v-if="cancelShow">取 消</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import { mapGetters } from "vuex";
import store from "@/store";
import { alterPassword } from "@/api/login";
import { getAuthMode } from '@/utils/auth'
import { encrypt, UIAEncrypt, base64Str } from "@/utils/smencrypt.js";
export default {
  computed: {
    ...mapGetters(["name", "pwdChgFlag", "pwdExpiredFlag"])
  },
  data() {
    var reg = /^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{6,20}$/;
    var validatePasswd = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入旧密码"));
      }
      // else if (!reg.test(value)) {
      //   callback(new Error("密码必须包含字母、数字、特殊字符,6-20位"));
      // }
      else {
        callback();
      }
    };
    var validateNewPasswd = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入新密码"));
      } else if (value == this.ruleForm.passwd) {
        callback(new Error("新密码不能与旧密码相同!"));
      } else if (!reg.test(value)) {
        callback(new Error("密码必须包含字母、数字、特殊字符,6-20位"));
      } else {
        if (this.ruleForm.checkPass !== "") {
          this.$refs.ruleForm.validateField("checkPass");
        }
        callback();
      }
    };
    var validateCheckPass = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请再次输入新密码"));
      } else if (value !== this.ruleForm.newPasswd) {
        callback(new Error("两次输入密码不一致!"));
      } else if (!reg.test(value)) {
        callback(new Error("密码必须包含字母、数字、特殊字符,6-20位"));
      } else {
        callback();
      }
    };
    return {
      title: "修改密码",
      cancelShow: true,
      dialogVisible: false,
      ruleForm: {
        passwd: "",
        newPasswd: "",
        checkPass: ""
      },
      rules: {
        passwd: [{ validator: validatePasswd, trigger: "blur" }],
        newPasswd: [{ validator: validateNewPasswd, trigger: "blur" }],
        checkPass: [{ validator: validateCheckPass, trigger: "blur" }]
      },
      userStartStr: '{dhjdfu34i34u34-zmew8732dfhjd-',
      userEndStr: 'dfhjdf8347sdhxcye-ehjcbeww34}',
    };
  },
  created() {
    //20240513改造对接邮连，故注释下面的强制修改密码（因为原框架会检测太久没修改密码的话，登录时会自动弹出强制修改密码）
    /*if (this.pwdExpiredFlag == 1 || this.pwdChgFlag == 0) {
      this.cancelShow = false;
      this.title = "强制修改密码";
      this.dialogVisible = true;
    }*/
  },
  methods: {
    show() {
      this.dialogVisible = true;
    },
    // 取消
    cancel() {
      this.$refs.ruleForm.resetFields();
      this.dialogVisible = false;
    },
    // 确定
    submit() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          const { passwd, newPasswd } = this.ruleForm;
          let data = {}
          if(getAuthMode() === 'uaas') {
            let passwdCodeStr = passwd + this.userStartStr + this.name + this.userEndStr;
            let newPasswdCodeStr = newPasswd + this.userStartStr + this.name + this.userEndStr;
            data = {
              userName: this.name,
              // passwd: encrypt(passwd),
              // newPasswd: encrypt(newPasswd)
              passwd: base64Str(UIAEncrypt(passwd,this.name)),
              newPasswd: base64Str(UIAEncrypt(newPasswd, this.name))
            };
          }else{
            data = {
              userName: this.name,
              passwd: encrypt(passwd),
              newPasswd: encrypt(newPasswd)
            };
          }
          alterPassword(data).then(res => {
            if (res.code == 0) {
              this.$message.success(res.message);
              this.$refs.ruleForm.resetFields();
              this.dialogVisible = false;
            }
          });
        } else {
          return false;
        }
      });
    },
    handleClose(done) {
      if (this.pwdExpiredFlag == 1 || this.pwdChgFlag == 0) {
        this.$confirm("请返回修改您的密码, 否则将无法后续操作!", "提示", {
          confirmButtonText: "暂不修改",
          cancelButtonText: "返回",
          type: "warning",
        })
          .then(() => {
            store.dispatch("FedLogOut").then(() => {
              // 跳转到登录页面
              window.location = "#/login";
            });
          })
          .catch(() => {});
      } else {
        this.$refs.ruleForm.resetFields();
        done();
      }
    }
  }
};
</script>
