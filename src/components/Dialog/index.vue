<template>
  <el-dialog title="" :visible.sync="dialogVisible" :width="width" :before-close="handleClose" v-bind="$attrs"
    :append-to-body="true" :close-on-click-modal="false" v-on="$listeners" :style="styleName" append-to-body
    :class="fullscreen?'yo-dialog yo-table__dialog yo-dialog--fullscreen':'yo-dialog '">
    <div slot="title" class="yo-table__dialog__header">
      <span class="el-dialog__title">{{title}}</span>
      <div class="yo-table__dialog__menu">
        <i @click="handleFullScreen" :class="fullscreen?'el-icon-news':'el-icon-full-screen'"
          class="el-dialog__close"></i>
      </div>
    </div>
    <slot name="content" />
    <span v-if="isShowFooter" slot="footer">
      <el-button type="primary" :size="size" @click="submit">{{
          submitText
          }}</el-button>
      <el-button :size="size" @click="cancel">取 消</el-button>

    </span>
    <span v-else slot="footer">
      <slot name="dialog-footer" />
    </span>
  </el-dialog>

</template>

<script>
  export default {
    name: "p-dialog",
    data() {
      return {
        dialogVisible: false,
        fullscreen: false,
      };
    },
    props: {
      title: {
        type: String,
        default: ""
      },
      isShowFooter: {
        type: Boolean,
        default: true
      },
      size: {
        type: String,
        default: "small"
      },
      width: {
        type: String,
        default: "60%"
      },
      submitText: {
        type: String,
        default: "确 定"
      },
      loadingFooter: {
        type: Boolean,
        default: false
      }
    },
    computed: {
      styleName() {
        if (!this.fullscreen) {
          return { top: '15%', bottom: '5%' }
        } else {
          return { top: 0, bottom: 0, marginTop: 0 }
        }
      }
    },
    methods: {
      handleFullScreen() {
        this.fullscreen = !this.fullscreen
      },
      show() {
        this.dialogVisible = true;
      },
      close() {
        this.dialogVisible = false;
      },
      handleClose(done) {
        this.$emit("cancel");
      },
      // 取消
      cancel() {
        this.$emit("cancel");
      },
      // 确定
      submit() {
        this.$emit("submit");
      }
    }
  };
</script>
<style scoped>
  .yo-table__dialog__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .yo-table__dialog__menu {
    padding-right: 20px;
  }
</style>