import Vue from 'vue'
import DataDict from '@/utils/dict'
import { getDicts as getDicts } from '@/api/system/dict/data'

function install() {
  Vue.use(DataDict, {
    metas: {
      '*': {
        labelField: 'dictLabel',
        valueField: 'dictValue',
        request(dictMeta) {
          return getDicts(dictMeta.type).then(res => {
            let result = [...res.data]
            res.data.forEach((item,index) => {
              result[index]['dictLabel'] = result[index]['dictName']
              result[index]['dictValue'] = result[index]['dictId']
              result[index]['type'] = result[index]['dictTypeId']
            })

           return  res.data
            })
        },
      },
    },
  })
}

export default {
  install,
}