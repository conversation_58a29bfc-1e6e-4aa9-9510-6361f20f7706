<template>
  <div>
    <div class="app-container" v-if="showContainer">
      <yo-table
        v-loading="tableLoading"
        :data="data"
        :option="option"
        ref="crud"
        :page.sync="page"
        :search.sync="search"
        @selection-change="filterSelect"
        :permission="getPermission"
        @on-load="onLoad"
        @row-save="rowSave"
        @row-update="rowUpdate"
        v-model="formParent"
      >
        <template slot-scope="{scope}" slot="searchMenu">
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            v-hasPermi="['admin:posts:page']"
          >查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click.stop="resetQuery">重置</el-button>
        </template>
        <!-- 自定义左侧操作栏 -->
        <template slot-scope="{size}" slot="menuLeft">
          <el-button
            type="primary"
            icon="el-icon-plus"
            :size="size"
            plain
            v-hasPermi="['admin:posts:add']"
            @click.stop="handleAdd()"
          >新增</el-button>
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            :size="size"
            :disabled="multiple"
            @click="handleDeleteAll"
            v-hasPermi="['admin:posts:remove:batch']"
          >批量删除</el-button>
        </template>
        <!-- 自定义列 -->
        <template slot="status" slot-scope="scope">
          <el-tag size="mini" effect="plain" :type="tagType(scope.row)">{{status(scope.row)}}</el-tag>
        </template>
        <!-- 自定义操作按钮 -->
        <template slot-scope="{row,size,type,index}" slot="menu">
          <el-button
            :size="size"
            :type="type"
            icon="el-icon-edit"
            @click.stop="handleUpdate(row,index)"
            v-hasPermi="['admin:posts:edit']"
          >修改</el-button>
          <el-button
            :size="size"
            :type="type"
            icon="el-icon-delete"
            @click="handleDelete(row)"
            v-hasPermi="['admin:posts:remove']"
          >删除</el-button>
          <el-dropdown
            :size="size"
            @command="(command) => handleCommand(command, row)"
            v-hasPermi="['admin:posts:users:page']"
          >
            <span class="el-dropdown-link">
              <i class="el-icon-d-arrow-right el-icon--right"></i>更多
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                command="handleAuthUser"
                icon="el-icon-user"
                v-hasPermi="['admin:posts:users:page']"
              >配置用户</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </yo-table>
    </div>
    <div v-else>
      <!-- <auth-user @show-detail="showDetails" :roleId="roleId"></auth-user> -->
      <auth-user v-if="isActive=='authUser'" @show-detail="showDetails" :postId="postId"></auth-user>
    </div>
  </div>
</template>
<script>
import {
  listJop,
  delJop,
  addJop,
  updateJop,
  delmultiple
} from "@/api/system/position";
import {
  treeselect as menuTreeselect,
  roleMenuTreeselect
} from "@/api/system/menu";
import {
  treeselect as deptTreeselect,
  roleDeptTreeselect
} from "@/api/system/dept";
import listOption from "./infoData/listOption.js";
import authUser from "./authUser.vue";
import toTreeArray from "yo-utils/toTreeArray";
export default {
  name: "Position",
  components: {
    authUser
  },
  dicts: ["yoaf_post_state"],
  data() {
    return {
      postId: "",
      showContainer: true, //显示主界面
      isActive: "authUser", // 配置用户 or 操作菜单权限
      formParent: {
        status: "1"
      },
      fullscreen: false,
      search: {},
      page: {
        pageSize: 10,
        currentPage: 1
      },
      data: [],
      option: listOption,
      // 遮罩层
      loading: true,
      tableLoading: true,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      menuData: [],
      // 修改选中项
      resList: [],
      multiple: true,
      num: 0,
      // 表单参数
      form: {},
      postIdArr: [],
      checkPageAll: false, // 操作权限弹窗中全选
      isIndeterminate: false // 全选的不确定状态 只要有一个子集不为true 即设置true
    };
  },
  computed: {
    styleName() {
      if (!this.fullscreen) {
        return { top: "15%", bottom: "5%" };
      } else {
        return { top: 0, bottom: 0, marginTop: 0 };
      }
    }
  },
  created() {
    /** *添加字典项数据*/
    this.updateDictData(
      this.option.column,
      "status",
      this.dict.type["yoaf_post_state"]
    );
  },
  methods: {
    //多选
    filterSelect(selection) {
      this.postIdArr = selection.map(item => item.postId);
      this.multiple = !selection.length;
    },
    // 更新字典数据
    updateDictData(option, key, data) {
      let column = this.findObject(option, key);
      column.dicData = data;
    },
    showDetails() {
      this.showContainer = true;
    },
    /** 新增按钮操作 */
    handleAdd() {
      (this.formParent = {
        status: "1"
      }),
        this.$refs.crud.rowAdd();
    },
    // 首次加载调用此方法
    onLoad(page) {
      this.getList(this.search);
    },
    handleFullScreen() {
      this.fullscreen = !this.fullscreen;
    },
    // 超级管理员不能编辑和删除
    getPermission(key, row, index) {
      const flag =
        (key === "editBtn" || key === "delBtn") && row.roleKey === "admin";
      if (flag) {
        return false;
      } else {
        return true;
      }
    },
    // 新增表单保存
    rowSave(form, done, loading) {
      //console.log(this.form,'我是新增.....');
      addJop(form)
        .then(response => {
          if (response.code == 0) {
            this.$modal.msgSuccess(response.message);
            this.getList();
            done();
          }
        })
        .catch(error => {
          loading();
        });
    },
    // 修改表单保存
    rowUpdate(form, index, done, loading) {
      updateJop(form)
        .then(response => {
          if (response.code == 0) {
            this.$modal.msgSuccess(response.message);
            this.getList();
            done();
          }
        })
        .catch(error => {
          loading();
        });
    },
    // 搜索按钮
    searchChange(params, done) {
      this.handleQuery();
      setTimeout(() => {
        done();
      }, 1000);
    },

    /** 查询角色列表 */
    async getList(params) {
      const data = { ...this.page, ...params };
      let response = await listJop(data);
      if (response.code == 0) {
        this.data = response.data.list;
        this.page.total = response.data.total;
        this.tableLoading = false;
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.page.currentPage = 1;
      this.getList(this.search);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.$refs.crud.searchReset();
      // this.handleQuery();
    },
    // 更多操作触发
    handleCommand(command, row) {
      //console.log(command,row,'1wwqq')
      switch (command) {
        case "handleAuthUser":
          this.handleMoreProcess(row, "authUser");
          break;
        default:
          break;
      }
    },
    /** 修改按钮操作 */
    handleUpdate(row, index) {
      this.formParent = row;
      this.$refs.crud.rowEdit(row, index);
    },
    /** 分配数据权限操作 or  分配用户操作*/
    handleMoreProcess(row, type) {
      this.postId = row.postId;
      this.showContainer = false;
      this.isActive = type;
    },
    /** 删除按钮操作 */
    async handleDel(id, delFn) {
      let res = await delFn(id);
      if (res.code == 0) {
        this.getList();
        this.$message.success(res.message);
      }
    },
    handleDelete(row) {
      const postIds = row.postId;
      this.$modal
        .confirm('是否确认删除岗位编号为"' + postIds + '"的数据项？')
        .then(()=> {
          this.handleDel(postIds, delJop);
        })
        .catch(() => {});
    },
    //批量删除
    async handleDels(id, delFn) {
      let res = await delFn(id);
      if (res.code == 0) {
        this.getList();
        this.$message.success(res.message);
        this.$refs.crud.toggleSelection();
      }
    },
    handleDeleteAll() {
      this.$modal
        .confirm(
          '是否确认删除岗位编号为"' +
            JSON.stringify(this.postIdArr) +
            '"的数据项？'
        )
        .then(() => {
          this.handleDels(this.postIdArr, delmultiple);
        })
        .catch(() => {});
    },
    status(row) {
      switch (row.status) {
        case "1":
          return "启用";
        case "2":
          return "停用";
        case "3":
          return "注销";
        case "4":
          return "锁定";
        default:
          break;
      }
    },
    //tag样式
    tagType(row) {
      switch (row.status) {
        case "1":
          return "success";
        case "2":
          return "danger";
        case "3":
          return "info";
        case "4":
          return "warning";
        default:
          break;
      }
    }
  }
};
</script>
<style scoped>
.yo-table__dialog__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.yo-table__dialog__menu {
  padding-right: 20px;
}
</style>