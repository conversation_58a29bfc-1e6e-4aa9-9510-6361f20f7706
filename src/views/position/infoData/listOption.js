import validate from "@/utils/validate"
export default {
  index: true,
  indexLabel: "序号",
  align: "center",
  card: true,
  selection: true,
  tip:false,
  menuAlign: "center",
  searchBtnText: "查询",
  emptyBtnText: "重置",
  emptyBtnIcon: "el-icon-refresh",
  searchMenuSpan: 4,
  searchMenuPosition: "left",
  addTitle: "添加岗位",
  editTitle: "修改岗位",
  saveBtnText: "确定",
  editBtnText: "修改",
  updateBtnText: "确定",
  addBtn: false,
  editBtn: false,
  delBtn: false,
  refreshBtn: false,
  searchBtn: false,
  emptyBtn: false,
  labelWidth: 100,
  columnBtn: false,
  column: [
    {
      label: "岗位编号",
      prop: "postId",
      search: true,
      editDisabled: true,
      rules: [
        {
          required: true,
          message: "岗位编号不能为空",
          trigger: "blur"
        },
        { min: 1, max: 20, message: "长度在 1 到 20 个字符", trigger: "blur" },
        {
          validator: validate.roleIdValidate, trigger: 'blur'
        }
      ]
    },
    {
      label: "岗位名称",
      prop: "postName",
      search: true,
      rules: [
        {
          required: true,
          message: "岗位名称不能为空",
          trigger: "blur"
        },
        { min: 1, max: 20, message: "长度在 1 到 20 个字符", trigger: "blur" },
      ]
    },
    {
      label: "岗位状态",
      prop: "status",
      type: "select",
      slot: true,
      dicData: [],
      search: true,
      searchslot: true,
      rules: [
        {
          required: true,
          message: "请选择状态",
          trigger: "blur,change"
        }
      ]
    },
    {
      label: "创建人",
      prop: "createUser",
      editDisplay: false,
      addDisplay: false
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "datetime",
      editDisplay: false,
      addDisplay: false
    },
    {
      label: "显示顺序",
      prop: "displayOrder",
      type:'number',
      hide: true,
      minRows: 0,
      maxRows: 10000,
      rules: [
        {
          required: true,
          message: "显示顺序不能为空",
          trigger: "blur change"
        },
      ]
    },
    {
      label: "岗位描述",
      prop: "postDesc",
      type: "textarea",
      hide: true,
      rules: [
        { min: 1, max: 255, message: "长度在 1 到 255 个字符", trigger: "blur" },
      ]
    }
  ]
};