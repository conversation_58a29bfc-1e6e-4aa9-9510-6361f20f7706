<template>
  <!-- 授权用户 -->
  <el-dialog
    title="选择用户"
    :visible.sync="visible"
    append-to-body
    :style="styleName"
    width="60%"
    :class="fullscreen?'yo-dialog yo-table__dialog yo-dialog--fullscreen':'yo-dialog '"
  >
    <div slot="title" class="yo-table__dialog__header">
      <span class="el-dialog__title">选择用户</span>
      <div class="yo-table__dialog__menu">
        <i
          @click="handleFullScreen"
          :class="fullscreen?'el-icon-news':'el-icon-full-screen'"
          class="el-dialog__close"
        ></i>
      </div>
    </div>
    <yo-table
      ref="crud"
      :data="userList"
      :option="option"
      :page.sync="page"
      :search.sync="search"
      @on-load="onLoad"
      @search-change="searchChange"
      @refresh-change="refresh"
      v-model="formParent"
      @selection-change="handleSelectionChange"
    >
      <!-- 自定义搜索框 -->
      <template slot="userStatSearch" slot-scope="{disabled,size}">
        <el-select
          v-model="search.userStat"
          :disabled="disabled"
          placeholder="状态"
          clearable
          :size="size"
        >
          <el-option 
          v-for="i in dict.type['yoaf_user_state']" 
          :key="i.value" 
          :label="i.label"
           :value="i.value"/>
        </el-select>
      </template>
      <!-- 自定义列 -->
      <template slot="gender" slot-scope="scope">
        <el-tag
          :type="scope.row.gender=='1'? '':'danger'"
          size="mini"
        >{{scope.row.gender=='1'? '男':'女'}}</el-tag>
      </template>
      <template slot="userStat" slot-scope="scope">
        <el-tag size="mini" effect="plain" :type="tagType(scope.row)">{{userStat(scope.row)}}</el-tag>
      </template>
      <template slot-scope="{scope}" slot="searchMenu">
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-search"
          @click.stop="handleQuery"
          v-hasPermi="['admin:posts:no:users:page']"
        >查询</el-button>
        <el-button size="mini" icon="el-icon-refresh" @click.stop="resetQuery">重置</el-button>
      </template>
    </yo-table>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :size="option.size || 'small'" @click="handleSelectUser"  v-hasPermi="['admin:posts:users:add']">保 存</el-button>
      <el-button :size="option.size || 'small'" @click="cancelSelectUser">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addRoleUserList, unocatedUserList } from "@/api/system/position";
import {
  listUser,
  addUser,
  delUser,
  delUsers,
  updateUser
} from "@/api/system/user";
export default {
  dicts: ["yoaf_user_state"],
  props: {
    // 角色编号
    postId: {
      type: [Number, String]
    }
  },
  data() {
    return {
      formParent: {},
      fullscreen: false,
      search: {},
      page: {
        pageSize: 10,
        currentPage: 1
      },
      option: {
        selection: true,
        tip:false,
        align: "center",
        card: true,
        menuAlign: "center",
        emptyBtnText: "重置",
        emptyBtnIcon: "el-icon-refresh",
        searchMenuSpan: 8,
        menu: false,
        searchBtn: false,
        emptyBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        columnBtn: false,
        refreshBtn: false,
        searchShowBtn: false,
        column: [
          {
            label: "用户名",
            prop: "userName",
            search: true
          },
          {
            label: "姓名",
            prop: "userNickname",
            search: true
          },
          {
            label: "性别",
            prop: "gender",
            slot: true
          },
          {
            label: "邮箱",
            prop: "email"
          },
          {
            label: "手机号",
            prop: "phoneNumber"
          },
          {
            label: "状态",
            prop: "userStat",
            slot: true,
            search: true,
            searchslot: true
          }
        ]
      },
      // 遮罩层
      visible: false,
      // 选中数组值
      userIds: [],
      // 授权用户数据
      userList: []
    };
  },
  computed: {
    styleName() {
      if (!this.fullscreen) {
        return { top: "15%", bottom: "5%" };
      } else {
        return { top: 0, bottom: 0, marginTop: 0 };
      }
    }
  },
  methods: {
    // 首次加载调用此方法
    onLoad(page) {
      this.getList(this.search);
    },
    // 显示弹框
    show() {
      setTimeout(() => {
        this.getList();
      }, 500);
      this.visible = true;
      this.fullscreen = false;
    },
    handleFullScreen() {
      this.fullscreen = !this.fullscreen;
    },
    // 刷新按钮
    refresh(val) {
      this.handleQuery();
    },
    // 点击搜索
    searchChange(params, done) {
      // this.$message.success(JSON.stringify(params))
      this.handleQuery();
      setTimeout(() => {
        done();
      }, 1000);
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.userIds = selection.map(item => item.userName);
    },
    // 查询表数据
    getList(params) {
      const data = { postId: this.postId, ...this.page, ...params };           
      unocatedUserList(data).then(res => {
        const data = res.data;
        this.userList = data.list;
        this.page.total = data.total;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.page.currentPage = 1;
      this.getList(this.search);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.$refs.crud.searchReset();
      // this.handleQuery();
    },
    /** 选择用户操作 */
    handleSelectUser() {
      const postId = this.postId;
      let userIds = [];
      this.userIds.forEach(item => {
        userIds.push({ userName: item });
      });
      if (userIds == "") {
        this.$modal.msgError("请选择要分配的用户");
        return;
      }
      let query = {
        postId: postId,
        userList: userIds
      };
      addRoleUserList(query).then(res => {
        if (res.code == 0) {
          this.$message.success(res.message);
          this.visible = false;
          this.$emit("ok");
        }
      });
    },
    cancelSelectUser() {
      this.visible = false;
      this.fullscreen = false;
    },
    userStat(row) {
      switch (row.userStat) {
        case "01":
          return "启用";
        case "02":
          return "停用";
        case "03":
          return "注销";
        case "04":
          return "锁定";
        default:
          break;
      }
    },
    //tag样式
    tagType(row) {
      switch (row.userStat) {
        case "01":
          return "success";
        case "02":
          return "danger";
        case "03":
          return "info";
        case "04":
          return "warning";
        default:
          break;
      }
    }
  }
};
</script>
<style scoped>
.yo-table__dialog__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.yo-table__dialog__menu {
  padding-right: 20px;
}
</style>