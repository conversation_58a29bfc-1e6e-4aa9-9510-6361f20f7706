<template>
    <div class="app-container">
        <yo-table
            v-loading="loading"
            :data="data"
            :option="bwOption"
            ref="crud"
            :page.sync="page"
            :search.sync="search"
            @on-load="onLoad"
            @search-change="searchChange"
            v-model="formParent"
        >
            <template slot-scope="{ scope }" slot="searchMenu">
                <el-button
                    size="mini"
                    type="primary"
                    icon="el-icon-search"
                    @click.stop="handleQuery"
                >
                    查询
                </el-button>
                <el-button
                    size="mini"
                    icon="el-icon-refresh"
                    @click.stop="resetQuery()"
                >
                    重置
                </el-button>
                <!-- <el-button
                    size="mini"
                    type="primary"
                    icon="el-icon-download"
                    @click.stop="exportExample"
                >
                    生成模板
                </el-button> -->
                <input
                    id="fileslist"
                    v-show="false"
                    type="file"
                    accept=".xls"
                    ref="fileRef"
                    @change="fileChange"
                />
            </template>
            <template slot-scope="{ row, size, type, index }" slot="menu">
                <el-button
                    v-if="row.state == '1' && row.pstate != '1'"
                    :size="size"
                    :type="type"
                    icon="el-icon-edit"
                    @click.stop="openModal(row, index, false)"
                >
                    修改
                </el-button>
                <el-button
                    v-if="row.state == '1' && row.pstate != '1'"
                    :size="size"
                    :type="type"
                    icon="el-icon-upload2"
                    @click.stop="handleImport(row, index)"
                >
                    导入
                </el-button>
                <el-button
                    v-if="row.state == '1' && row.pstate != '1'"
                    :size="size"
                    :type="type"
                    icon="el-icon-download"
                    @click="handleSub(row)"
                >
                    提交
                </el-button>
                <el-button
                    v-if="row.state == '1' && row.pstate != '1'"
                    :size="size"
                    :type="type"
                    icon="el-icon-download"
                    @click="handleExp(row)"
                >
                    生成模板
                </el-button>
                <el-button
                    v-if="row.state == '2' || row.pstate == '1'"
                    :size="size"
                    :type="type"
                    icon="el-icon-search"
                    @click="openModal(row, index, true)"
                >
                    查看
                </el-button>
            </template>
        </yo-table>

        <customModal ref="customModalRef"></customModal>
    </div>
</template>
<script>
import { list, downloadExample, importGz, changeState } from '@/api/jxkh/gzwh';
import bwOption from './infoData/gzwhOption';
import customModal from './modal/editIndex';

export default {
    dicts: ['jxkh_salary_batch_type', 'jxkh_salary_batch_state'],
    components: { customModal },
    data() {
        return {
            showContainer: true,
            loading: true,
            formParent: {},
            form: {},
            batchId: '',
            exportSearch: {},
            search: {},
            page: {
                pageSize: 10,
                currentPage: 1,
            },
            data: [],
            bwOption: bwOption,
            multiple: false,
            rowInfo: {},
            defaultValue: Number(new Date().getFullYear()),
        };
    },
    created() {
        this.search.state = '1';
        /** *添加字典项数据*/
        this.updateDictData(
            this.bwOption.column,
            'type',
            this.dict.type['jxkh_salary_batch_type']
        );
        this.updateDictData(
            this.bwOption.column,
            'state',
            this.dict.type['jxkh_salary_batch_state']
        );
    },
    methods: {
        // 首次加载调用此方法
        onLoad(page) {
            this.getList(this.search);
        },
        showDetails() {
            this.showContainer = true;
        },
        // 更新字典数据
        updateDictData(option, key, data) {
            let column = this.findObject(option, key);
            column.dicData = data;
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.page.currentPage = 1;
            let date = new Date(this.search.year);
            let year = date.getFullYear().toString();
            if (year == '1970' || year == 'NaN') {
                year = '';
            }
            this.search.year = year;
            let date2 = new Date(this.search.yearMonth);
            let year2 = date2.getFullYear();
            let month = ('0' + (date2.getMonth() + 1)).slice(-2);
            if (year2 == '1970' && month == '01') {
                month = '';
            }
            if (month == 'aN') {
                month = '';
            }
            this.search.yearMonth = month;
            this.getList(this.search);
            this.exportSearch = this.search;
        },
        /** 分页查询列表 */
        async getList(query) {
            const params = { ...query, ...this.page };
            if (
                params.year == null ||
                params.year == '' ||
                params.year == undefined
            ) {
                params.year = this.defaultValue;
            }
            this.loading = true;
            let response = await list(params);
            if (response.code == 0) {
                response.data.list.map(item => {
                    if (item.status == '1') {
                        item.switch = true;
                    } else {
                        item.switch = false;
                    }
                });
                setTimeout(() => {
                    this.data = response.data.list;
                    this.page.total = response.data.total;
                    this.loading = false;
                }, 1000);
            }
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.$refs.crud.searchReset();
        },
        // 搜索按钮
        searchChange(params, done) {
            this.handleQuery();
            setTimeout(() => {
                done();
            }, 1000);
        },
        /*下载已存在模板*/
        handleExp(row, index) {
            downloadExample(row).then(res => {
                this.handleExportData(res);
            });
        },
        handleEdit(row, index) {
            this.formParent = row;
            this.$refs.crud.rowEdit(row, index);
        },
        handleSub(row) {
            const batchId = row.id;
            this.$modal
                .confirm('是否确认是否提交当前批次数据，提交后将不能再修改？')
                .then(() => {
                    this.handleSubs(batchId, changeState);
                })
                .catch(() => {});
        },

        async handleSubs(id, delFn) {
            let res = await delFn(id);
            if (res.code == 0) {
                this.getList();
                this.$message.success(res.message);
                //this.$refs.crud.toggleSelection();
            }
        },
        /** 下载模板 */
        exportExample() {
            if (this.search.type == null || this.search.type == '') {
                this.$modal.msgWarning('请选择下载的绩效类型！');
                return;
            } else {
                if (this.search.type == '1') {
                    //月度
                    if (this.search.year == '') {
                        this.$modal.msgWarning('请选择下载的年度！');
                        return;
                    }
                    if (this.search.yearMonth == '') {
                        this.$modal.msgWarning('请选择下载的月度！');
                        return;
                    }
                }
                if (this.search.type == '2') {
                    //季度
                    if (this.search.year == '') {
                        this.$modal.msgWarning('请选择下载的年度！');
                        return;
                    }
                    if (this.search.yearQuar == '') {
                        this.$modal.msgWarning('请选择下载的季度！');
                        return;
                    }
                }
                if (this.search.type == '3') {
                    //年度
                    if (this.search.year == '') {
                        this.$modal.msgWarning('请选择下载的年度！');
                        return;
                    }
                }
                let date = new Date(this.search.year);
                let year = date.getFullYear().toString();
                if (year == '1970') {
                    year = '';
                }
                this.search.year = year;
                let date2 = new Date(this.search.yearMonth);
                let year2 = date2.getFullYear();
                let month = ('0' + (date2.getMonth() + 1)).slice(-2);
                if (year2 == '1970' && month == '01') {
                    month = '';
                }
                if (month == 'aN') {
                    month = '';
                }
                this.search.yearMonth = month;

                downloadExample(this.search)
                    .then(
                        res => {
                            let ea = this.handleExportData(res);
                            console.log(ea + '--ea');
                        },
                        error => {
                            //this.$message.error("当前批次未上次不可下载");
                            console.log(error.response);
                            console.log(error);
                        }
                    )
                    .catch(function (error) {
                        if (error.response) {
                            console.log(error.response);
                        } else if (error.request) {
                            console.log(error);
                        } else {
                            console.log('error---->' + error);
                        }
                    });
            }
        },
        /** 导出结果，处理返回的流文件 */
        handleExportData(res) {
            console.log('res:', res);
            if (res.status == 200 && res.config.responseType == 'blob') {
                console.log(2);

                if (!res) return;
                let data = res.data;
                let filename = res.headers['content-disposition'].split('=')[1];
                let _filename = decodeURI(filename);
                const link = document.createElement('a');
                //创建 Blob对象 可以存储二进制文件
                let blob = new Blob([data], { type: 'application/x-excel' });
                link.style.display = 'none';
                link.href = URL.createObjectURL(blob);
                link.download = _filename;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            } else {
                this.$modal.msgWarning('当前批次未上传不可下载！');
                console.log('当前批次未上传不可下载');
            }
        },
        fileChange(event) {
            if (this.batchId == '') {
                this.$modal.msgWarning('请选择导入的批次！');
                return;
            }
            let file = document.querySelector('#fileslist').files[0];
            let formData = new FormData();
            formData.append('file', file);
            formData.append('batchId', this.batchId);
            formData.append('batchCode', this.rowInfo.batchCode);
            importGz(formData).then(res => {
                if (res.code == 0) {
                    this.$message.success(res.message);
                    this.getList();
                }
            });
            // 清除文件，防止下次上传相同文件无反应
            event.target.value = null;
        },
        /** 导入按钮操作 */
        handleImport(row) {
            this.rowInfo = row;
            this.batchId = row.id;
            this.$refs.fileRef.click();
        },
        //tag样式
        tagType(row) {
            switch (row.status) {
                case '1':
                    return 'success';
                case '2':
                    return 'danger';
                case '03':
                    return 'info';
                case '04':
                    return 'warning';
                default:
                    break;
            }
        },
        openModal(row, index, isDetail) {
            this.$nextTick(() => {
                this.$refs.customModalRef.openModal(row, index, isDetail);
            });
        },
    },
};
</script>
