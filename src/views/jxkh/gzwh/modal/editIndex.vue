<template>
    <el-dialog
        :fullscreen="false"
        :destroy-on-close="true"
        :title="titleWord"
        :visible.sync="dialogVisible"
        width="90%"
        :close-on-click-modal="isDetail"
        :close-on-press-escape="isDetail"
    >
        <!-- <div style="height: 600px; overflow: scroll"> -->
        <el-row :gutter="20" style="margin-bottom: 20px">
            <el-col :span="6">
                <el-input
                    v-model="search.empName"
                    placeholder="员工姓名（输入关键字模糊查询）"
                ></el-input>
            </el-col>
            <el-col :span="12">
                <el-button
                    type="primary"
                    icon="el-icon-search"
                    @click="searchData"
                >
                    查 询
                </el-button>
                <el-button @click="resetQuery" icon="el-icon-refresh">
                    重 置
                </el-button>
                <el-button
                    v-if="!isDetail"
                    type="primary"
                    icon="el-icon-plus"
                    plain
                    @click.stop="handleAdd()"
                >
                    新增人员
                </el-button>
            </el-col>
        </el-row>

        <el-table
            :header-cell-style="{ 'text-align': 'center' }"
            :cell-style="{ textAlign: 'center' }"
            v-loading="loading"
            :data="tableDatas"
            style="width: 100%"
        >
            <el-table-column
                min-width="150px"
                label="绩效年月"
                key="jxny"
                prop="jxny"
            ></el-table-column>
            <el-table-column
                min-width="150px"
                label="机构编号"
                key="jgbh"
                prop="jgbh"
            ></el-table-column>

            <el-table-column
                v-for="(header, index) in dynamicHeader"
                :key="index"
                :prop="header.prop"
                :label="header.label"
            >
                <el-table-column label="本期预算" key="bqys" prop="bqys">
                    {{ header.bqys }}
                </el-table-column>
                <el-table-column label="本期使用" key="bqsy" prop="bqsy">
                    {{ header.bqsy }}
                </el-table-column>
                <el-table-column label="本期结余" key="bqjy" prop="bqjy">
                    {{ header.bqjy }}
                </el-table-column>
            </el-table-column>
        </el-table>
        <!-- 以下是可编辑的表单数据 -->
        <el-table
            :header-cell-style="{ textAlign: 'center' }"
            :cell-style="{ textAlign: 'center' }"
            v-loading="loading"
            :data="tableData"
            style="width: 100%"
            :border="true"
        >
            <el-table-column
                fixed="left"
                v-if="tableData.length > 0"
                :width="80"
                label="序号"
            >
                <template slot-scope="scope">
                    {{
                        page.pageSize * (page.currentPage - 1) +
                        scope.$index +
                        1
                    }}
                </template>
            </el-table-column>
            <el-table-column
                fixed="left"
                v-if="tableData.length > 0"
                key="header.prop"
                prop="empId"
                width="140"
                label="用户编号"
            ></el-table-column>
            <el-table-column
                fixed="left"
                v-if="tableData.length > 0"
                key="header.prop"
                prop="empName"
                label="姓名"
            ></el-table-column>

            <el-table-column
                v-if="tableData.length > 0"
                key="header.prop"
                prop="isLeader"
                label="是否领导"
            ></el-table-column>
            <el-table-column
                v-for="(item, index) in dynamicHeader"
                :key="index"
                :prop="item.prop"
                :label="item.label"
            >
                <!-- @blur="focusM" -->
                <template slot-scope="scope">
                    <el-input
                        @blur="focusM(scope.row)"
                        type="number"
                        v-if="!isDetail"
                        controls-position="right"
                        v-model="scope.row[item.prop]"
                        @change="handleEdit(scope.$index, scope.row)"
                    ></el-input>

                    <span v-else>
                        {{ scope.row[item.prop] }}
                    </span>
                </template>
            </el-table-column>

            <el-table-column
                v-if="rowInfo.state == '1' && rowInfo.pstate != '1'"
                fixed="right"
                label="操作"
                width="100"
            >
                <template slot-scope="scope">
                    <el-button
                        @click="handleDelete(scope.$index, scope.row)"
                        type="text"
                        size="small"
                        icon="el-icon-delete"
                    >
                        删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <span slot="footer" class="dialog-footer">
            <el-pagination
                background
                style="margin-bottom: 20px"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page.sync="page.currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="page.pageSize"
                layout="total,sizes, prev, pager, next"
                :total="total"
            ></el-pagination>

            <el-button v-if="!isDetail" @click="dialogVisible = false">
                关 闭
            </el-button>
            <!-- <el-button v-if="!isDetail" type="primary" @click="sureBtn">
                保 存
            </el-button> -->
        </span>

        <yo-table
            v-show="false"
            style="width: 0px; height: 0px"
            :option="tableOption"
            ref="crud"
            v-model="formParent"
            @row-save="rowSave"
        ></yo-table>
        <!-- </div> -->
    </el-dialog>
</template>

<script>
import { list, batchAdd } from '@/api/jxkh/jxkh';
import { addUserImport } from '@/api/system/userimport';
import tableOption from './../infoData/tableoption.js';
import { delEmpByBatchIdAndEmpId } from '@/api/jxkh/gzwh';
export default {
    data() {
        return {
            search: {
                empName: '',
            },
            formParent: {},
            tableOption: tableOption,
            tableData: [],
            tableHeaders: [],
            dynamicHeader: [],
            dialogVisible: false,
            loading: false,
            page: {
                pageSize: 10,
                currentPage: 1,
            },

            listParams: [],
            total: 0,
            isDetail: false,
            tableDatas: [],
            test: '1000',
            test2: '200',
            test3: '800',
            titleWord: '工资维护',
            rowInfo: {},
        };
    },
    methods: {
        onLoad(page) {
            this.getList(this.search);
        },
        handleEdit(index, row) {
            // 编辑操作
        },
        handleDelete(index, row) {
            console.log(row);
            // 删除操作
            const batchId = this.search.batchId;
            const empName = row.empName;
            this.$confirm(`是否确认删除姓名为" ${empName} "的数据项?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                // this.$modal
                //   .confirm('是否确认删除角色编号为"' + roleIds + '"的数据项？')
                .then(async () => {
                    // 删除

                    //delEmpByBatchIdAndEmpId
                    let res = await delEmpByBatchIdAndEmpId(
                        batchId,
                        row.userId
                    );
                    if (res.code == 0) {
                        this.$message.success(res.message);
                        this.getList(this.search);
                    }

                    //this.handleDelRole(id, delSalarySub);
                })
                .catch(() => {});
        },
        /** 分页查询列表 */
        async getList(query) {
            const params = { ...query, ...this.page };
            this.loading = true;
            let response = await list(params);
            if (
                response.data.tableData != undefined &&
                response.data.tableData != null
            ) {
                this.dynamicHeader = response.data.dynamicHeader;
                this.tableData = response.data.tableData.list;
                this.tableDatas = [response.data.jxfpzeData];
                this.tableDatas = response.data.otherInfo;
                this.total = response.data.tableData.total;
                this.page.currentPage = response.data.tableData.pageNum;
            } else {
                this.tableData = [];
            }
            this.loading = false;
        },
        handleSizeChange(val) {
            this.page.pageSize = val;
            this.page.currentPage = 1;
            this.getList(this.search);
        },
        handleCurrentChange(val) {
            this.page.currentPage = val;
            this.getList(this.search);
        },
        openModal(row, index, isDetail) {
            this.rowInfo = row;
            this.titleWord =
                row.year +
                '年' +
                row.yearMonth +
                '月工资维护（' +
                row.batchCode +
                '）';
            this.search.batchId = row.id;
            this.search.batchCode = row.batchCode;
            this.dialogVisible = true;
            this.isDetail = isDetail;
            this.$nextTick(() => {
                this.resetQuery();
            });
        },
        handleEdit(index, row) {
            // 处理编辑逻辑，例如更新数据库等
            console.log(index, row.index);
        },
        async sureBtn(row) {
            // this.dialogVisible = false;
            this.loading = true;
            const res = await batchAdd(row);
            if (res.code == 0) {
                this.loading = false;
                this.$message.success(res.message);
                this.getList(this.search);
            }
            // else {
            //     this.$message.error(res.message);
            // }

            this.loading = false;
        },
        focusM(row) {
            console.log(row);
            this.sureBtn(row);
        },
        searchData() {
            this.getList(this.search);
        },
        resetQuery() {
            //重置
            this.search.empName = '';
            this.page = {
                pageSize: 10,
                currentPage: 1,
            };
            this.getList(this.search);
        },
        // 新增表单保存
        rowSave(form, done, loading) {
            //console.log(this.form,'我是新增.....');

            form.batchId = this.search.batchId;
            form.batchCode = this.rowInfo.batchCode;
            addUserImport(form)
                .then(response => {
                    if (response.code == 0) {
                        this.$modal.msgSuccess(response.message);
                        done();
                        this.getList(this.search);
                    }
                })
                .catch(error => {
                    loading();
                });
        },
        /** 新增按钮操作 */
        handleAdd() {
            let uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
                /[xy]/g,
                function (c) {
                    let r = (Math.random() * 16) | 0,
                        v = c === 'x' ? r : (r & 0x3) | 0x8;
                    return v.toString(16);
                }
            );
            (this.formParent = {
                id: uuid,
            }),
                this.$refs.crud.rowAdd();
        },
    },
};
</script>
<style>
.el-input-number .el-input__inner {
    text-align: center;
    width: 200px;
}

.div-fixed {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 5rem;
    background-color: #f4f4f4; /* 示例背景色 */
    z-index: 1000; /* 确保 div 在页面顶层 */
}

.demo-table-expand {
    font-size: 0;
}
.demo-table-expand label {
    width: 90px;
    color: #99a9bf;
}
.demo-table-expand .el-form-item {
    margin-right: 0;
    margin-bottom: 0;
    width: 50%;
}

.el-table .success-row {
    background: oldlace;
}
</style>
