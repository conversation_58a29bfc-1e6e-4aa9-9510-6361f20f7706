<template>
    <el-dialog
        :fullscreen="false"
        :destroy-on-close="true"
        title="绩效考核"
        :visible.sync="dialogVisible"
        width="90%"
        :close-on-click-modal="isDetail"
        :close-on-press-escape="isDetail"
    >
        <!-- <div style="height: 600px; overflow: scroll"> -->
        <el-row :gutter="20" style="margin-bottom: 20px">
            <el-col :span="6">
                <el-input
                    v-model="search.empName"
                    placeholder="员工姓名（输入关键字模糊查询）"
                ></el-input>
            </el-col>
            <el-col :span="6">
                <el-button type="primary" @click="searchData">查 询</el-button>
                <el-button @click="resetQuery">重 置</el-button>
                <el-button
                    v-if="!isDetail"
                    type="primary"
                    icon="el-icon-plus"
                    plain
                    @click.stop="handleAdd()"
                >
                    新增人员
                </el-button>
            </el-col>
        </el-row>

        <el-table
            :header-cell-style="{ 'text-align': 'center' }"
            v-loading="loading"
            :data="tableDatas"
            style="width: 100%"
        >
            <el-table-column
                min-width="150px"
                label="绩效年月"
                key="jxny"
                prop="jxny"
            ></el-table-column>
            <el-table-column
                min-width="150px"
                label="机构编号"
                key="jgbh"
                prop="jgbh"
            ></el-table-column>

            <el-table-column
                v-for="(header, index) in tableHeaders"
                :key="header.prop"
                :prop="header.prop"
                :label="header.label"
            >
                <el-table-column label="本期预算" key="bqys" prop="bqys">
                    {{ header.zesx }}
                </el-table-column>
                <el-table-column label="本期使用" key="bqsy" prop="bqsy">
                    {{ header.bqsy }}
                </el-table-column>
                <el-table-column label="本期结余" key="bqjy" prop="bqjy">
                    {{ header.bqjy }}
                </el-table-column>
            </el-table-column>
        </el-table>
        <!-- 以下是可编辑的表单数据 -->
        <el-table
            :header-cell-style="{ 'text-align': 'center' }"
            v-loading="loading"
            :data="tableData"
            style="width: 100%"
        >
            <el-table-column
                fixed="left"
                v-if="tableData.length > 0"
                width="120"
                key="header.prop"
                prop="empName"
                label="姓名"
            ></el-table-column>
            <!--      <el-table-column label="员工编号" prop="empId"></el-table-column>

            <el-table-column label="发放机构" prop="empId">
                {{ '厦门分行' }}
            </el-table-column> -->

            <el-table-column
                v-for="(header, index) in tableHeaders"
                :key="header.prop"
                :prop="header.prop"
                :label="header.label"
                min-width="120px"
            >
                <template slot-scope="scope">
                    <el-input
                        @blur="focusM"
                        type="number"
                        v-if="!isDetail"
                        controls-position="right"
                        v-model="
                            listParams[
                                tableHeaders.length * scope.$index + index
                            ].salary
                        "
                        @change="handleEdit(scope.$index, scope.row)"
                    ></el-input>

                    <span v-else>
                        {{
                            listParams[
                                tableHeaders.length * scope.$index + index
                            ].salary
                        }}
                    </span>
                </template>
            </el-table-column>

            <el-table-column fixed="right" label="操作" width="100">
                <template slot-scope="scope">
                    <el-button
                        @click="handleDelete(scope.$index, scope.row)"
                        type="text"
                        size="small"
                        icon="el-icon-delete"
                    >
                        删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <span slot="footer" class="dialog-footer">
            <el-pagination
                background
                style="margin-bottom: 20px"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page.sync="page.currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="page.pageSize"
                layout="total,sizes, prev, pager, next"
                :total="total"
            ></el-pagination>

            <el-button v-if="!isDetail" @click="dialogVisible = false">
                关 闭
            </el-button>
            <!-- <el-button v-if="!isDetail" type="primary" @click="sureBtn">
                保 存
            </el-button> -->
        </span>

        <yo-table
            v-show="false"
            style="width: 0px; height: 0px"
            :option="tableOption"
            ref="crud"
            v-model="formParent"
            @row-save="rowSave"
        >
            <!-- 自定义左侧操作栏 -->
            <!--              <template slot-scope="{size}" slot="menuLeft">
                <el-button
                  type="primary"
                  icon="el-icon-plus"
                  :size="size"
                  plain
                  @click.stop="handleAdd()"
                >新增</el-button>
              </template>-->
        </yo-table>
        <!-- </div> -->
    </el-dialog>
</template>

<script>
import { list, batchAdd } from '@/api/jxkh/jxkh';
import { addUserImport } from '@/api/system/userimport';
import tableOption from './../infoData/tableoption.js';
import { delEmpByBatchIdAndEmpId } from '@/api/jxkh/gzwh';
export default {
    data() {
        return {
            search: {
                empName: '',
            },
            formParent: {},
            tableOption: tableOption,
            tableData: [],
            tableHeaders: [],
            dialogVisible: false,
            loading: false,
            page: {
                pageSize: 10,
                currentPage: 1,
            },
            listParams: [],
            total: 0,
            isDetail: false,
            tableDatas: [],
            test: '1000',
            test2: '200',
            test3: '800',
        };
    },
    methods: {
        onLoad(page) {
            this.getList(this.search);
        },
        handleEdit(index, row) {
            // 编辑操作
        },
        handleDelete(index, row) {
            // 删除操作
            console.log(row);
            console.log(index + 'index');
            const batchId = this.search.batchId;
            const empName = row.empName;
            this.$confirm(`是否确认删除姓名为" ${empName} "的数据项?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                // this.$modal
                //   .confirm('是否确认删除角色编号为"' + roleIds + '"的数据项？')
                .then(async () => {
                    // 删除

                    //delEmpByBatchIdAndEmpId
                    let res = await delEmpByBatchIdAndEmpId(batchId, row.empId);
                    if (res.code == 0) {
                        this.$message.success(res.message);
                        this.getList(this.search);
                    }

                    //this.handleDelRole(id, delSalarySub);
                })
                .catch(() => {});
        },
        /** 分页查询列表 */
        async getList(query) {
            const params = { ...query, ...this.page };
            this.loading = true;
            let response = await list(params);

            if (response.data.data != undefined) {
                this.total = response.data.data.total;
                this.tableHeaders = response.data.head;
                this.tableData = response.data.data.list;
                let allData = response.data.allData;
                this.tableDatas = [response.data.jxfpzeData];

                this.listParams = [];
                this.tableData.forEach(item => {
                    this.tableHeaders.forEach(ss => {
                        let a = allData.filter(
                            all => all.empId == item.empId && all.bh == ss.prop
                        )[0];
                        this.listParams.push({
                            empId: item.empId,
                            bh: ss.prop,
                            salary: a.salary,
                            batchId: params.batchId,
                            id: a.id,
                        });
                    });
                });
            } else {
                this.tableData = [];
                this.tableHeaders = [];
            }

            this.loading = false;
        },
        handleSizeChange(val) {
            this.page.pageSize = val;
            this.page.currentPage = 1;
            this.getList(this.search);
        },
        handleCurrentChange(val) {
            this.page.currentPage = val;
            this.getList(this.search);
        },
        openModal(row, index, isDetail) {
            this.search.batchId = row.id;
            this.dialogVisible = true;
            this.isDetail = isDetail;
            this.$nextTick(() => {
                this.resetQuery();
            });
        },
        handleEdit(index, row) {
            // 处理编辑逻辑，例如更新数据库等
            console.log(index, row.index);
        },
        async sureBtn() {
            // this.dialogVisible = false;
            this.loading = true;
            const res = await batchAdd(this.listParams);
            if (res.code == 0) {
                this.$message.success(res.message);
                this.getList(this.search);
            }
            // else {
            //     this.$message.error(res.message);
            // }

            this.loading = false;
        },
        searchData() {
            this.getList(this.search);
        },
        resetQuery() {
            //重置
            this.search.empName = '';
            this.page = {
                pageSize: 10,
                currentPage: 1,
            };
            this.getList(this.search);
        },
        // 新增表单保存
        rowSave(form, done, loading) {
            //console.log(this.form,'我是新增.....');
            form.batchId = this.search.batchId;
            addUserImport(form)
                .then(response => {
                    if (response.code == 0) {
                        this.$modal.msgSuccess(response.message);
                        done();
                        this.getList(this.search);
                    }
                })
                .catch(error => {
                    loading();
                });
        },
        /** 新增按钮操作 */
        handleAdd() {
            let uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
                /[xy]/g,
                function (c) {
                    let r = (Math.random() * 16) | 0,
                        v = c === 'x' ? r : (r & 0x3) | 0x8;
                    return v.toString(16);
                }
            );
            (this.formParent = {
                id: uuid,
            }),
                this.$refs.crud.rowAdd();
        },
        focusM() {
            this.sureBtn();
        },
    },
};
</script>
<style>
.el-input-number .el-input__inner {
    text-align: center;
    width: 200px;
}

.div-fixed {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 5rem;
    background-color: #f4f4f4; /* 示例背景色 */
    z-index: 1000; /* 确保 div 在页面顶层 */
}
</style>
