<template>
    <div>
        <el-dialog
            :modal="false"
            :modal-append-to-body="false"
            :title="tipTitle"
            :visible.sync="dialogVisible"
            width="30%"
            :before-close="handleClose"
            :destroy-on-close="true"
            :fullscreen="true"
        >
            <el-form :inline="true" class="demo-form-inline">
                <el-form-item>
                    <el-button
                        type="primary"
                        icon="el-icon-download"
                        @click.stop="handleExp()"
                        :size="size"
                        plain
                    >
                        下载模板
                    </el-button>

                    <el-button
                        type="primary"
                        icon="el-icon-coin"
                        @click.stop="handleImport"
                    >
                        导入
                    </el-button>
                </el-form-item>
            </el-form>
            <input
                id="fileslistFlag"
                v-show="false"
                type="file"
                accept=".xls"
                ref="fileRef"
                @change="fileChange"
            />
            <el-table
                :data="tableData"
                height="500"
                style="width: 100%"
                v-loading="loading"
            >
                <el-table-column
                    prop="instName"
                    label="机构名称"
                    width="180"
                ></el-table-column>
                <el-table-column
                    prop="deptId"
                    label="机构编号"
                    width="180"
                ></el-table-column>

                <el-table-column
                    v-for="column in columnList"
                    :prop="column.bh"
                    :label="column.name"
                    width="180"
                >
                    <template slot-scope="{ row, size, type, index }">
                        <el-input
                            type="number"
                            v-model="formDataList[row.deptId + '_' + column.bh]"
                        ></el-input>
                    </template>
                </el-table-column>
            </el-table>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关 闭</el-button>
                <el-button type="primary" @click="sureBtn">保 存</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import {
    getSettingJxze,
    addBatch,
    importEx,
    downloadExample,
} from '@/api/jxkh/jxfpze';

export default {
    data() {
        return {
            dialogVisible: false,
            tableData: [],
            columnList: [],
            rowList: [],
            formData: [],
            fpzeList: [],
            formDataList: {},
            tipTitle: '',
            search: {
                year: '',
                yearMonth: '',
            },
            rowInfo: {},
            loading: true,
        };
    },
    methods: {
        async getDataList() {
            this.loading = true;
            const data = await getSettingJxze(this.search);
            this.columnList = data.data.column;
            this.tableData = data.data.row;
            // 生成一个对象
            this.formData = [];
            let formDataList = {};
            this.fpzeList = data.data.fpze;

            let zeDx = {};
            this.fpzeList.forEach(item => {
                zeDx[item.tempName] = item.zesx;
            });
            this.loading = false;
            this.tableData.forEach(row => {
                this.columnList.forEach(item => {
                    // this.formData.push(row.deptId + '_' + item.bh);
                    formDataList[row.deptId + '_' + item.bh] =
                        zeDx[row.deptId + '_' + item.bh];
                });
            });

            this.formDataList = formDataList;
        },
        handleClose(done) {
            this.dialogVisible = false;
            // done();
        },
        openModal(row, index, isDetail) {
            this.search.year = row.year;
            this.search.yearMonth = row.yearMonth;
            this.search.yearQuar = '';
            this.search.deptId = '';
            this.search.batchCode = row.batchCode;
            this.rowInfo = row;
            this.tipTitle =
                row.year +
                '年' +
                row.yearMonth +
                '月机构薪资额度维护（' +
                row.batchCode +
                '）';
            this.getDataList();
            this.dialogVisible = true;
        },
        async sureBtn() {
            this.loading = true;
            this.formDataList.year = this.rowInfo.year;
            this.formDataList.yearMonth = this.rowInfo.yearMonth;
            this.formDataList.batchCode = this.rowInfo.batchCode;
            const result = await addBatch(this.formDataList);
            if (result.code == 0) {
                this.$message.success('保存成功！');
                this.getDataList();
                this.loading = false;
            }
        },
        /** 导入按钮操作 */

        handleExp() {
            let params = {
                batchCode: this.rowInfo.batchCode,
            };

            downloadExample(params)
                .then(res => {
                    this.handleExportData(res);
                })
                .catch(function (error) {
                    if (error.response) {
                        console.log(error.response);
                    } else if (error.request) {
                        console.log(error.request);
                    } else {
                        console.log(error.message);
                    }
                    console.log(error);
                });
        },
        /** 导出结果，处理返回的流文件 */
        handleExportData(res) {
            if (!res) return;
            let data = res.data;
            let filename = res.headers['content-disposition'].split('=')[1];
            let _filename = decodeURI(filename);
            const link = document.createElement('a');
            //创建 Blob对象 可以存储二进制文件
            let blob = new Blob([data], { type: 'application/x-excel' });
            link.style.display = 'none';
            link.href = URL.createObjectURL(blob);
            link.download = _filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        },

        handleImport() {
            this.$refs.fileRef.click();
        },
        fileChange(event) {
            let file = document.querySelector('#fileslistFlag').files[0];
            let formData = new FormData();
            formData.append('file', file);
            formData.append('type', this.search.type);
            formData.append('year', this.search.year);
            formData.append('yearQuar', this.search.yearQuar);
            formData.append('yearMonth', this.search.yearMonth);
            formData.append('batchCode', this.search.batchCode);
            importEx(formData).then(res => {
                if (res.code == 0) {
                    this.$message.success(res.message);
                    this.getDataList();
                }
            });
            // 清除文件，防止下次上传相同文件无反应
            event.target.value = null;
        },
    },
};
</script>
