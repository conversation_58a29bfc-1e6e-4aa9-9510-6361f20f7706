<template>
    <el-dialog
        :title="pageTitle"
        :visible.sync="dialogVisible"
        width="90%"
        :before-close="handleClose"
        :close-on-press-escape="false"
    >
        <el-form :inline="true" :model="formInline" class="demo-form-inline">
            <el-form-item label="人员编号">
                <el-input
                    v-model="formInline.userCode"
                    placeholder="输入关键字模糊查询"
                ></el-input>
            </el-form-item>
            <el-form-item label="人员姓名">
                <el-input
                    v-model="formInline.userName"
                    placeholder="输入关键字模糊查询"
                ></el-input>
            </el-form-item>
            <el-form-item label="机构编号">
                <el-input
                    v-model="formInline.deptId"
                    placeholder="输入机构编号"
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-button
                    type="primary"
                    icon="el-icon-search"
                    @click="onSubmit"
                >
                    查询
                </el-button>
                <el-button
                    type="default"
                    icon="el-icon-refresh"
                    @click="resetF"
                >
                    重置
                </el-button>
            </el-form-item>
        </el-form>

        <el-form :inline="true" class="demo-form-inline">
            <el-form-item>
                <el-button
                    icon="el-icon-set-up"
                    type="primary"
                    @click="openSettingEd"
                >
                    机构薪资额度维护
                </el-button>
                <el-button
                    type="primary"
                    icon="el-icon-plus"
                    @click.stop="handleAdd1()"
                >
                    新增
                </el-button>
            </el-form-item>
        </el-form>
        <el-table
            ref="tableList"
            :data="tableData"
            style="width: 100%"
            :row-class-name="'success-row'"
            :row-key="getRowKey"
            :expand-row-keys="expandedRows"
            v-loading="loading"
            @row-click="rowClick"
            @expand-change="handleExpandChange"
            :default-expand-all="defaultFlag"
        >
            <el-table-column type="expand">
                <template slot-scope="{ row, size, type, index }">
                    <yo-table
                        @header-click="clickFun"
                        v-loading="loading"
                        :data="row.userList"
                        :option="tableOption"
                        ref="crud"
                        :search.sync="search"
                        @search-change="searchChange"
                        @row-save="rowSave"
                        @row-update="rowUpdate"
                        v-model="formParent"
                    >
                        <template slot-scope="{ scope }" slot="searchMenu">
                            <el-button
                                size="mini"
                                type="primary"
                                icon="el-icon-search"
                                @click.stop="handleQuery"
                            >
                                查询
                            </el-button>

                            <el-button
                                size="mini"
                                icon="el-icon-refresh"
                                @click.stop="resetQuery()"
                            >
                                重置
                            </el-button>
                        </template>
                        <!-- 自定义左侧操作栏 -->
                        <template
                            slot-scope="{ size }"
                            slot="menuLeft"
                        ></template>
                        <!-- 自定义操作按钮 -->
                        <template
                            slot-scope="{ row, size, type, index }"
                            slot="menu"
                        >
                            <el-button
                                :type="type"
                                icon="el-icon-edit"
                                @click.stop="handleUpdate(row, index)"
                            >
                                修改
                            </el-button>
                            <el-button
                                :type="type"
                                icon="el-icon-delete"
                                @click="handleDelete(row)"
                            >
                                删除
                            </el-button>
                        </template>

                        <!-- 自定义列 -->
                        <template slot="idCard" slot-scope="scope">
                            <div>
                                <span>{{ scope.row.idCard }}</span>
                                <el-button
                                    v-if="scope.row.idCard"
                                    type="text"
                                    icon="el-icon-view"
                                    @click="viewUserInfo(scope.row, 'idCard')"
                                ></el-button>
                            </div>
                        </template>
                        <template slot="phone" slot-scope="scope">
                            <div>
                                <span>{{ scope.row.phone }}</span>
                                <el-button
                                    v-if="scope.row.phone"
                                    type="text"
                                    icon="el-icon-view"
                                    @click="viewUserInfo(scope.row, 'phone')"
                                ></el-button>
                            </div>
                        </template>

                        <template slot="userNameHeader">
                            姓名
                            <el-tooltip
                                class="item"
                                effect="dark"
                                content="点击显示所有姓名"
                                placement="bottom"
                                icon="el-icon-view"
                            >
                                <i
                                    style="color: #409eff"
                                    class="el-icon-view"
                                ></i>
                            </el-tooltip>
                        </template>

                        <template slot="userCodeHeader">
                            人员编码
                            <el-tooltip
                                class="item"
                                effect="dark"
                                content="点击显示所有人员编号"
                                placement="bottom"
                                icon="el-icon-view"
                            >
                                <i
                                    style="color: #409eff"
                                    class="el-icon-view"
                                ></i>
                            </el-tooltip>
                        </template>

                        <template slot="createUser" slot-scope="scope">
                            <div>
                                <span>{{ scope.row.createUser }}</span>
                                <el-button
                                    v-if="scope.row.createUser"
                                    type="text"
                                    icon="el-icon-view"
                                    @click="
                                        viewUserInfo(scope.row, 'createUser')
                                    "
                                ></el-button>
                            </div>
                        </template>
                        <!-- 自定义右侧操作栏 -->
                        <template slot-scope="{ size }" slot="menuRight">
                            <input
                                id="fileslist"
                                v-show="false"
                                type="file"
                                accept=".xls"
                                ref="fileRef"
                                @change="fileChange"
                            />
                        </template>
                    </yo-table>
                </template>
            </el-table-column>
            <el-table-column label="机构编号" prop="deptId"></el-table-column>
            <el-table-column label="机构名称" prop="instName"></el-table-column>
            <el-table-column label="操作">
                <template slot-scope="{ row, scope }">
                    <span
                        v-if="fqstatus == '1'"
                        style="color: #409eff; cursor: pointer"
                        @click.stop="reInitiatedFunction(row)"
                    >
                        <i class="el-icon-refresh-right"></i>
                        重新发起
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <span slot="footer" class="dialog-footer">
            <el-pagination
                background
                style="margin-bottom: 20px"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page.sync="page.currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="page.pageSize"
                layout="total,sizes, prev, pager, next"
                :total="total"
            ></el-pagination>
            <el-button @click="handleClose">关 闭</el-button>
            <!-- <el-button type="primary" @click="dialogVisible = false">
                确 定
            </el-button> -->
        </span>
        <customModal ref="customModalRef"></customModal>

        <yo-table
            v-show="false"
            style="width: 0px; height: 0px"
            :option="tableOptionFlag"
            ref="crudFlag"
            v-model="formParent"
            @row-save="rowSave"
        ></yo-table>
    </el-dialog>
</template>
<script>
import {
    listConfig,
    importConfig,
    downloadExample,
    importSensitive,
    addUserImport,
    updateUserImport,
    delUserImport,
    HandleSend,
    getById,
    listSecondConfig,
    reInitiated,
} from '@/api/system/userimport';
import tableOption from './infoData/sencondTableoption.js';
import tableOptionFlag from './infoData/tableoption.js';
import customModal from './modal/jgxzedwh';
import d from 'highlight.js/lib/languages/d.js';
export default {
    name: 'Auditlog',
    components: { customModal },
    dicts: [
        'yoaf_system_code',
        'yoaf_log_state',
        'yoaf_request_action',
        'yoaf_request_type',
    ],
    data() {
        return {
            formInline: {
                userName: '',
                userCode: '',
                deptId: '',
            },
            loading: true,
            formParent: {},
            search: {},
            page: {
                pageSize: 10,
                currentPage: 1,
            },
            total: 0,
            data: [],
            tableOption: tableOption,
            tableOptionFlag: tableOptionFlag,
            // 非多个禁用
            multiple: false,
            menu: false,
            yearA: '',
            yearM: '',
            yearQ: '',
            isShowName: '',
            nameText: '显示姓名',
            dialogVisible: false,
            rowInfo: {},
            tableData: [],
            pageTitle: '批次',
            expandedRows: [], // 保存展开行的键
            defaultFlag: false,
            fqstatus: '',
            openFlag: '',
        };
    },
    created() {},
    methods: {
        getRowKey(row) {
            return row.deptId; // 假设每行数据有一个唯一的id字段
        },

        handleExpandChange(row, expandedRows) {
            if (expandedRows.length > 1) {
                this.$refs.tableList.toggleRowExpansion(expandedRows[0]);
            }
        },
        rowClick(row) {
            if (this.openFlag == row.deptId && this.expandedRows.length != 0) {
                this.expandedRows = [];
            } else {
                this.openFlag = row.deptId;
                this.expandedRows = [row.deptId];
                this.handleExpandChange(row, this.expandedRows);
            }
        },
        // 首次加载调用此方法
        onLoad(page) {
            this.getList(this.search);
        },
        showAllName() {
            if (this.isShowName == '1') {
                this.isShowName = '';
                this.nameText = '显示姓名';
            } else {
                this.isShowName = '1';
                this.nameText = '姓名脱敏';
            }
            this.handleQuery();
        },
        /** 分页查询人员列表 */
        getList(query) {
            const params = { ...query, ...this.page };
            this.loading = true;
            listSecondConfig(params).then(response => {
                if (response.code == 0) {
                    console.log();
                    this.data = response.data.list;
                    this.page.total = response.data.total;
                    this.loading = false;
                    this.total = response.data.total;
                    this.tableData = response.data.list;
                    this.fqstatus = response.data.list[0].userList[0].fqStatus;
                }
            });
        },
        clickFun(column, event) {
            if (column.label === '姓名') {
                if (this.search.isShowName == '1') {
                    this.search.isShowName = '';
                } else {
                    this.search.isShowName = '1';
                }
            }
            if (column.label === '人员编码') {
                if (this.search.isShowCode == '1') {
                    this.search.isShowCode = '';
                } else {
                    this.search.isShowCode = '1';
                }
            }
            this.getList(this.search);
        },
        // 搜索按钮
        searchChange(params, done) {
            this.handleQuery();
            setTimeout(() => {
                done();
            }, 1000);
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.page.currentPage = 1;
            let date = new Date(this.search.year);
            let year = date.getFullYear().toString();
            if (year == '1970' || year == 'NaN') {
                year = '';
            }
            this.search.year = year;
            let date2 = new Date(this.search.yearMonth);
            let year2 = date2.getFullYear();
            let month = ('0' + (date2.getMonth() + 1)).slice(-2);
            if (year2 == '1970' && month == '01') {
                month = '';
            }
            this.search.yearMonth = month;
            if (this.search.yearMonth == 'aN') {
                this.search.yearMonth = '';
            }
            this.search.isShowName = this.isShowName;
            this.search.year = this.rowInfo.year;
            this.search.yearMonth = this.rowInfo.yearMonth;
            this.search.groupId = this.rowInfo.groupId;
            this.search.deptId = row.deptId;
            this.getList(this.search);
            this.exportSearch = this.search;
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.$refs.crud.searchReset();
        },
        fileChange(event) {
            let file = document.querySelector('#fileslist').files[0];
            let formData = new FormData();
            formData.append('file', file);
            formData.append('year', this.yearA);
            formData.append('yearMonth', this.yearM);
            formData.append('yearQuar', this.yearQ);
            importConfig(formData).then(res => {
                if (res.code == 0) {
                    this.$message.success(res.message);
                    this.getList();
                }
            });
            // 清除文件，防止下次上传相同文件无反应
            event.target.value = null;
        },
        /** 导入按钮操作 */
        handleImport() {
            let date = new Date(this.search.year);
            let year = date.getFullYear().toString();
            if (year == '1970') {
                year = null;
            }

            let date2 = new Date(this.search.yearMonth);
            let year2 = date2.getFullYear();
            let month = ('0' + (date2.getMonth() + 1)).slice(-2);
            if (year2 == '1970' && month == '01') {
                month = null;
            }
            if (date2 == 'Invalid Date') {
                month = null;
            }

            if (year == null || year == 'NaN') {
                this.$modal.alertWarning('请先选择年度!');
                return;
            }
            if (this.search.yearQuar != '' && month != null) {
                this.$modal.alertWarning('只能选择年季度或者年月');
                return;
            }
            this.yearA = year;
            this.yearM = month;
            if (this.yearM == 'aN') {
                this.yearM = '';
            }
            this.yearQ = this.search.yearQuar;
            this.$refs.fileRef.click();
        },
        /**敏感信息显隐**/
        viewUserInfo(data, type) {
            let sensitiveStatus = data[type].includes('*') ? '1' : '2';
            importSensitive(data.id, sensitiveStatus).then(response => {
                const res = response.data;

                this.data.forEach(item => {
                    if (item.id == data.id) {
                        item[type] = res[type];
                    }
                });
            });
        },
        /** 下载模板 */
        exportExample() {
            downloadExample().then(res => {
                this.handleExportData(res);
            });
        },
        /** 处理返回的流文件 */
        handleExportData(res) {
            if (!res) return;
            let data = res.data;
            let filename = res.headers['content-disposition'].split('=')[1];
            let _filename = decodeURI(filename);
            const link = document.createElement('a');
            //创建 Blob对象 可以存储二进制文件
            let blob = new Blob([data], { type: 'application/x-excel' });
            link.style.display = 'none';
            link.href = URL.createObjectURL(blob);
            link.download = _filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        },
        /** 新增按钮操作 */
        handleAdd() {
            let uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
                /[xy]/g,
                function (c) {
                    let r = (Math.random() * 16) | 0,
                        v = c === 'x' ? r : (r & 0x3) | 0x8;
                    return v.toString(16);
                }
            );
            (this.formParent = {
                id: uuid,
            }),
                this.$refs.crud.rowAdd();
        },
        handleAdd1() {
            let uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
                /[xy]/g,
                function (c) {
                    let r = (Math.random() * 16) | 0,
                        v = c === 'x' ? r : (r & 0x3) | 0x8;
                    return v.toString(16);
                }
            );
            (this.formParent = {
                id: uuid,
            }),
                this.$refs.crudFlag.rowAdd();
        },
        // 新增表单保存
        rowSave(form, done, loading) {
            form.year = this.rowInfo.year;
            form.yearMonth = this.rowInfo.yearMonth;
            form.batchCode = this.rowInfo.batchCode;
            addUserImport(form)
                .then(response => {
                    if (response.code == 0) {
                        this.$modal.msgSuccess(response.message);

                        this.getList(this.search);
                        done();
                    }
                })
                .catch(error => {
                    loading();
                });
        },
        /** 修改按钮操作 */
        async handleUpdate(row, index) {
            // 因为有脱敏的数据 所以需要请求后台获取数据
            const result = await getById(row.id);
            this.formParent = result.data;
            this.$refs.crud.rowEdit(result.data, index);
        },
        // 修改表单保存
        rowUpdate(form, index, done, loading) {
            console.log('修改之后的回：', this.search);
            updateUserImport(form)
                .then(response => {
                    if (response.code == 0) {
                        this.$modal.msgSuccess(response.message);
                        this.getList(this.search);
                        done();
                    }
                })
                .catch(error => {
                    loading();
                });
        },
        handleDelete(row) {
            const userCode = row.userCode;
            const id = row.id;
            this.$modal
                // .confirm('是否确认删除人员编号为"' + userCode + '"的数据项？')
                .confirm('是否确认删除该数据项？')

                .then(() => {
                    this.handleDel(id, delUserImport);
                })
                .catch(() => {});
        },
        /** 删除按钮操作 */
        async handleDel(id, delFn) {
            let res = await delFn(id);
            if (res.code == 0) {
                this.getList(this.search);
                //this.getList();
                this.$message.success(res.message);
            }
        },
        /** 发起按钮操作 */
        handleFq() {
            let date = new Date(this.search.year);
            let year = date.getFullYear().toString();
            if (year == '1970' || year == 'NaN') {
                year = '';
            }
            this.search.year = year;
            let date2 = new Date(this.search.yearMonth);
            let year2 = date2.getFullYear();
            let month = ('0' + (date2.getMonth() + 1)).slice(-2);
            if (year2 == '1970' && month == '01') {
                month = '';
            }
            if (date2 == 'Invalid Date') {
                month = null;
            }
            this.search.yearMonth = month;
            if (
                this.search.yearMonth == 'aN' ||
                this.search.yearMonth == null ||
                this.search.yearMonth == 'null'
            ) {
                this.search.yearMonth = '';
            }
            if (year == null || year == 'NaN') {
                this.$modal.alertWarning('请先选择年度!');
                return;
            }
            if (this.search.yearQuar != '' && month != null && month != '') {
                this.$modal.alertWarning('只能选择年季度或者年月');
                return;
            }
            const params = { ...this.search };
            this.$modal
                .confirm(
                    '是否确认发起：' +
                        this.search.year +
                        '年-' +
                        this.search.yearMonth +
                        '月份-' +
                        this.search.yearQuar +
                        '季度的批次？'
                )
                .then(() => {
                    this.handleFq2(params, HandleSend);
                })
                .catch(() => {});
        },
        /** 发起按钮操作 */
        async handleFq2(params, delFn) {
            let res = await delFn(params);
            if (res.code == 0) {
                this.getList();
                this.$message.success(res.message);
            }
        },
        openModal(row, index) {
            this.defaultFlag = false;
            this.expandedRows = [];
            this.resetF();
            this.pageTitle =
                row.year +
                '年' +
                row.yearMonth +
                '月人员维护（' +
                row.batchCode +
                '）';
            this.rowInfo = row;
            this.search.year = row.year;
            this.search.yearMonth = row.yearMonth;
            this.search.batchCode = row.batchCode;

            this.search.groupId = row.groupId;
            this.search.deptId = row.deptId;

            this.search = {
                year: row.year,
                yearMonth: row.yearMonth,
                groupId: row.groupId,
                batchCode: row.batchCode,
            };
            this.getList(this.search);
            this.dialogVisible = true;
        },
        handleClose(done) {
            this.dialogVisible = false;
            // done();
            this.resetF();
            // this.$confirm('确认关闭？')
            //     .then(_ => {
            //         done();
            //     })
            //     .catch(_ => {});
        },
        onSubmit() {
            this.defaultFlag = true;
            this.search.userName = this.formInline.userName;
            this.search.userCode = this.formInline.userCode;
            this.search.deptId = this.formInline.deptId;
            this.getList(this.search);
        },
        resetF() {
            this.formInline.userName = '';
            this.formInline.userCode = '';
            this.formInline.deptId = '';
        },
        handleSizeChange(val) {
            this.page.pageSize = val;
            this.page.currentPage = 1;
            this.getList(this.search);
        },
        handleCurrentChange(val) {
            this.page.currentPage = val;
            this.getList(this.search);
        },
        openSettingEd(row, index) {
            this.$nextTick(() => {
                this.$refs.customModalRef.openModal(this.rowInfo);
            });
        },
        reInitiatedFunction(row) {
            let formData = {
                batchCode: row.userList[0].batchCode,
                deptId: row.deptId,
            };

            this.$confirm('确定重新发起当前机构人员？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(async () => {
                    const res = await reInitiated(formData);
                    console.log('结果：', res);
                    if (res.code == 0) {
                        this.$message.success('重新发起成功！');
                    }
                })
                .catch(() => {});
        },
    },
};
</script>

<style>
.demo-table-expand {
    font-size: 0;
}
.demo-table-expand label {
    width: 90px;
    color: #99a9bf;
}
.demo-table-expand .el-form-item {
    margin-right: 0;
    margin-bottom: 0;
    width: 50%;
}

.el-table .success-row {
    background: oldlace;
}
</style>
