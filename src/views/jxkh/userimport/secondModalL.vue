<template>
    <el-dialog
        :title="pageTitle"
        :visible.sync="dialogVisible"
        width="95%"
        :before-close="handleClose"
        :close-on-press-escape="false"
    >
        <el-tabs type="card" v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="机构维度" name="first">
                <customModalFirst ref="customModalRef"></customModalFirst>
            </el-tab-pane>
            <el-tab-pane label="人员维度" name="second">
                <customModalTwo ref="customModalRefTwo"></customModalTwo>
            </el-tab-pane>
        </el-tabs>
        <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">关 闭</el-button>
            <!-- <el-button type="primary" @click="dialogVisible = false">
                确 定
            </el-button> -->
        </span>
    </el-dialog>
</template>

<script>
import customModalFirst from './secondModalF.vue';
import customModalTwo from './secondModalS.vue';
export default {
    components: { customModalFirst, customModalTwo },
    data() {
        return {
            dialogVisible: false,
            activeName: 'first',
            pageTitle: '',
            rowInfo: {},
        };
    },
    methods: {
        handleClose(done) {
            done();
        },
        openModal(row, index) {
            this.activeName = 'first';
            this.rowInfo = row;
            this.pageTitle =
                row.year +
                '年' +
                row.yearMonth +
                '月绩效工资管理（' +
                row.batchCode +
                '）';
            this.dialogVisible = true;
            this.$nextTick(() => {
                this.$refs.customModalRef.openModal(row);
            });
        },
        handleClick(tab) {
            let tabFlag = tab.index;
            if (tabFlag == 1) {
                this.$refs.customModalRefTwo.openModal(this.rowInfo);
            } else if (tabFlag == 0) {
                this.$refs.customModalRef.openModal(this.rowInfo);
            }
        },
    },
};
</script>
