import validate from '@/utils/validate';

export default {
    index: true,
    indexLabel: '序号',
    selection: false,
    align: 'center',
    card: true,
    menuAlign: 'center',
    addTitle: '添加人员',
    editTitle: '修改人员',
    saveBtnText: '确定',
    editBtnText: '修改',
    updateBtnText: '确定',
    addBtn: false,
    editBtn: false,
    delBtn: false,
    searchBtn: false,
    refreshBtn: false,
    emptyBtn: false,
    searchMenuSpan: 6,
    searchMenuPosition: 'left',
    labelPosition: 'right',
    labelWidth: 150,
    tip: false,
    columnBtn: false,
    //menu:false,
    column: [
        {
            label: '批次编码',
            prop: 'batchCode',
            addDisplay: false,
            editDisplay: false,
        },
        {
            label: '年度',
            prop: 'year',
            type: 'year',
            search: true,
            format: 'yyyy',
            valueFormat: 'yyyy',
            addDisplay: false,
            editDisplay: false,
            // value: Number(new Date().getFullYear()),
        },
        {
            label: '月份',
            prop: 'yearMonth',
            type: 'month',
            search: true,
            format: 'MM',
            valueFormat: 'MM',
            addDisplay: false,
            editDisplay: false,
        },
        {
            label: '机构编码',
            hide: true,
            prop: 'deptId',
            rules: [
                {
                    required: true,
                    message: '请输入机构编码',
                },
            ],
        },
        {
            label: '机构名称',
            prop: 'instName',
            hide: true,
            rules: [
                {
                    required: true,
                    message: '请输入机构编码',
                },
            ],
        },

        {
            label: '人员编码',
            prop: 'userCode',
            hide: true,
            showColumn: false,
            rules: [
                {
                    required: true,
                    message: '请输入人员编码',
                    trigger: 'blur,change',
                },
            ],
        },
        {
            label: '姓名',
            prop: 'userName',
            hide: true,
            showColumn: false,
            rules: [
                {
                    required: true,
                    message: '请输入人员编码',
                    trigger: 'blur,change',
                },
            ],
        },
        {
            label: '用户数',
            prop: 'num',
            showColumn: false,
            addDisplay: false,
        },
        {
            label: '身份证号',
            prop: 'idCard',
            showColumn: false,
            hide: true,
        },
        {
            label: '所在部门',
            prop: 'groupId',
            hide: true,
            showColumn: false,
        },
        {
            label: '性别',
            prop: 'sex',
            showColumn: false,
            hide: true,
        },
        {
            label: '民族',
            prop: 'nation',
            showColumn: false,
            hide: true,
        },
        {
            label: '出生日期',
            prop: 'birthday',
            showColumn: false,
            hide: true,
        },
        {
            label: '移动电话',
            prop: 'phone',
            showColumn: false,
            hide: true,
        },
        {
            label: '岗位',
            prop: 'post',
            showColumn: false,
            hide: true,
        },
        {
            label: '人员类别',
            prop: 'userType',
            showColumn: false,
            hide: true,
        },
        {
            label: '岗位序列',
            prop: 'orgNo',
            hide: true,
            showColumn: false,
            hide: true,
        },
        {
            label: '工资单列类别',
            prop: 'wageType',
            hide: true,
            showColumn: false,
            hide: true,
        },
        {
            label: '人员职级（新）',
            prop: 'userRank',
            hide: true,
            showColumn: false,
            hide: true,
        },
        {
            label: '是否为领导',
            prop: 'isLeader',
            hide: true,
            showColumn: false,
        },
        {
            label: '创建用户',
            prop: 'createUser',
            addDisplay: false,
            editDisplay: false,
            showColumn: false,
            hide: true,
        },
        {
            label: '创建时间',
            prop: 'createTime',
            addDisplay: false,
            editDisplay: false,
            showColumn: false,
            hide: true,
        },
    ],
};
