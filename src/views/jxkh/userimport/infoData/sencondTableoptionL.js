import validate from '@/utils/validate';

export default {
    index: true,
    indexLabel: '序号',
    selection: false,
    align: 'center',
    card: true,
    menuAlign: 'center',
    addTitle: '添加人员',
    editTitle: '修改人员',
    saveBtnText: '确定',
    editBtnText: '修改',
    updateBtnText: '确定',
    addBtn: false,
    editBtn: false,
    delBtn: false,
    searchBtn: false,
    refreshBtn: false,
    emptyBtn: false,
    searchMenuSpan: 6,
    searchMenuPosition: 'left',
    labelPosition: 'right',
    labelWidth: 150,
    tip: false,
    columnBtn: false,
    menu: false,
    column: [
        {
            label: '机构编码',
            prop: 'deptId',
            hide: true,
            rules: [
                {
                    required: true,
                    message: '请输入机构编码',
                },
            ],
        },
        {
            label: '人员编码',
            prop: 'userCode',
            width: '120',
            rules: [
                {
                    required: true,
                    message: '请输入人员编码',
                    trigger: 'blur,change',
                },
            ],
        },
        {
            label: '姓名',
            prop: 'userName',
            rules: [
                {
                    required: true,
                    message: '请输入人员编码',
                    trigger: 'blur,change',
                },
            ],
        },
    ],
};
