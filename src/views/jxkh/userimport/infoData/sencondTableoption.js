import validate from '@/utils/validate';

export default {
    index: true,
    indexLabel: '序号',
    selection: false,
    align: 'center',
    card: true,
    menuAlign: 'center',
    addTitle: '添加人员',
    editTitle: '修改人员',
    saveBtnText: '确定',
    editBtnText: '修改',
    updateBtnText: '确定',
    addBtn: false,
    editBtn: false,
    delBtn: false,
    searchBtn: false,
    refreshBtn: false,
    emptyBtn: false,
    searchMenuSpan: 6,
    searchMenuPosition: 'left',
    labelPosition: 'right',
    labelWidth: 150,
    tip: false,
    columnBtn: false,
    //menu:false,
    column: [
        // {
        //     label: 'id',
        //     prop: 'id',
        //     hide: true,
        // },
        {
            label: '年度',
            prop: 'year',
            type: 'year',
            hide: true,
            format: 'yyyy',
            valueFormat: 'yyyy',
            editDisabled: true,
            rules: [
                {
                    required: true,
                    message: '请输入年度',
                },
            ],
        },
        {
            label: '月份',
            prop: 'yearMonth',
            type: 'month',
            hide: true,
            format: 'MM',
            valueFormat: 'MM',
            editDisabled: true,
        },
        {
            label: '机构编码',
            prop: 'deptId',
            rules: [
                {
                    required: true,
                    message: '请输入机构编码',
                },
            ],
        },

        {
            label: '人员编码',
            prop: 'userCode',
            width: '120',
            // search: true,

            rules: [
                {
                    required: true,
                    message: '请输入人员编码',
                    trigger: 'blur,change',
                },
            ],
        },
        {
            label: '姓名',
            prop: 'userName',
            rules: [
                {
                    required: true,
                    message: '请输入人员编码',
                    trigger: 'blur,change',
                },
            ],
        },
        {
            label: '身份证号',
            prop: 'idCard',
            hide: true,
        },
        {
            label: '所在部门',
            prop: 'groupId',
        },
        {
            label: '性别',
            prop: 'sex',
        },
        {
            label: '民族',
            prop: 'nation',
            hide: true,
        },
        {
            label: '出生日期',
            prop: 'birthday',
            hide: true,
        },
        {
            label: '移动电话',
            prop: 'phone',
            hide: true,
        },
        {
            label: '岗位',
            prop: 'post',
        },
        {
            label: '人员类别',
            prop: 'userType',
        },
        {
            label: '岗位序列',
            prop: 'orgNo',
        },
        {
            label: '工资单列类别',
            prop: 'wageType',
        },
        {
            label: '人员职级（新）',
            prop: 'userRank',
        },
        {
            label: '是否为领导',
            prop: 'isLeader',
            showColumn: false,
        },
        {
            label: '创建用户',
            prop: 'createUser',
            addDisplay: false,
            hide: true,
            editDisplay: false,
        },
        {
            label: '创建时间',
            prop: 'createTime',
            hide: true,
            addDisplay: false,
            width: '200',
            editDisplay: false,
        },
    ],
};
