<template>
    <div class="app-container">
        <yo-table
            @keyup.enter.native="handleQuery"
            v-loading="loading"
            :data="data"
            :option="option"
            ref="crud"
            :page.sync="page"
            :search.sync="search"
            :before-open="beforeOpen"
            @on-load="onLoad"
            @search-change="searchChange"
            @row-update="rowUpdate"
            @row-save="rowSave"
            @refresh-change="refresh"
            v-model="formParent"
            @selection-change="handleSelectionChange"
        >
            <template slot="searchMenu">
                <el-button
                    size="mini"
                    type="primary"
                    icon="el-icon-search"
                    @click.stop="handleQuery"
                    v-hasPermi="['admin:user:page']"
                >
                    查询
                </el-button>
                <el-button
                    size="mini"
                    icon="el-icon-refresh"
                    @click.stop="resetQuery()"
                >
                    重置
                </el-button>
            </template>
            <template slot="typeForm">
                <el-select
                    :disabled="disabled"
                    v-model="formParent.type"
                    placeholder="请选择 菜单分类"
                    clearable
                >
                    <el-option
                        v-for="dict in dict.type['xmzhyy_mobilemenu_type']"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                </el-select>
            </template>
            <template slot="statusForm">
              <el-select
                :disabled="disabled"
                v-model="formParent.status"
                placeholder="请选择 状态"
                clearable
              >
                <el-option
                  v-for="dict in dict.type['xmzhyy_mobilemenu_status']"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </template>

            <!-- 自定义列 -->
            <template slot="status" slot-scope="scope">
                <el-tag size="mini" effect="plain" :type="tagType(scope.row)">
                    {{ status(scope.row) }}
                </el-tag>
            </template>
            <!-- 自定义左侧操作栏 -->
            <template slot="menuLeft">
                <el-button
                    type="primary"
                    icon="el-icon-plus"
                    size="mini"
                    plain
                    v-hasPermi="['admin:user:add']"
                    @click.stop="handleAdd"
                >
                    新增
                </el-button>
                <el-button
                    type="danger"
                    plain
                    icon="el-icon-delete"
                    size="mini"
                    :disabled="multiple"
                    @click="handleDelete"
                    v-hasPermi="['admin:user:remove:batch']"
                >
                    批量删除
                </el-button>
            </template>
            <!-- 自定义右侧操作栏 -->
            <template slot="menuRight">
            </template>
            <template slot-scope="{ row, size, type, index }" slot="menu">
                <el-button
                    :size="size"
                    :type="type"
                    icon="el-icon-edit"
                    @click.stop="handleEdit(row, index)"
                    v-hasPermi="['admin:user:edit']"
                >
                    修改
                </el-button>
                <el-button
                    :size="size"
                    :type="type"
                    icon="el-icon-delete"
                    @click="handleDelete(row)"
                    v-hasPermi="['admin:user:remove']"
                >
                    删除
                </el-button>
                <el-button
                    :size="size"
                    :type="type"
                    icon="el-icon-info"
                    @click="handleView(row)"
                    v-hasPermi="['admin:user:remove']"
                >
                    查看详情
                </el-button>
            </template>

        </yo-table>
        <el-dialog :title="title" :visible.sync="dialogVisible" width="30%">
            <span slot="footer" class="dialog-footer">
                <el-button
                    type="primary"
                    @click="dialogQueren(dialogType)"
                    size="mini"
                >
                    确 定
                </el-button>
                <el-button @click="dialogVisible = false" size="mini">
                    取 消
                </el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import { mobileMenuList, addMobileMenu, editMobileMenu, deleteMobileMenu} from '@/api/xmqywx/zhyy';
import mobileMenuOption from './infoData/mobileMenuOption.js';
import { mapActions } from 'vuex';

export default {
    name: 'MobileMenu',
    dicts: ['xmzhyy_mobilemenu_type','xmzhyy_mobilemenu_status'],
    components: {},
    data() {
        return {
            title: '',
            dialogType: '',
            loading: true,
            formParent: {},
            search: {},
            exportSearch: {},
            page: {
                pageSize: 10,
                pageNum: 1,
            },
            data: [],
            option: mobileMenuOption,
            dialogVisible: false,
            // 非多个禁用
            multiple: true,
            disabled: false,
        };
    },
    created() {
        this.updateDictData(
            this.option.column,
            'type',
            this.dict.type['xmzhyy_mobilemenu_type']
        );
        this.updateDictData(
          this.option.column,
          'status',
          this.dict.type['xmzhyy_mobilemenu_status']
        );
    },
    methods: {
        // 首次加载调用此方法
        onLoad(page) {
            this.getList(this.search);
            // this.$refs['crud'].toggleSelection()
        },
        handleView(row) {
            this.$refs.crud.rowView(row);
        },
        refreshList() {
            this.handleQuery();
        },
        // 更新字典数据
        updateDictData(option, key, data) {
            let column = this.findObject(option, key);
            column.dicData = data;
        },
        // 搜索按钮
        searchChange(params, done) {
            this.handleQuery();
            setTimeout(() => {
                done();
            }, 1000);
        },
        // 新增表单保存
        async rowSave(form, done, loading) {
            let response = await addMobileMenu(form);
            if (response.code == 0) {
                this.$message.success(response.message);
                this.handleQuery();
                done();
            }
        },
        // 刷新按钮
        refresh() {
            this.handleQuery();
        },
        /** 查询列表数据 */
        async getList(query) {
            const params = { ...query, ...this.page };
            this.loading = true;
            let response = await mobileMenuList(params);
            if (response.code == 0) {
                this.data = response.data.list;
                this.page.total = response.data.total;
                this.loading = false;
            }
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.page.currentPage = 1;
            this.getList(this.search);
            this.exportSearch = this.search;
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.$refs.crud.searchReset();
            this.getList();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.id);
            this.multiple = !selection.length;
        },
        /** 新增按钮操作 */
        async handleAdd() {
            this.$refs.crud.rowAdd();
        },
        // 修改
        async handleEdit(row, index) {
            this.$refs.crud.rowEdit(row, index);
        },
        // 修改表单保存
        rowUpdate(form, index, done, loading) {
          editMobileMenu(form).then(response => {
                done();
                if (response.code == 0) {
                    this.$message.success(response.message);
                    this.handleQuery();
                }
            });
        },
        async handleDel(id) {
            let res = await deleteMobileMenu(id);
            if (res.code == 0) {
                this.$message.success(res.message);
                this.$refs.crud.toggleSelection();
                this.getList();
            }
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            this.$confirm(`是否永久删除选中的数据项？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                showCancelButton: true,
            })
                .then(async () => {
                    // 删除单个
                    if (row.id) {
                        this.handleDel(row.id);
                    } else {
                        //批量删除
                        if (
                            this.ids instanceof Array &&
                            this.ids != null &&
                            this.ids != '' &&
                            this.ids != undefined
                        ) {
                            this.handleDel(this.ids.join(','));
                        }
                    }
                })
                .catch(() => {});
        },

        // 弹窗打开
        beforeOpen(done, type) {
          done();
        },
        dialogQueren(type) {
            if (type == 'dept') {
                let arr = this.$refs.tree.getCheckedNodes();
                let deptName = arr.map(i => {
                    return i.name;
                });
                this.deptName = deptName.join(',');
                let deptIds = arr.map(i => {
                    return i.id;
                });
                this.formParent.deptIds = deptIds;
            } else {
                let arr = this.$refs.tree.getCheckedNodes();
                if (!arr.length) {
                    this.instName = '';
                    this.formParent.instId = '';
                } else {
                    this.instName = arr[0].name;
                    this.formParent.instId = arr[0].id;
                }
            }
            this.dialogVisible = false;
        },
        status(row) {
            switch (row.status) {
                case '1':
                    return '正常';
                case '2':
                    return '冻结';
                default:
                    break;
            }
        },
        //tag样式
        tagType(row) {
            switch (row.status) {
                case '1':
                    return 'primary';
                case '2':
                    return 'info';
                default:
                    break;
            }
        },
    },
};
</script>

<style scoped>
.icon-list {
    height: 220px;
    overflow-y: scroll;
}
</style>
