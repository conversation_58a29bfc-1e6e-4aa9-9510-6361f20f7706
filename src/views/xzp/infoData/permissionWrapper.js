import jgzhOption from './jgzhOption';

export function withPermissions(config, getContext) {
  return {
    ...config,
    column: config.column.map(column => {
      if (column.prop === 'jgzhStatus') {
        return {
          ...column,
          editDisplay: getContext().hasEditPermission,
          editDisabled: !getContext().hasEditPermission
        }
      }
      return column;
    })
  }
}
