// 监管账户信息审批列表、弹框项
import validate from '@/utils/validate';
export default {
  index: false,
  indexLabel: '序号',
  rowKey: 'id',
  reserveSelection: true,
  selection: true,
  align: 'center',
  card: true,
  menuAlign: 'center',
  emptyBtnIcon: 'el-icon-refresh',
  searchMenuSpan: 6,
  searchMenuPosition: 'left',
  // addTitle: '监管账户信息审核',
  viewTitle: '审核信息查询',
  editTitle: '审核',
  editBtnText: '修改',
  updateBtnText: '保存',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  searchBtn: false,
  refreshBtn: false,
  emptyBtn: false,
  labelWidth: 120,
  labelPosition: 'right',
  tip: false,
  columnBtn: false,
  menuWidth: 100,
  column: [
    {
      label: 'ID',
      prop: 'id',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: 'applyID',
      prop: 'applicationId',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '审核状态',
      prop: 'spStatus',
      // width: 120,
      /*  searchLabelWidth: 50,
        searchSpan: 5,
        search: true, */
      type: 'select',
      slot: true,
      formslot: true,
      selectedValue: "0",
      dicData: [{
        label: '待审核',
        value: "0"
      }, {
        label: '已审核',
        value: "1"
      }],
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      //查询设置：
      search: true, //查询条件
      searchLabelWidth: 100, //左边长度
      searchSpan: 7, //右边长度
    },
    {
      label: '审核结果',
      prop: 'spRes',
      // width: 120,
      /*  searchLabelWidth: 50,
       searchSpan: 5,
       search: true,*/
      type: 'select',
      slot: true,
      formslot: true,
      dicData: [{
        label: '通过',
        value: "1"
      }, {
        label: '不通过',
        value: "2"
      }],
      rules: [{
        required: true,
        message: "请选择审核结果",
        trigger: "blur,change"
      }],
      showColumn: false,
      addDisplay: true,
      //查询设置：
      search: true, //查询条件
      searchLabelWidth: 100, //左边长度
      searchSpan: 7, //右边长度*100
    },
    {
      label: '审批意见',
      prop: 'spContent',
      width: 180,
      slot: true,
      showColumn: false,
      addDisplay: true,
      formslot: true, //编辑需要
      span: 24,
      rules: [{
        required: true,
        message: "请输入审批意见",
        trigger: "change"
      }],
    },
    {
      label: '审核人',
      prop: 'spBy',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
    },
    {
      label: '审核时间',
      prop: 'spTime',
      // hide: true,
      slot: true,
      formslot: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
    },
    {
      label: '执行流水号',
      prop: 'serialno',
      width: 180,
      slot: true,
      formslot: true,
      showColumn: false,
      editDisabled: true, //不可修改
      span: 24, //一行
      addDisplay: true,
      //查询设置：
      search: true, //查询条件
      searchLabelWidth: 150, //左边长度
      searchSpan: 10, //右边长度
    },
    {
      label: '开户银行代码',
      prop: 'bankid',
      slot: true,
      width: 150,
      showColumn: false,
      addDisplay: true,
      span: 24,
      editDisabled: true, //不可修改
    },
    {
      label: '账户名',
      prop: 'accountname',
      slot: true,
      width: 150,
      showColumn: false,
      addDisplay: true,
      span: 24,
      editDisabled: true, //不可修改
    },
    {
      label: '账户号',
      prop: 'accountno',
      slot: true,
      width: 150,
      showColumn: false,
      addDisplay: true,
      span: 24,
      editDisabled: true, //不可修改
    },
    {
      label: '执行类型',
      prop: 'executetype',
      type: "select",
      dicData: [],
      slot: true,
      width: 150,
      showColumn: false,
      addDisplay: true,
      span: 24,
      editDisabled: true, //不可修改
    },
    {
      label: '执行金额（元）',
      prop: 'executeamount',
      slot: true,
      width: 150,
      showColumn: false,
      addDisplay: true,
      span: 24,
      editDisabled: true, //不可修改
    },
    {
      label: '执行部门',
      prop: 'executedept',
      slot: true,
      width: 150,
      showColumn: false,
      addDisplay: true,
      span: 24,
      editDisabled: true, //不可修改
    },
    {
      label: '执行时间',
      prop: 'executedate',
      type: "datetime",
      format: "yyyy-MM-dd hh:mm:ss",
      slot: true,
      width: 180,
      showColumn: false,
      addDisplay: true,
      span: 24,
      editDisabled: true, //不可修改
    },

    {
      label: '解除执行原流水号',
      prop: 'releaseserialno',
      width: 180,
      formslot: true,
      showColumn: false,
      span: 24,
      addDisplay: true,
      editDisabled: true, //不可修改
    },
    {
      label: '解除执行时间',
      prop: 'releasetime',
      type: "datetime",
      format: "yyyy-MM-dd hh:mm:ss",
      slot: true,
      width: 180,
      showColumn: false,
      addDisplay: true,
      span: 24,
      editDisabled: true, //不可修改
    },
    {
      label: '备注说明',
      prop: 'note',
      width: 180,
      slot: true,
      showColumn: false,
      addDisplay: true,
      formslot: true, //编辑需要
      span: 24,
      hide: true, //列表隐藏显示
      editDisabled: true, //不可修改
    },
    {
      label: '创建人',
      prop: 'createBy',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '创建时间',
      prop: 'createTime',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '操作人',
      prop: 'updateBy',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
    },
    {
      label: '操作时间',
      prop: 'updateTime',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
    },
  ],
};
