// 监管账户信息列表、弹框项
import validate from '@/utils/validate';
export default {
  index: false,
  indexLabel: '序号',
  rowKey: 'id',
  reserveSelection: true,
  selection: true,
  align: 'center',
  card: true,
  menuAlign: 'center',
  emptyBtnIcon: 'el-icon-refresh',
  searchMenuSpan: 6,
  searchMenuPosition: 'left',
  addTitle: '新增监管账户信息',
  viewTitle: '监管账户信息查询',
  editTitle: '修改监管账户信息',
  editBtnText: '修改',
  updateBtnText: '保存',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  searchBtn: false,
  refreshBtn: false,
  emptyBtn: false,
  labelWidth: 120,
  labelPosition: 'right',
  tip: false,
  columnBtn: false,
  menuWidth: 100,
  column: [
    {
      label: 'ID',
      prop: 'id',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '反馈状态',
      prop: 'jgzhStatus',
      type: "select",
      slot: true,
      width: 150,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      editDisabled: true, //不可修改
      dicData: [],
      span: 24,
    },
    {
      label: '执行流水号',
      prop: 'serialno',
      width: 180,
      slot: true,
      formslot: true,
      showColumn: false,
      editDisabled: true, //不可修改
      span: 24, //一行
      addDisplay: true,
      //查询设置：
      search: true, //查询条件
      searchLabelWidth: 150, //左边长度
      searchSpan: 160, //右边长度
      rules: [{ //新增修改时的规则和提示信息
        required: true,
        message: "请输入执行流水号，银行代码 + 年月日 + 三位数序号：10539300001320240509001",
        trigger: "change"
      }],
    },
    {
      label: '开户银行代码',
      prop: 'bankid',
      slot: true,
      width: 150,
      showColumn: false,
      addDisplay: true,
      span: 24,
      rules: [{ //新增修改时的规则和提示信息
        required: true,
        message: "请输入开户银行代码",
        trigger: "change"
      }],
    },
    {
      label: '账户名',
      prop: 'accountname',
      slot: true,
      width: 150,
      showColumn: false,
      addDisplay: true,
      span: 24,
      rules: [{ //新增修改时的规则和提示信息
        required: true,
        message: "请输入账户名",
        trigger: "change"
      }],
    },
    {
      label: '账户号',
      prop: 'accountno',
      slot: true,
      width: 150,
      showColumn: false,
      addDisplay: true,
      span: 24,
      rules: [{ //新增修改时的规则和提示信息
        required: true,
        message: "请输入账户号",
        trigger: "change"
      }],
    },
    {
      label: '执行类型',
      prop: 'executetype',
      type: "select",
      dicData: [],
      rules: [{
        required: true,
        message: "请选择执行类型",
        trigger: "blur,change"
      }],
      slot: true,
      width: 150,
      showColumn: false,
      addDisplay: true,
      span: 24
    },
    {
      label: '执行金额（元）',
      prop: 'executeamount',
      slot: true,
      width: 150,
      showColumn: false,
      addDisplay: true,
      span: 24
    },
    {
      label: '执行部门',
      prop: 'executedept',
      slot: true,
      width: 150,
      showColumn: false,
      addDisplay: true,
      span: 24,
      rules: [{ //新增修改时的规则和提示信息
        required: true,
        message: "请输入执行部门，多个部门可以用逗号分隔",
        trigger: "change"
      }],
    },
    {
      label: '执行时间',
      prop: 'executedate',
      type: "datetime",
      format: "yyyy-MM-dd hh:mm:ss",
      slot: true,
      width: 180,
      showColumn: false,
      addDisplay: true,
      span: 24,
      rules: [{
        required: true,
        message: "请选择执行时间",
        trigger: "change"
      }],

      //查询设置：
      // search: true, //查询条件
      // searchLabelWidth: 100, //左边长度
    },

    {
      label: '解除执行原流水号',
      prop: 'releaseserialno',
      width: 180,
      formslot: true,
      showColumn: false,
      // editDisabled: true,
      // labelWidth: 180,
      span: 24,
      addDisplay: true,
      //查询设置：
      // search: true, //查询条件
      // searchLabelWidth: 100, //左边长度
      // rules: [{
      //   required: true,
      //   message: "请输入解除执行原流水号",
      //   trigger: "change"
      // }],
    },
    {
      label: '解除执行时间',
      prop: 'releasetime',
      type: "datetime",
      format: "yyyy-MM-dd hh:mm:ss",
      slot: true,
      width: 180,
      showColumn: false,
      addDisplay: true,
      span: 24,
      rules: [{
        required: false,
        message: "解除执行时间",
        trigger: "change"
      }],
    },
    {
      label: '备注说明',
      prop: 'note',
      width: 180,
      slot: true,
      // formslot: true,
      showColumn: false,
      addDisplay: true,
      formslot: true, //编辑需要
      span: 24,
    },
    {
      label: '反馈结果',
      prop: 'jgzhDesc',
      width: 180,
      slot: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      span: 24,
    },
    {
      label: '创建人',
      prop: 'createBy',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '创建时间',
      prop: 'createTime',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '操作人',
      prop: 'updateBy',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
    },
    {
      label: '操作时间',
      prop: 'updateTime',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,

    },
    /*  {
        label: '预留字段',
        prop: 'reserve',
        hide: true,
        showColumn: false,
        addDisplay: false,
        editDisplay: false,
        viewDisplay: false,
      },
      {
        label: '预留字段1',
        prop: 'reserveA',
        hide: true,
        showColumn: false,
        addDisplay: false,
        editDisplay: false,
        viewDisplay: false,
      },
      {
        label: '预留字段2',
        prop: 'reserveB',
        hide: true,
        showColumn: false,
        addDisplay: false,
        editDisplay: false,
        viewDisplay: false,
      },
      {
        label: '预留字段3',
        prop: 'reserveC',
        hide: true,
        showColumn: false,
        addDisplay: false,
        editDisplay: false,
        viewDisplay: false,
      },
      {
        label: '预留字段4',
        prop: 'reserveD',
        hide: true,
        showColumn: false,
        addDisplay: false,
        editDisplay: false,
        viewDisplay: false,
      }, */
  ],
};
