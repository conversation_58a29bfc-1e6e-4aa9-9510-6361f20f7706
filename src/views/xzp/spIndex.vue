<!-- 监管账户被执行反馈复核页面 -->
<template>
  <div class="app-container">
    <yo-table @keyup.enter.native="handleQuery" v-loading="loading" :option="option" :data="data" ref="crud"
      :page.sync="page" :search.sync="search" :before-open="beforeOpen" @on-load="onLoad" @search-change="searchChange"
      @row-update="rowUpdate" @row-save="rowSave" @refresh-change="refresh" v-model="formParent"
      @selection-change="handleSelectionChange">
      <template slot="searchMenu">
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <el-button size="mini" type="primary" icon="el-icon-search" @click.stop="handleQuery">
          查询
        </el-button>
        <el-button size="mini" icon="el-icon-refresh" @click.stop="resetQuery()">
          重置
        </el-button>
      </template>

      <!-- 自定义显示隐藏列 -->
      <template slot="accountname" slot-scope="scope">
        <div>
          <span>{{scope.row.accountname}}</span>
          <el-button v-if="scope.row.accountname" type="text" icon="el-icon-view"
            @click="viewSensitive(scope.row,'accountname')"></el-button>
        </div>
      </template>
      <template slot="accountno" slot-scope="scope">
        <div>
          <span>{{scope.row.accountno}}</span>
          <el-button v-if="scope.row.accountno" type="text" icon="el-icon-view"
            @click="viewSensitive(scope.row,'accountno')"></el-button>
        </div>
      </template>

      <!-- 自定义左侧操作栏 -->
      <template slot="menuLeft">
        <el-button type="danger" plain icon="el-icon-edit" size="mini" :disabled="multiple"
          @click="handleUpdate">批量审核</el-button>
      </template>


      <!-- 操作列 -->
      <template slot-scope="{ row, size, type ,index}" slot="menu">
        <el-button size="mini" :type="type" icon="el-icon-edit" @click.stop="spJgzh(row,index)">
          审核
        </el-button>
        <el-button size="mini" :type="type" icon="el-icon-info" @click.stop="handleView(row)">
          查看
        </el-button>
      </template>
    </yo-table>
    <!--审核弹框-->
    <p-dialog ref="spDialog" @cancel="cancel" @submit="submit" :title="title" class="sp-form-dialog" width="800px">
      <template slot="content">
        <el-form :model="treeForm" ref="treeForm" :rules="rules" label-suffix=":" label-width="150px">
          <el-row>
            <el-col :span="22">
              <el-form-item label="审批结果" prop="spRes">
                <el-select v-model="treeForm.spRes" placeholder="审批结果" clearable>
                  <el-option v-for="dict in dict.type['xzp_sp_res']" :key="dict.value" :label="dict.label"
                    :value="dict.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="22">
              <el-form-item label="审批意见" prop="spContent">
                <el-input type="textarea" resize="none" :rows="3" v-model.trim="treeForm.spContent" clearable
                  placeholder="请输入审批意见">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-show="false">
            <el-col :span="12">
              <el-form-item label="ids" prop="id">
                <el-input v-model.trim="treeForm.id" clearable>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-show="false">
            <el-col :span="12">
              <el-form-item label="applicationIds" prop="applicationId">
                <el-input v-model.trim="treeForm.applicationId" clearable>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </template>
    </p-dialog>

  </div>

</template>
<script>
import {
  jgzhSpList,
  spJgzh,
  jgzhSpDetail,
  spJgzhBatch,
  querySensitive
} from '@/api/jgzhSp';
import jgzhSpOption from './infoData/jgzhSpOption.js';
import Cookies from "js-cookie";

export default {
  name: 'jgzh',
  components: {},
  dicts: [
    "xzp_exe_type",
    "xzp_sp_status",
    "xzp_sp_res"
  ],
  data () {
    return {
      selectSp: 0,
      dialogVisible: false,
      treeForm: {
        id: "",
        applicationId: "",
        spRes: "1",
        spContent: "",
      },
      title: '',
      dialogType: '',
      loading: true,
      statusFlag: false,
      buttonAttr: '0',
      formParent: {},
      search: {},
      exportSearch: {},
      page: {
        pageSize: 10,
        pageNum: 1,
      },
      treeprops: {
        label: 'name',
        children: 'children',
      },
      deptName: '',
      instName: '',
      data: [],
      option: jgzhSpOption,
      dialogVisible: false,
      // 非多个禁用
      multiple: true,
      labelPosition: 'right',
      formLabelAlign: {
        name: '',
        idCard: '',
      },
      rules: {
        spContent: [
          { required: true, message: "请输入审批意见", trigger: "blur" },
          {
            min: 1,
            max: 100,
            message: "长度在 1 到 100 个字符",
            trigger: "blur",
          },
        ],
        spRes: [
          { required: true, message: "请审批", trigger: "blur,change" },
        ],
      },
    };
  },
  watch: {
    'search.updateTimeBox': {
      handler (val) {
        if (val) {
          // this.search.updateTimeStart = val[0];
          // this.search.updateTimeEnd = val[1];
        }
      },
      deep: true,
    },
  },
  created () {
    /** *添加字典项数据*/
    this.updateDictData(
      this.option.column,
      "executetype",
      this.dict.type["xzp_exe_type"]
    );
    this.updateDictData(
      this.option.column,
      "spStatus",
      this.dict.type["xzp_sp_status"]
    );
    this.updateDictData(
      this.option.column,
      "spRes",
      this.dict.type["xzp_sp_res"]
    );
    this.page.currentPage = 1;
    this.getList();
  },
  methods: {
    // 首次加载调用此方法
    onLoad () {
      this.getList();
    },
    // 弹窗打开
    beforeOpen (done, type) {
      console.log('type:', type);
      done();
    },
    refreshList () {
      this.handleQuery();
    },
    // 搜索按钮
    searchChange (params, done) {
      this.handleQuery();
      setTimeout(() => {
        done();
      }, 1000);
    },

    // 更新字典数据
    updateDictData (option, key, data) {
      let column = this.findObject(option, key);
      column.dicData = data;
    },

    // 新增表单保存
    async rowSave (form, done, loading) {
      console.log("新增表单参数==>" + JSON.stringify(form))
      addJgzhInfo(form).then(res => {
        if (res.code == '0') {
          this.$message.success(res.message);
          this.handleQuery();
          done();
        } else {
          this.$message.error(res.message);
          this.loading = false;
        }
      });
    },
    // 刷新按钮
    refresh () {
      this.handleQuery();
    },
    /** 查询列表 */
    async getList (query) {
      const params = { ...query, ...this.page };
      console.log('查询参数：', params);
      this.loading = true;
      const { code, data } = await jgzhSpList(params);
      if (code == 0) {
        this.data = data.list;
        this.page.total = data.total;
        this.loading = false;
      }
    },

    /** 搜索按钮操作 */
    handleQuery () {
      this.page.currentPage = 1;
      this.getList(this.search);
      this.exportSearch = this.search;
    },
    // /** 重置按钮操作 */
    resetQuery () {
      this.$refs.crud.searchReset();
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange (selection) {
      this.selectSp = selection.length;
      this.statusFlag = true;
      let row = selection; //查找到的所有进行修改
      if (row != undefined) {
        this.statusFlag = false;
      }
      this.ids = selection.map(item => item.id).join(',');
      this.applicationIds = selection.map(item => item.applicationId).join(',');
      console.log("关联id---" + this.applicationIds);
      this.multiple = !(selection.length > 0 && !this.statusFlag);
    },



    // 审核
    async spJgzh (row, index) {
      var status = row.spRes;
      if (status == "1") {
        this.$confirm("该记录已审核通过，无需再审核！", "提示", {
            confirmButtonText: "确认",
            type: "warning"
          })
          .then(() => {})
          .catch(() => {});
      } else {
        var res = {};
        res.id = row.id; //审核表id
        res.applicationId = row.applicationId; //关联申请表id
        res.serialno = row.serialno;
        res.spRes = row.spRes;
        res.spContent = row.spContent;
        Cookies.set("serialno", row.serialno);
        Cookies.set("applicationId", row.applicationId);
        Cookies.set("spRes", row.spRes);
        Cookies.set("spContent", row.spContent);
        this.$refs.crud.rowEdit(res, index);
      }
    },
    //查看
    async handleView (row) {

      this.$refs.crud.rowView(row);
      // var id = row.id;
      // jgzhSpDetail(id).then(res => {
      //   console.log("审核列表查询返回的结果--" + JSON.stringify(res))
      //   if (res.code == 0) {
      //     this.$refs.crud.rowView(res.data);
      //   }
      // });
    },

    // 修改表单保存
    rowUpdate (form, index, done, loading) {
      spJgzh(form).then(response => {
        done()
        if (response.code == 0) {
          this.$message.success(response.message);
          this.handleQuery();
        }
      });
    },

    //批量审核
    async handleUpdate (row) {
      const h = this.$createElement;
      this.$msgbox({
          title: '提示',
          message: h('p', null, [
            h('p', { style: 'word-break: break-all' },
              '是否批量审核选中的' + this.selectSp + '条数据？'
            ),
          ]),
          showCancelButton: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        })
        .then(async () => {
          let res;
          this.title = "批量审核";
          this.$refs.spDialog.show();
          if (row.id != undefined) {
            // res = await spJgzhBatch({ id: row.id });
            this.treeForm.id = row.id;
            this.treeForm.applicationId = row.applicationId;
          } else {
            if (
              this.ids != null &&
              this.ids != '' &&
              this.ids != undefined
            ) {
              // res = await spJgzhBatch({ id: this.ids });
              this.treeForm.id = this.ids;
              this.treeForm.applicationId = this.applicationIds;
            }
          }
          console.log("关联id---" + this.treeForm.applicationId);
          this.getList(this.search);
        })
        .catch(() => {});
    },
    //提交审核结果
    submit () {
      const {
        id = "",
          applicationId = "",
          spRes = "",
          spContent = "",
      } = this.treeForm || {};
      let submitData = {
        id,
        applicationId,
        spRes,
        spContent,
      };
      console.log("审批结果信息：" + JSON.stringify(submitData))
      this.$refs.treeForm.validate((valid) => {
        if (valid) {
          //提交审核
          return this.update(submitData);
        } else {
          return false;
        }
      });
    },
    // 提交审核
    update (form) {
      spJgzhBatch(form).then((res) => {
        if (res.code == 0) {
          this.$refs.spDialog.close();
          this.getList(this.search);
          this.$message.success(res.message);
        }
      });
    },
    // 取消
    cancel () {
      this.$refs.treeForm.resetFields();
      this.getList(this.search);
      this.$refs.spDialog.close();
    },

    /**敏感信息显隐**/
    viewSensitive (data, type) {
      let sensitiveStatus = data[type].includes("*") ? "1" : "2";
      querySensitive(data.id, sensitiveStatus).then(response => {
        const res = response.data;
        console.log("监管账户审核列表敏感信息响应结果：" + res);
        this.data.forEach(item => {
          if (item.id == data.id) {
            item[type] = res[type];
          }
        });
      });
    },

    // end

  },
};
</script>

<style scoped>
.icon-list {
  height: 220px;
  overflow-y: scroll;
}

/* 表单弹框最大化对齐顶部 */
.sp-form-dialog>>>.el-dialog {
  margin-top: 0vh !important;
}
</style>
