<template>
    <div class="app-container">
      <el-card class="search-form">
        <el-form :inline="true" :model="search" class="search-form" label-width="120px" size="small">
          <el-row :gutter="24">
            <el-col :span="10">
              <el-form-item label="业务代码" class="form-item-with-tip">
                <div class="input-with-tip">
                  <el-autocomplete
                    v-model="search.opeCd"
                    :fetch-suggestions="queryOpeCd"
                    placeholder="请输入业务代码"
                    @select="handleOpeCdSelect"
                    @clear="handlerOpeCdClear"
                    :trigger-on-focus="true"
                    class="custom-input"
                    clearable
                  >
                    <template slot-scope="{ item }">
                      <div class="autocomplete-item">
                        <div class="name">{{ item.value +'-'+ item.item.opeNm }}</div>
                      </div>
                    </template>
                  </el-autocomplete>
                  <span v-if="search.opeNm" class="tip-text">{{ search.opeNm }}</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="委托单位代码" class="form-item-with-tip">
                <div class="input-with-tip">
                  <el-autocomplete
                    v-model="search.merchId"
                    :fetch-suggestions="queryMerchId"
                    placeholder="请输入委托单位代码"
                    @select="handleMerchIdSelect"
                    @clear="handlerMerchIdClear"
                    :trigger-on-focus="true"
                    class="custom-input"
                    clearable
                  >
                    <template slot-scope="{ item }">
                      <div class="autocomplete-item">
                        <div class="name">{{ item.value + '-' + item.item.prdtNm}}</div>
                      </div>
                    </template>
                  </el-autocomplete>
                  <span v-if="search.merchNm" class="tip-text">{{ search.merchNm }}</span>
                </div>
              </el-form-item>
            </el-col>
            
          </el-row>
          <el-row :gutter="24">
            <el-col :span="10">
              <el-form-item label="子业务代码">
                <el-input v-model="search.subOpeId" clearable placeholder="请输入子业务代码" class="custom-input" />
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="子业务名称">
                <el-input v-model="search.subOpeNm" clearable placeholder="请输入子业务名称" class="custom-input" />
              </el-form-item>
            </el-col>
            <el-col :span="24" class="search-btns">
              <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
              <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
              <!-- <el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增</el-button> -->
            </el-col>
          </el-row>
        </el-form>
      </el-card>
  
      <yo-table
        v-loading="loading"
        :data="data"
        :option="option"
        ref="crud"
        :page.sync="page"
        :search.sync="search"
        @on-load="onLoad"
        @row-update="rowUpdate"
        @row-save="rowSave"
        @row-del="rowDel"
        v-model="formParent"
        @selection-change="handleSelectionChange"
      >
        <template slot-scope="{row,size,type}" slot="menu">
          <el-button
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(row)"
            v-hasPermi="['admin:merch:subope:edit']"
          >修改</el-button>
          <el-button
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(row)"
            v-hasPermi="['admin:merch:subope:delete']"
          >删除</el-button>
        </template>
      </yo-table>
    </div>
  </template>
  
  <script>
  import { merchSubOpeList, saveOrUpdateMerchSubOpe, deleteMerchSubOpe } from '@/api/subopecd';
  import { listGroupByMerchId, listGroupByOpeCd } from '@/api/merchope';
  import subOpecdOption from './infoData/subOpecdOption.js';
  
  export default {
    name: 'suboped',
    data() {
      return {
        loading: false,
        formParent: {},
        search: {
          opeCd: '',
          opeNm: '',
          merchId: '',
          merchNm: '',
          subOpeId: '',
          subOpeNm: ''
        },
        page: {
          pageSize: 10,
          currentPage: 1
        },
        data: [],
        option: subOpecdOption,
        multiple: true,
        selectedRows: [],
        // 自动完成相关数据
        opeCdLoading: false,
        merchIdLoading: false
      };
    },
    created() {
      this.getList();
    },
    methods: {
      onLoad() {
        this.getList();
      },
      async getList() {
        const params = { ...this.search, ...this.page };
        this.loading = true;
        try {
          const { code, data } = await merchSubOpeList(params);
          if (code === '0') {
            this.data = data.list;
            this.page.total = data.total;
          }
        } catch (error) {
          console.error('Error fetching data:', error);
        } finally {
          this.loading = false;
        }
      },
      handleQuery() {
        this.page.currentPage = 1;
        this.getList();
      },
      resetQuery() {
        this.search = {
          opeCd: '',
          opeNm: '',
          merchId: '',
          merchNm: '',
          subOpeId: '',
          subOpeNm: ''
        };
        this.handleQuery();
      },
      handleAdd() {
        this.$refs.crud.rowAdd();
      },
      handleUpdate(row) {
        this.$refs.crud.rowEdit(row);
      },
      handleDelete(row) {
        this.$modal.confirm('是否确认删除该子业务参数？').then(() => {
          deleteMerchSubOpe(row).then(response => {
            if (response.code === '0') {
              this.$modal.msgSuccess('删除成功');
              this.getList();
            }
          });
        });
      },
      handleSelectionChange(selection) {
        this.selectedRows = selection;
        this.multiple = !selection.length;
      },
      rowSave(form, done, loading) {
        saveOrUpdateMerchSubOpe(form)
          .then(response => {
            if (response.code === '0') {
              this.$modal.msgSuccess('新增成功');
              this.getList();
              done();
            }
          })
          .catch(() => {
            loading();
          });
      },
      rowUpdate(form, index, done, loading) {
        saveOrUpdateMerchSubOpe(form)
          .then(response => {
            if (response.code === '0') {
              this.$modal.msgSuccess('修改成功');
              this.getList();
              done();
            }
          })
          .catch(() => {
            loading();
          });
      },
      rowDel(row) {
        this.handleDelete(row);
      },
      // 查询业务代码
      async queryOpeCd(queryString, cb) {
        this.opeCdLoading = true;
        try {
          const params = {
            opeCd: queryString
          };
          const { code, data } = await listGroupByOpeCd(params);
          if (code === '0' && data) {
            const results = data.map(item => ({
              value: item.opeCd,
              label: item.opeNm,
              item: item
            }));
            cb(results);
          } else {
            cb([]);
          }
        } catch (error) {
          console.error('Error fetching opeCd:', error);
          cb([]);
        } finally {
          this.opeCdLoading = false;
        }
      },

      // 查询委托单位代码
      async queryMerchId(queryString, cb) {
        this.merchIdLoading = true;
        try {
          const params = {
            merchId: queryString,
            opeCd: this.search.opeCd
          };
          const { code, data } = await listGroupByMerchId(params);
          if (code === '0' && data) {
            const results = data.map(item => ({
              value: item.merchId,
              label: item.prdtNm,
              item: item
            }));
            cb(results);
          } else {
            cb([]);
          }
        } catch (error) {
          console.error('Error fetching merchId:', error);
          cb([]);
        } finally {
          this.merchIdLoading = false;
        }
      },

      // 清除方法
      handlerOpeCdClear() {
        this.search.opeCd = '';
        this.search.opeNm = '';
      },
      handlerMerchIdClear() {
        this.search.merchId = '';
        this.search.merchNm = '';
      },

      // 选择业务代码
      handleOpeCdSelect(item) {
        this.search.opeCd = item.value;
        this.search.opeNm = item.item.opeNm;
        // 清空委托单位代码，触发重新查询
        if (this.search.merchId) {
          this.search.merchId = '';
          this.search.merchNm = '';
        }
      },

      // 选择委托单位代码
      handleMerchIdSelect(item) {
        this.search.merchId = item.value;
        this.search.merchNm = item.item.prdtNm;
      },
    }
  };
  </script>
  
<style lang="scss" scoped>
.search-form {
  margin-bottom: 16px;
}

.form-item-with-tip {
  margin-bottom: 18px;
}

.input-with-tip {
  display: flex;
  align-items: center;
  gap: 8px;
}

.custom-input {
  width: 240px !important;
}

.tip-text {
  font-size: 12px;
  color: #606266;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 140px;
}

.app-container {
  padding: 20px;
}

.search-btns {
  text-align: right;
  padding-right: 50px;
}

.search-btns .el-button + .el-button {
  margin-left: 8px;
}

.autocomplete-item {
  display: flex;
  flex-direction: column;
}
.autocomplete-item .name {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

:deep(.el-form-item__label) {
  width: 120px !important;
  padding-right: 0px !important;
}

:deep(.el-form-item__content) {
  margin-left: 10px !important;
}
</style> 