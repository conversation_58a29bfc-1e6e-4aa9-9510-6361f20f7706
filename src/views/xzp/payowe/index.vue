<template>
  <div class="app-container">
    <el-card class="box-card">
      <el-form ref="queryForm" :model="search" :inline="true" label-width="100px">
        <el-row :gutter="24">
          <el-col :span="10">
            <el-form-item label="业务代码" class="form-item-with-tip">
              <div class="input-with-tip">
                <el-autocomplete
                  v-model="search.opeCd"
                  :fetch-suggestions="queryOpeCd"
                  placeholder="请输入业务代码"
                  @select="handleOpeCdSelect"
                  @clear="handlerOpeCdClear"
                  :trigger-on-focus="true"
                  class="custom-input"
                  clearable
                >
                  <template slot-scope="{ item }">
                    <div class="autocomplete-item">
                      <div class="name">{{ item.value + '-' + item.item.opeNm }}</div>
                    </div>
                  </template>
                </el-autocomplete>
                <span v-if="search.opeNm" class="tip-text value">{{ search.opeNm }}</span>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="委托单位代码" class="form-item-with-tip">
              <div class="input-with-tip">
                <el-autocomplete
                  v-model="search.merchId"
                  :fetch-suggestions="queryMerchId"
                  placeholder="请输入委托单位代码"
                  @select="handleMerchIdSelect"
                  @clear="handlerMerchIdClear"
                  :trigger-on-focus="true"
                  class="custom-input"
                  clearable
                >
                  <template slot-scope="{ item }">
                    <div class="autocomplete-item">
                      <div class="name">{{ item.value + '-' + item.item.prdtNm }}</div>
                    </div>
                  </template>
                </el-autocomplete>
                <span v-if="search.merchNm" class="tip-text value">{{ search.merchNm }}</span>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="10">
            <el-form-item label="起始日期">
              <el-date-picker
                v-model="search.startTime"
                type="date"
                placeholder="选择起始日期"
                value-format="yyyyMMdd"
                style="width: 200px;"
                @change="validateEndTime"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="终止日期">
              <el-date-picker
                v-model="search.endTime"
                type="date"
                placeholder="选择终止日期"
                value-format="yyyyMMdd"
                style="width: 200px;"
                @change="validateEndTime"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" style="text-align: right">
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <yo-table
      v-loading="loading"
      :option="option"
      :data="data"
      ref="crud"
      :page.sync="page"
      @on-load="getList"
      @search-change="searchChange"
      @refresh-change="refresh"
      v-model="formParent"
    >
      <template slot="payId" slot-scope="scope">
        <div>
          <span>{{scope.row.payId}}</span>
          <el-button
            v-if="scope.row.payId"
            type="text"
            icon="el-icon-view"
            @click="viewPayOweInfo(scope.row,'payId')"
          ></el-button>
        </div>
      </template>
      <template slot="accCardId" slot-scope="scope">
        <div>
          <span>{{scope.row.accCardId}}</span>
          <el-button
            v-if="scope.row.accCardId"
            type="text"
            icon="el-icon-view"
            @click="viewPayOweInfo(scope.row,'accCardId')"
          ></el-button>
        </div>
      </template>
      <template slot-scope="{row}" slot="menu">
        <el-button
          size="mini"
          type="text"
          icon="el-icon-info"
          @click="handleView(row)"
        >查看</el-button>
      </template>
    </yo-table>
  </div>
</template>

<script>
import { getPayOweList, payOweSensitive } from '@/api/payowe'
import { listGroupByMerchId, listGroupByOpeCd } from '@/api/merchope';
import payOweOption from './infoData/payOweOption.js'

export default {
  name: 'PayOwe',
  dicts: ['xzp_owe_status'],
  data() {
    return {
      loading: false,
      search: {
        opeCd: '',
        opeNm: '',
        merchId: '',
        merchNm: '',
        startTime: '',
        endTime: ''
      },
      page: {
        pageSize: 10,
        pageNum: 1,
        total: 0
      },
      data: [],
      option: payOweOption
    }
  },
  created() {
    this.getList();
    // 更新字典数据
    this.updateDictData(this.option.column, 'txnSta', this.dict.type.xzp_owe_status);
  },
  methods: {
    // 更新字典数据
    updateDictData(option, key, data) {
      let column = this.findObject(option, key);
      column.dicData = data;
    },
    searchChange(params, done) {
      this.handleQuery();
      setTimeout(() => {
        done();
      }, 1000);
    },
    refresh() {
      this.handleQuery();
    },
    /** 查询列表 */
    async getList() {
      const params = { ...this.search, ...this.page };
      this.loading = true;
      try {
        const { code, data } = await getPayOweList(params);
        if (code === '0') {
          this.data = data.list || [];
          this.page.total = data.total;
          
          // 在数据加载后默认进行脱敏
          this.data.forEach(row => {
            const fieldsToMask = ['payId', 'accCardId'];
            fieldsToMask.forEach(type => {
              const originalValue = row[type];
              if (originalValue) {
                const originalKey = `_original_${type}`;
                // 仅在尚未脱敏时进行处理
                if (!row[originalKey]) {
                  this.$set(row, originalKey, originalValue);
                  const maskedValue = String(originalValue).replace(/(?<=\w{3})\w(?=\w{4})/g, '*');
                  this.$set(row, type, maskedValue);
                }
              }
            });
          });
        }
      } finally {
        this.loading = false;
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.page.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 业务代码搜索 */
    async queryOpeCd(queryString, cb) {
      this.opeCdLoading = true
      try {
        const params = {
          opeCd: queryString
        }
        const { code, data } = await listGroupByOpeCd(params)
        if (code === '0' && data) {
          const results = data.map(item => ({
            value: item.opeCd,
            label: item.opeNm,
            item: item
          }))
          cb(results)
        } else {
          cb([])
        }
      } catch (error) {
        console.error('Error fetching opeCd:', error)
        cb([])
      } finally {
        this.opeCdLoading = false
      }
    },
    /** 委托单位代码搜索 */
    async queryMerchId(queryString, cb) {
      this.merchIdLoading = true
      try {
        const params = {
          merchId: queryString,
          opeCd: this.search.opeCd
        }
        const { code, data } = await listGroupByMerchId(params)
        if (code === '0' && data) {
          const results = data.map(item => ({
            value: item.merchId,
            label: item.prdtNm,
            item: item
          }))
          cb(results)
        } else {
          cb([])
        }
      } catch (error) {
        console.error('Error fetching merchId:', error)
        cb([])
      } finally {
        this.merchIdLoading = false
      }
    },
    /** 业务代码选择 */
    handleOpeCdSelect(item) {
      this.search.opeCd = item.value
      this.search.opeNm = item.item.opeNm
      this.search.merchId = ''
      this.search.merchNm = ''
    },
    /** 委托单位代码选择 */
    handleMerchIdSelect(item) {
      this.search.merchId = item.value
      this.search.merchNm = item.item.prdtNm
    },
    /** 清空业务代码 */
    handlerOpeCdClear() {
      this.search.opeCd = ''
      this.search.opeNm = ''
      this.search.merchId = ''
      this.search.merchNm = ''
    },
    /** 清空委托单位代码 */
    handlerMerchIdClear() {
      this.search.merchId = ''
      this.search.merchNm = ''
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.$refs.crud.rowView(row);
    },
    /** 重置表单 */
    resetForm(formName) {
      if (this.$refs[formName]) {
        this.$refs[formName].resetFields()
      }
      this.search = {
        opeCd: '',
        opeNm: '',
        merchId: '',
        merchNm: '',
        startTime: '',
        endTime: ''
      }
    },
    /** 验证结束日期 */
    validateEndTime() {
      if (this.search.startTime && this.search.endTime) {
        if (Number(this.search.endTime) < Number(this.search.startTime)) {
          this.$message.warning('终止日期不能小于起始日期')
          this.search.endTime = ''
        }
      }
    },
    /** 敏感信息显隐 */
    viewPayOweInfo(row, type) {
      // // 注释掉API调用
      // // 假设使用流水号作为唯一标识
      // const uniqueId = row.serialNo
      // if (!uniqueId) {
      //   this.$message.warning('无法确定唯一记录，缺少流水号。')
      //   return
      // }
      // const sensitiveStatus = row[type].includes('**') ? '2' : '1' // 1: 脱敏, 2: 显示原文
      // payOweSensitive({ serialNo: uniqueId, status: sensitiveStatus }).then(response => {
      //   const res = response.data
      //   if (response.code === '0' && res) {
      //     this.data.forEach(item => {
      //       if (item.serialNo === uniqueId) {
      //         item[type] = res[type]
      //       }
      //     })
      //   }
      // })

      const originalKey = `_original_${type}`
      // 如果原始值已存储（表示当前是脱敏状态），则恢复
      if (row[originalKey]) {
        this.$set(row, type, row[originalKey])
        this.$delete(row, originalKey)
      } else { // 否则（表示当前是明文状态），进行脱敏
        const originalValue = row[type]
        if (!originalValue) {
          return
        }
        // 使用正则表达式进行脱敏: (?<=\w{3})\w(?=\w{4})
        const maskedValue = String(originalValue).replace(/(?<=\w{3})\w(?=\w{4})/g, '*')
        // 存储原始值并更新显示值为脱敏后的值
        this.$set(row, originalKey, originalValue)
        this.$set(row, type, maskedValue)
      }
    },
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.box-card {
  margin-bottom: 20px;
}
.custom-input {
  width: 200px;
}
.form-item-with-tip {
  margin-bottom: 18px;
}
.input-with-tip {
  display: flex;
  align-items: center;
}
.tip-text {
  margin-left: 8px;
  color: #909399;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 140px;
}
.autocomplete-item {
  display: flex;
  flex-direction: column;
}
.autocomplete-item .name {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}
</style> 