<template>
  <div class="app-container">
    <el-card class="search-form">
      <el-form :inline="true" :model="search" class="search-form" label-width="100px" size="small">
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="新卡号">
              <el-input
                v-model="search.account"
                placeholder="请输入新卡号"
                style="width: 200px;"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="旧卡号">
              <el-input
                v-model="search.str31"
                placeholder="请输入旧卡号"
                style="width: 200px;"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="用户号">
              <el-input
                v-model="search.payid"
                placeholder="请输入用户号"
                style="width: 200px;"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="子业务代码" class="form-item-with-tip">
              <div class="input-with-tip">
                <el-autocomplete
                  v-model="search.subOpecd"
                  :fetch-suggestions="querySubOpeCd"
                  placeholder="请输入子业务代码"
                  @select="handleSubOpeCdSelect"
                  @clear="handlerSubOpeCdClear"
                  :trigger-on-focus="true"
                  class="custom-input"
                  clearable
                >
                  <template slot-scope="{ item }">
                    <div class="autocomplete-item">
                      <div class="name">{{ item.value + '-' + item.item.subOpeNm }}</div>
                    </div>
                  </template>
                </el-autocomplete>
                <span v-if="search.subOpeNm" class="tip-text">{{ search.subOpeNm }}</span>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="业务代码" class="form-item-with-tip">
              <div class="input-with-tip">
                <el-autocomplete
                  v-model="search.opecd"
                  :fetch-suggestions="queryOpeCd"
                  placeholder="请输入业务代码"
                  @select="handleOpeCdSelect"
                  @clear="handlerOpeCdClear"
                  :trigger-on-focus="true"
                  class="custom-input"
                  clearable
                >
                  <template slot-scope="{ item }">
                    <div class="autocomplete-item">
                      <div class="name">{{ item.value + '-' + item.item.opeNm }}</div>
                    </div>
                  </template>
                </el-autocomplete>
                <span v-if="search.opeNm" class="tip-text">{{ search.opeNm }}</span>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="委托单位代码" class="form-item-with-tip">
              <div class="input-with-tip">
                <el-autocomplete
                  v-model="search.merchid"
                  :fetch-suggestions="queryMerchId"
                  placeholder="请输入委托单位代码"
                  @select="handleMerchIdSelect"
                  @clear="handlerMerchIdClear"
                  :trigger-on-focus="true"
                  class="custom-input"
                  clearable
                >
                  <template slot-scope="{ item }">
                    <div class="autocomplete-item">
                      <div class="name">{{ item.value + '-' + item.item.prdtNm }}</div>
                    </div>
                  </template>
                </el-autocomplete>
                <span v-if="search.merchNm" class="tip-text">{{ search.merchNm }}</span>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" class="search-btns">
            <el-button type="primary" icon="el-icon-search" @click="handleQuery" :loading="queryLoading">查询</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 查询结果展示区域 -->
    <el-card v-if="queryResult" class="result-card">
      <div slot="header" class="clearfix">
        <span>查询结果</span>
      </div>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="响应码">{{ queryResult.code }}</el-descriptions-item>
        <el-descriptions-item label="响应消息">{{ queryResult.message }}</el-descriptions-item>
        <el-descriptions-item label="查询时间">{{ queryTime }}</el-descriptions-item>
        <el-descriptions-item label="用户号">{{ search.payid }}</el-descriptions-item>
        <el-descriptions-item label="旧卡号">{{ search.str31 }}</el-descriptions-item>
        <el-descriptions-item label="新卡号">{{ search.account }}</el-descriptions-item>
      </el-descriptions>

      <div style="margin-top: 20px;">
        <h4>响应数据：</h4>
        <el-input
          type="textarea"
          :rows="10"
          v-model="formattedResult"
          readonly
          style="font-family: monospace;"
        ></el-input>
      </div>
    </el-card>
  </div>
</template>

<script>
import { cardChangeNotice } from '@/api/cardchange';
import { instTree } from '@/api/system/dept';
import { listGroupByMerchId, listGroupByOpeCd } from '@/api/merchope';
import { listGroupBySubOpeId } from '@/api/subope';

export default {
  name: 'CardChange',
  dicts: ['xzp_wl_url'],
  data() {
    return {
      search: {
        payid: '',
        str31: '',
        account: '',
        merchid: '',
        merchNm: '',
        opecd: '',
        opeNm: '',
        subOpecd: '',
        subOpeNm: ''
      },
      queryLoading: false,
      queryResult: null,
      queryTime: '',
      formattedResult: '',
      // 自动完成相关数据
      opeCdLoading: false,
      merchIdLoading: false,
      subOpeCdLoading: false
    };
  },
  async mounted() {
    // 等待用户信息加载完成后再初始化机构信息
    await this.waitForUserInfo();
    await this.initInstInfo();
  },
  methods: {
    // 字典加载完成回调
    onDictReady(dict) {
      console.log('字典加载完成:', dict);
    },

    // 等待用户信息加载完成
    async waitForUserInfo() {
      let retryCount = 0;
      const maxRetries = 10;

      while (retryCount < maxRetries) {
        if (this.$store.state.user.instId) {
          console.log('用户信息已加载完成');
          return;
        }

        console.log(`等待用户信息加载... (${retryCount + 1}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, 500));
        retryCount++;
      }

      console.warn('用户信息加载超时，将使用默认值');
    },

    // 初始化机构信息
    async initInstInfo() {
      try {
        console.log('开始初始化机构信息...');
        const currentInstId = this.$store.state.user.instId;
        console.log('当前用户instId:', currentInstId);
        console.log('当前用户完整信息:', this.$store.state.user);

        if (currentInstId) {
          // 获取机构树信息
          console.log('正在获取机构树信息...');
          const response = await instTree('inst');
          console.log('机构树API响应:', response);

          if (response && response.length > 0) {
            // 递归查找当前用户的机构信息
            const findInstInfo = (nodes, targetId) => {
              for (let node of nodes) {
                if (node.id === targetId) {
                  return node;
                }
                if (node.children && node.children.length > 0) {
                  const found = findInstInfo(node.children, targetId);
                  if (found) return found;
                }
              }
              return null;
            };

            const instInfo = findInstInfo(response, currentInstId);
            console.log('查找到的机构信息:', instInfo);

            if (instInfo) {
              // 更新store中的机构信息
              this.$store.commit('SET_ORGCODE', instInfo.brhCode || instInfo.id);
              this.$store.commit('SET_ORGDEGREE', instInfo.brhLvl || instInfo.level);
              this.$store.commit('SET_INSTLVL', instInfo.brhLvl || instInfo.level);

              console.log('机构信息已更新:', {
                orgCode: instInfo.brhCode || instInfo.id,
                orgDegree: instInfo.brhLvl || instInfo.level,
                instLvl: instInfo.brhLvl || instInfo.level
              });
            } else {
              console.warn('未找到匹配的机构信息，instId:', currentInstId);
            }
          } else {
            console.warn('机构树API返回数据为空');
          }
        } else {
          console.warn('用户instId为空，无法获取机构信息');
        }
      } catch (error) {
        console.error('获取机构信息失败:', error);
      }
    },

    // 提交查询
    async handleQuery() {
      // 必填字段校验
      if (!this.search.payid) {
        this.$message.warning('请输入用户号');
        return;
      }

      if (!this.search.str31) {
        this.$message.warning('请输入旧卡号');
        return;
      }

      if (!this.search.account) {
        this.$message.warning('请输入新卡号');
        return;
      }

      if (!this.search.merchid) {
        this.$message.warning('请输入委托单位代码');
        return;
      }

      if (!this.search.opecd) {
        this.$message.warning('请输入业务代码');
        return;
      }

      this.queryLoading = true;
      try {
        // 从数据字典获取目标URL (读取w0001的值)
        console.log('字典数据:', this.dict);
        console.log('xzp_wl_url字典:', this.dict.label.xzp_wl_url);
        const targetUrl = this.dict.label.xzp_wl_url['w0001'] || '';
        console.log('获取到的targetUrl:', targetUrl);

        // 校验目标URL
        if (!targetUrl) {
          this.$message.error('未配置目标URL，请联系管理员');
          return;
        }

        // 获取用户信息
        const userInfo = this.$store.state.user;
        const empname = userInfo.name || userInfo.userName;
        const empcode = userInfo.empCode || userInfo.userId;
        const orgcode = userInfo.orgCode || userInfo.instId;
        const orgdegree = userInfo.orgDegree || userInfo.instLvl || '2';

        // 校验用户信息
        if (!empname) {
          this.$message.error('未获取到员工姓名，请重新登录');
          return;
        }
        if (!empcode) {
          this.$message.error('未获取到员工代码，请重新登录');
          return;
        }
        if (!orgcode) {
          this.$message.error('未获取到机构代码，请重新登录');
          return;
        }

        const requestData = {
          busikind: 'XMPLDSF',
          tradecode: 'A004',
          merchid: this.search.merchid,
          opecd: this.search.opecd,
          empname: empname,
          empcode: empcode,
          orgcode: orgcode,
          orgdegree: orgdegree,
          payid: this.search.payid,
          str31: this.search.str31,
          account: this.search.account,
          targetUrl: targetUrl,
          action: 'card_change'
        };

        console.log('发送请求参数:', requestData);
        const response = await cardChangeNotice(requestData);
        this.queryResult = response;
        this.queryTime = new Date().toLocaleString();
        this.formattedResult = JSON.stringify(response, null, 2);

        this.$message.success('查询成功');
      } catch (error) {
        console.error('Query error:', error);
        this.$message.error('查询失败：' + (error.message || '未知错误'));
      } finally {
        this.queryLoading = false;
      }
    },

    // 重置查询
    resetQuery() {
      this.search = {
        payid: '',
        str31: '',
        account: '',
        merchid: '',
        merchNm: '',
        opecd: '',
        opeNm: ''
      };
      this.queryResult = null;
      this.queryTime = '';
      this.formattedResult = '';
    },

    /** 业务代码搜索 */
    async queryOpeCd(queryString, cb) {
      this.opeCdLoading = true
      try {
        const params = {
          opeCd: queryString
        }
        const { code, data } = await listGroupByOpeCd(params)
        if (code === '0' && data) {
          const results = data.map(item => ({
            value: item.opeCd,
            label: item.opeNm,
            item: item
          }))
          cb(results)
        } else {
          cb([])
        }
      } catch (error) {
        console.error('Error fetching opeCd:', error)
        cb([])
      } finally {
        this.opeCdLoading = false
      }
    },

    /** 委托单位代码搜索 */
    async queryMerchId(queryString, cb) {
      this.merchIdLoading = true
      try {
        const params = {
          merchId: queryString,
          opeCd: this.search.opecd
        }
        const { code, data } = await listGroupByMerchId(params)
        if (code === '0' && data) {
          const results = data.map(item => ({
            value: item.merchId,
            label: item.prdtNm,
            item: item
          }))
          cb(results)
        } else {
          cb([])
        }
      } catch (error) {
        console.error('Error fetching merchId:', error)
        cb([])
      } finally {
        this.merchIdLoading = false
      }
    },

    /** 业务代码选择 */
    handleOpeCdSelect(item) {
      this.search.opecd = item.value
      this.search.opeNm = item.item.opeNm
      // 清空委托单位代码和子业务代码
      this.search.merchid = ''
      this.search.merchNm = ''
      this.search.subOpecd = ''
      this.search.subOpeNm = ''
    },

    /** 委托单位代码选择 */
    handleMerchIdSelect(item) {
      this.search.merchid = item.value
      this.search.merchNm = item.item.prdtNm
      // 清空子业务代码
      this.search.subOpecd = ''
      this.search.subOpeNm = ''
    },

    /** 清除业务代码 */
    handlerOpeCdClear() {
      this.search.opecd = ''
      this.search.opeNm = ''
      // 清空委托单位代码和子业务代码
      this.search.merchid = ''
      this.search.merchNm = ''
      this.search.subOpecd = ''
      this.search.subOpeNm = ''
    },

    /** 清除委托单位代码 */
    handlerMerchIdClear() {
      this.search.merchid = ''
      this.search.merchNm = ''
      // 清空子业务代码
      this.search.subOpecd = ''
      this.search.subOpeNm = ''
    },

    /** 查询子业务代码 */
    async querySubOpeCd(queryString, cb) {
      this.subOpeCdLoading = true;
      try {
        const params = {
          subOpeId: queryString,
          opeCd: this.search.opecd,
          merchId: this.search.merchid
        };
        const { code, data } = await listGroupBySubOpeId(params);
        if (code === '0' && data) {
          const results = data.map(item => ({
            value: item.subOpeId,
            label: item.subOpeNm,
            item: item
          }));
          cb(results);
        } else {
          cb([]);
        }
      } catch (error) {
        console.error('Error fetching subOpeCd:', error);
        cb([]);
      } finally {
        this.subOpeCdLoading = false;
      }
    },

    /** 选择子业务代码 */
    handleSubOpeCdSelect(item) {
      this.search.subOpecd = item.value;
      this.search.subOpeNm = item.item.subOpeNm;
    },

    /** 清除子业务代码 */
    handlerSubOpeCdClear() {
      this.search.subOpecd = '';
      this.search.subOpeNm = '';
    }
  }
};
</script>

<style scoped>
.search-form {
  margin-bottom: 10px;
}

.app-container {
  padding: 20px;
}

.search-btns {
  text-align: right;
}

.result-card {
  margin-top: 20px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.custom-input {
  width: 200px;
}

.form-item-with-tip {
  margin-bottom: 18px;
  .input-with-tip {
    display: flex;
    align-items: center;
    .tip-text {
      margin-left: 8px;
      color: #909399;
      font-size: 12px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 140px;
    }
  }
}

.autocomplete-item {
  display: flex;
  flex-direction: column;
}

.autocomplete-item .name {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}
</style>
