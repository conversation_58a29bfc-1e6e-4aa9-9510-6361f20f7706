<template>
  <div class="app-container">
    <yo-table
      ref="crud"
      :option="option"
      :data="data"
      v-model="form"
      :table-loading="loading"
      :page.sync="page"
      :search.sync="query"
      @on-load="onLoad"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
    >
      <!-- 自定义查询区域控件 -->
      <template slot="opeCdSearch" slot-scope="{column, size, disabled, readonly}">
        <div class="input-with-tip">
          <el-autocomplete
            v-model="query.opeCd"
            :fetch-suggestions="queryOpeCd"
            :placeholder="column.placeholder"
            @select="handleOpeCdSelect"
            @clear="handleOpeCdClear"
            :trigger-on-focus="column.triggerOnFocus"
            :clearable="column.clearable"
            :size="size"
            :disabled="disabled"
            :readonly="readonly"
            class="custom-input"
          >
            <template slot-scope="{ item }">
              <div class="autocomplete-item">
                <div class="name">{{ item.value + '-' + item.label }}</div>
              </div>
            </template>
          </el-autocomplete>
          <span v-if="query.opeNm" class="tip-text">{{ query.opeNm }}</span>
        </div>
      </template>
      <template slot="merchIdSearch" slot-scope="{column, size, disabled, readonly}">
        <div class="input-with-tip">
          <el-autocomplete
            v-model="query.merchId"
            :fetch-suggestions="queryMerchId"
            :placeholder="column.placeholder"
            @select="handleMerchIdSelect"
            @clear="handleMerchIdClear"
            :trigger-on-focus="column.triggerOnFocus"
            :clearable="column.clearable"
            :size="size"
            :disabled="disabled"
            :readonly="readonly"
            class="custom-input"
          >
            <template slot-scope="{ item }">
              <div class="autocomplete-item">
                <div class="name">{{ item.value + '-' + item.label }}</div>
              </div>
            </template>
          </el-autocomplete>
          <span v-if="query.merchNm" class="tip-text">{{ query.merchNm }}</span>
        </div>
      </template>
      <!-- 自定义委托单位代码自动完成 -->
      

      <!-- 自定义弹框控件 -->
      <template slot="opeCdForm" slot-scope="{column, size, disabled, readonly}">
        <el-autocomplete
          v-model="form.opeCd"
          :fetch-suggestions="queryOpeCdForm"
          :placeholder="column.placeholder"
          @select="handleOpeCdFormSelect"
          @clear="handleOpeCdFormClear"
          @input="handleOpeCdFormInput"
          :trigger-on-focus="column.triggerOnFocus"
          :clearable="column.clearable"
          :size="size"
          :disabled="disabled"
          :readonly="readonly"
          style="width: 100%;"
        >
          <template slot-scope="{ item }">
            <div class="autocomplete-item">
              <div class="name">{{ item.value + '-' + item.label }}</div>
            </div>
          </template>
        </el-autocomplete>
      </template>
      <template slot="merchIdForm" slot-scope="{column, size, disabled, readonly}">
        <el-autocomplete
          v-model="form.merchId"
          :fetch-suggestions="queryMerchIdForm"
          :placeholder="column.placeholder"
          @select="handleMerchIdFormSelect"
          @clear="handleMerchIdFormClear"
          @input="handleMerchIdFormInput"
          :trigger-on-focus="column.triggerOnFocus"
          :clearable="column.clearable"
          :size="size"
          :disabled="disabled"
          :readonly="readonly"
          style="width: 100%;"
        >
          <template slot-scope="{ item }">
            <div class="autocomplete-item">
              <div class="name">{{ item.value + '-' + item.label }}</div>
            </div>
          </template>
        </el-autocomplete>
      </template>
      <template slot="menuLeft">
        <el-button
          type="danger"
          size="small"
          icon="el-icon-delete"
          plain
          v-hasPermi="['admin:xzp:acctmgmt:remove']"
          @click="handleDelete"
          :disabled="selectionList.length === 0"
        >
          删除
        </el-button>
      </template>

      <!-- 自定义监管账户列 -->
      <template slot="cpabAccId" slot-scope="scope">
        <div class="sensitive-field">
          <span>{{ scope.row.cpabAccId }}</span>
          <el-button
            v-if="scope.row.cpabAccId"
            type="text"
            icon="el-icon-view"
            @click="viewSensitiveInfo(scope.row, 'cpabAccId')"
            title="显隐敏感信息"
            class="sensitive-btn"
          ></el-button>
        </div>
      </template>

      <!-- 自定义监管账户名列 -->
      <template slot="acctNm" slot-scope="scope">
        <div class="sensitive-field">
          <span>{{ scope.row.acctNm }}</span>
          <el-button
            v-if="scope.row.acctNm"
            type="text"
            icon="el-icon-view"
            @click="viewSensitiveInfo(scope.row, 'acctNm')"
            title="显隐敏感信息"
            class="sensitive-btn"
          ></el-button>
        </div>
      </template>

      <!-- 自定义操作列 -->
      <template slot-scope="scope" slot="menu">
        <el-button
          type="text"
          size="small"
          icon="el-icon-view"
          v-hasPermi="['admin:xzp:acctmgmt:view']"
          @click="handleView(scope.row)"
        >
          查看
        </el-button>
        <el-button
          type="text"
          size="small"
          icon="el-icon-edit"
          v-hasPermi="['admin:xzp:acctmgmt:edit']"
          @click="handleEdit(scope.row, scope.$index)"
        >
          修改
        </el-button>
        <el-button
          type="text"
          size="small"
          icon="el-icon-delete"
          v-hasPermi="['admin:xzp:acctmgmt:remove']"
          @click="handleSingleDelete(scope.row, scope.$index)"
        >
          删除
        </el-button>
      </template>
    </yo-table>
  </div>
</template>

<script>
import { queryAcctInfoList, addAcctInfo, updateAcctInfo, deleteAcctInfo, getAcctSensitiveInfoByBizKey, getAcctInfoByOthMsg2Tx, deleteAcctInfoByOthMsg2Tx, updateAcctInfoByOthMsg2Tx } from '@/api/acctmgmt';
import { listGroupByMerchId, listGroupByOpeCd } from '@/api/merchope';
import option from './infoData/acctMgmtOption';
import { mapGetters } from 'vuex';

export default {
  name: 'AcctMgmt',
  dicts: ['xzp_acct_status'],
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: option,
      data: [],
      // 自动完成相关数据
      opeCdLoading: false,
      merchIdLoading: false
    };
  },
  computed: {
    ...mapGetters(['permission'])
  },
  created() {
    // 更新字典数据
    this.updateDictData(this.option.column, 'othMsg1Tx', this.dict.type.xzp_acct_status);
  },
  methods: {
    // 生成UUID
    generateUUID() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    },

    // 获取数据
    onLoad(page, params = {}) {
      this.loading = true;
      const query = {
        ...this.query,
        ...params,
        current: page.currentPage,
        size: page.pageSize
      };

      queryAcctInfoList(query).then(res => {
        const data = res.data.data || res.data;
        this.data = data.list || data.records || [];
        this.page.total = data.total || 0;

        // 后端已经默认进行脱敏处理，前端不需要再处理
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    
    // 搜索回调
    searchChange(params, done) {
      // 合并搜索参数，保留自定义字段的值
      this.query = { ...this.query, ...params };
      this.page.currentPage = 1;
      this.onLoad(this.page, this.query);
      done();
    },
    
    // 搜索重置
    searchReset() {
      this.query = {};
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    
    // 选择改变
    selectionChange(list) {
      this.selectionList = list;
    },
    
    // 当前页改变
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    
    // 页大小改变
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    
    // 刷新
    refreshChange() {
      this.onLoad(this.page);
    },
    
    // 新增前回调
    rowSave(row, loading, done) {
      // 设置默认值
      if (!row.transType) {
        row.transType = 'zzg';
      }

      // 为新增记录生成唯一标识
      if (!row.othMsg2Tx) {
        row.othMsg2Tx = this.generateUUID();
      }

      addAcctInfo(row).then(() => {
        loading();
        this.onLoad(this.page);
        // 移除新增成功提示，操作成功后自动刷新列表即可
      }).catch(() => {
        loading();
      });
    },
    
    // 修改前回调
    rowUpdate(row, index, loading, done) {
      // 使用oth_msg2_tx作为唯一标识进行更新
      const updatePromise = row.othMsg2Tx
        ? updateAcctInfoByOthMsg2Tx(row)
        : updateAcctInfo(row);

      updatePromise.then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: 'success',
          message: '修改成功!'
        });
      }).catch(() => {
        loading();
      });
    },
    
    // 删除回调
    rowDel(row, index) {
      // 使用oth_msg2_tx作为唯一标识进行删除
      if (row.othMsg2Tx) {
        return deleteAcctInfoByOthMsg2Tx(row.othMsg2Tx);
      } else {
        // 兼容旧数据，如果没有oth_msg2_tx则使用原来的方式
        return deleteAcctInfo(row.cpabAccId, row.acctNm);
      }
    },
    
    // 单行删除（逻辑删除）
    handleSingleDelete(row, index) {
      // 检查是否已经删除
      if (row.othMsg1Tx === '1') {
        this.$message.warning('该记录已被删除');
        return;
      }

      this.$confirm(`确定要删除监管账户"${row.cpabAccId}"的记录吗？\n注意：这是逻辑删除，记录将被标记为删除状态。`, '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 使用oth_msg2_tx作为唯一标识进行删除
        const deletePromise = row.othMsg2Tx
          ? deleteAcctInfoByOthMsg2Tx(row.othMsg2Tx)
          : deleteAcctInfo(row.cpabAccId, row.acctNm);

        deletePromise.then(() => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          });
          this.onLoad(this.page);
        }).catch(() => {
          this.$message.error('删除失败');
        });
      }).catch(() => {
        // 用户取消删除
      });
    },

    // 查看详情
    handleView(row) {
      // 使用oth_msg2_tx获取最新数据
      if (row.othMsg2Tx) {
        getAcctInfoByOthMsg2Tx(row.othMsg2Tx).then(res => {
          if (res.code === '0' && res.data) {
            this.$refs.crud.rowView(res.data);
          } else {
            // 如果通过oth_msg2_tx获取失败，则直接使用当前行数据
            this.$refs.crud.rowView(row);
          }
        }).catch(() => {
          // 如果API调用失败，则直接使用当前行数据
          this.$refs.crud.rowView(row);
        });
      } else {
        // 兼容旧数据，直接使用当前行数据查看
        this.$refs.crud.rowView(row);
      }
    },

    // 单行编辑
    handleEdit(row, index) {
      // 如果有oth_msg2_tx，先获取原始数据（非脱敏）再编辑
      if (row.othMsg2Tx) {
        getAcctInfoByOthMsg2Tx(row.othMsg2Tx).then(res => {
          if (res.code === '0' && res.data) {
            // 使用原始数据进行编辑，确保不会编辑脱敏后的数据
            this.form = { ...res.data };
            this.$refs.crud.rowEdit(res.data, index);
            // 移除获取数据成功提示
          } else {
            // 如果获取失败，使用当前行数据
            this.form = { ...row };
            this.$refs.crud.rowEdit(row, index);
            // 静默处理，不显示警告
          }
        }).catch(() => {
          // 如果API调用失败，使用当前行数据
          this.form = { ...row };
          this.$refs.crud.rowEdit(row, index);
          // 静默处理，不显示警告
        });
      } else {
        // 兼容旧数据，直接编辑
        this.form = { ...row };
        this.$refs.crud.rowEdit(row, index);
      }
    },

    // 批量删除（逻辑删除）
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择要删除的数据');
        return;
      }

      // 过滤掉已删除的记录
      const validItems = this.selectionList.filter(item => item.othMsg1Tx !== '1');
      if (validItems.length === 0) {
        this.$message.warning('所选记录已被删除');
        return;
      }

      // 移除过滤记录的提示，静默处理

      this.$confirm(`确定将选择的${validItems.length}条数据删除?\n注意：这是逻辑删除，记录将被标记为删除状态。`, '确认批量删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 使用oth_msg2_tx作为唯一标识进行批量删除
        const promises = validItems.map(item => {
          return item.othMsg2Tx
            ? deleteAcctInfoByOthMsg2Tx(item.othMsg2Tx)
            : deleteAcctInfo(item.cpabAccId, item.acctNm);
        });
        Promise.all(promises).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: 'success',
            message: '删除成功!'
          });
        }).catch(() => {
          this.$message.error('批量删除失败');
        });
      });
    },
    
    // 查询业务代码
    async queryOpeCd(queryString, cb) {
      this.opeCdLoading = true;
      try {
        const params = {
          opeCd: queryString
        };
        const { code, data } = await listGroupByOpeCd(params);
        if (code === '0' && data) {
          const results = data.map(item => ({
            value: item.opeCd,
            label: item.opeNm,
            item: item
          }));
          cb(results);
        } else {
          cb([]);
        }
      } catch (error) {
        console.error('Error fetching opeCd:', error);
        cb([]);
      } finally {
        this.opeCdLoading = false;
      }
    },

    // 查询委托单位代码（搜索区域）
    async queryMerchId(queryString, cb) {
      this.merchIdLoading = true;
      try {
        const params = {
          merchId: queryString,
          opeCd: this.query.opeCd // 添加业务代码参数进行联动查询
        };
        const { code, data } = await listGroupByMerchId(params);
        if (code === '0' && data) {
          const results = data.map(item => ({
            value: item.merchId,
            label: item.prdtNm,
            item: item
          }));
          cb(results);
        } else {
          cb([]);
        }
      } catch (error) {
        console.error('Error fetching merchId:', error);
        cb([]);
      } finally {
        this.merchIdLoading = false;
      }
    },

    // 查询业务代码（弹框表单）
    async queryOpeCdForm(queryString, cb) {
      this.opeCdLoading = true;
      try {
        const params = {
          opeCd: queryString
        };
        const { code, data } = await listGroupByOpeCd(params);
        if (code === '0' && data) {
          const results = data.map(item => ({
            value: item.opeCd,
            label: item.opeNm,
            item: item
          }));
          cb(results);
        } else {
          cb([]);
        }
      } catch (error) {
        console.error('Error fetching opeCd:', error);
        cb([]);
      } finally {
        this.opeCdLoading = false;
      }
    },

    // 查询委托单位代码（弹框表单）
    async queryMerchIdForm(queryString, cb) {
      this.merchIdLoading = true;
      try {
        const params = {
          merchId: queryString,
          opeCd: this.form.opeCd // 使用表单中的业务代码进行联动查询
        };
        const { code, data } = await listGroupByMerchId(params);
        if (code === '0' && data) {
          const results = data.map(item => ({
            value: item.merchId,
            label: item.prdtNm,
            item: item
          }));
          cb(results);
        } else {
          cb([]);
        }
      } catch (error) {
        console.error('Error fetching merchId:', error);
        cb([]);
      } finally {
        this.merchIdLoading = false;
      }
    },

    // 选择业务代码（搜索区域）
    handleOpeCdSelect(item) {
      this.query.opeCd = item.value;
      this.query.opeNm = item.item.opeNm;
      // 清空委托单位代码，因为业务代码变化时需要重新选择委托单位
      this.query.merchId = '';
      this.query.merchNm = '';
    },

    // 选择委托单位代码（搜索区域）
    handleMerchIdSelect(item) {
      this.query.merchId = item.value;
      this.query.merchNm = item.item.prdtNm;
    },

    // 清除业务代码（搜索区域）
    handleOpeCdClear() {
      this.query.opeCd = '';
      this.query.opeNm = '';
      // 同时清除委托单位代码
      this.query.merchId = '';
      this.query.merchNm = '';
    },

    // 清除委托单位代码（搜索区域）
    handleMerchIdClear() {
      this.query.merchId = '';
      this.query.merchNm = '';
    },

    // 选择业务代码（弹框表单）
    handleOpeCdFormSelect(item) {
      this.form.opeCd = item.value;
      this.form.opeNm = item.item.opeNm;
      // 清空委托单位代码，因为业务代码变化时需要重新选择委托单位
      this.form.merchId = '';
      this.form.merchNm = '';
      // 立即清除表单验证错误
      this.clearFormValidation(['opeCd', 'merchId']);
    },

    // 选择委托单位代码（弹框表单）
    handleMerchIdFormSelect(item) {
      this.form.merchId = item.value;
      this.form.merchNm = item.item.prdtNm;
      // 立即清除表单验证错误
      this.clearFormValidation(['merchId']);
    },

    // 清除业务代码（弹框表单）
    handleOpeCdFormClear() {
      this.form.opeCd = '';
      this.form.opeNm = '';
      // 同时清除委托单位代码
      this.form.merchId = '';
      this.form.merchNm = '';
      // 清除表单验证错误
      this.clearFormValidation(['opeCd', 'merchId']);
    },

    // 清除委托单位代码（弹框表单）
    handleMerchIdFormClear() {
      this.form.merchId = '';
      this.form.merchNm = '';
      // 清除表单验证错误
      this.clearFormValidation(['merchId']);
    },

    // 业务代码输入事件（弹框表单）
    handleOpeCdFormInput(value) {
      // 当用户手动输入时，如果有值就清除验证错误
      if (value && value.trim()) {
        this.clearFormValidation(['opeCd']);
      }
    },

    // 委托单位代码输入事件（弹框表单）
    handleMerchIdFormInput(value) {
      // 当用户手动输入时，如果有值就清除验证错误
      if (value && value.trim()) {
        this.clearFormValidation(['merchId']);
      }
    },

    // 更新字典数据
    updateDictData(option, key, data) {
      const column = this.findObject(option, key);
      if (column && data) {
        column.dicData = data;
      }
    },

    // 查找对象
    findObject(array, prop) {
      return array.find(item => item.prop === prop);
    },

    // 统一的表单验证清除方法
    clearFormValidation(fields) {
      // 立即清除验证
      this.$nextTick(() => {
        if (this.$refs.crud && this.$refs.crud.$refs.dialogForm) {
          if (Array.isArray(fields)) {
            fields.forEach(field => {
              this.$refs.crud.$refs.dialogForm.clearValidate(field);
            });
          } else {
            this.$refs.crud.$refs.dialogForm.clearValidate(fields);
          }
        }
      });

      // 延迟再次清除，确保验证完全清除
      setTimeout(() => {
        if (this.$refs.crud && this.$refs.crud.$refs.dialogForm) {
          if (Array.isArray(fields)) {
            fields.forEach(field => {
              this.$refs.crud.$refs.dialogForm.clearValidate(field);
            });
          } else {
            this.$refs.crud.$refs.dialogForm.clearValidate(fields);
          }
        }
      }, 100);
    },

    // 敏感信息显隐切换
    async viewSensitiveInfo(row, type) {
      try {
        // 检查必要的业务标识字段 - 使用oth_msg2_tx作为唯一标识
        if (!row.othMsg2Tx) {
          this.$message.error('缺少唯一标识信息，无法切换显示状态');
          return;
        }

        // 判断当前状态：如果sensitiveStatus为'1'表示已脱敏，需要显示明文
        // 如果sensitiveStatus为'2'或未设置，表示明文状态，需要脱敏
        const currentStatus = row.sensitiveStatus || '1'; // 默认为脱敏状态
        const targetStatus = currentStatus === '1' ? '2' : '1'; // 切换状态

        // 使用oth_msg2_tx调用后端接口获取对应状态的数据
        const response = await getAcctSensitiveInfoByBizKey(row.othMsg2Tx, targetStatus);

        if (response && response.data) {
          const sensitiveData = response.data;

          // 强制更新当前行的敏感字段
          row.cpabAccId = sensitiveData.cpabAccId;
          row.acctNm = sensitiveData.acctNm;
          row.sensitiveStatus = sensitiveData.sensitiveStatus;

          // 使用Vue的响应式更新
          this.$set(row, 'cpabAccId', sensitiveData.cpabAccId);
          this.$set(row, 'acctNm', sensitiveData.acctNm);
          this.$set(row, 'sensitiveStatus', sensitiveData.sensitiveStatus);

          // 强制重新渲染表格
          this.$forceUpdate();

          console.log('敏感信息切换成功:', {
            targetStatus,
            cpabAccId: sensitiveData.cpabAccId,
            acctNm: sensitiveData.acctNm,
            sensitiveStatus: sensitiveData.sensitiveStatus
          });

          // 移除敏感信息切换成功提示，状态变化已经在界面上体现
        } else {
          this.$message.error('获取敏感信息失败');
        }
      } catch (error) {
        console.error('切换敏感信息状态失败:', error);
        this.$message.error('操作失败，请重试');
      }
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.custom-input {
  width: 240px !important;
}

.input-with-tip {
  display: flex;
  align-items: center;
  width: 100%;
  max-width: 400px;
}

/* 查询区域样式优化 */
:deep(.yo-table__search .el-input) {
  width: 240px !important;
}

:deep(.yo-table__search .el-autocomplete) {
  width: 240px !important;
}

:deep(.yo-table__search .el-input__inner) {
  width: 240px !important;
}

:deep(.yo-table__search .el-autocomplete-suggestion) {
  width: 240px !important;
}

.tip-text {
  margin-left: 8px;
  color: #909399;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 140px;
}

.autocomplete-item {
  display: flex;
  flex-direction: column;
}
.autocomplete-item .value {
  font-size: 14px;
  color: #606266;
}
.autocomplete-item .name {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.autocomplete-item .highlighted .addr {
  color: #ddd;
}

/* 搜索按钮区域样式 */
:deep(.yo-table__search-menu) {
  text-align: right;
  padding: 15px 0 10px 0;
  min-height: 50px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  border-top: 1px solid #ebeef5;
  margin-top: 10px;
  width: 100%;
}

:deep(.yo-table__search-menu .el-button) {
  margin-left: 8px;
  min-width: 80px;
}

/* 查询区域整体布局优化 */
:deep(.yo-table__search) {
  padding: 20px;
  background: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 20px;
}

:deep(.yo-table__search .el-row) {
  margin-bottom: 0;
  display: flex;
  flex-wrap: wrap;
}

:deep(.yo-table__search .el-col) {
  padding-right: 20px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

:deep(.yo-table__search .el-form-item) {
  margin-bottom: 0;
  width: 100%;
  display: flex;
  align-items: center;
}

:deep(.yo-table__search .el-form-item__label) {
  white-space: nowrap;
  padding-right: 12px;
  min-width: 120px;
  text-align: right;
}

:deep(.yo-table__search .el-form-item__content) {
  flex: 1;
  min-width: 240px;
  max-width: 400px;
}

/* 确保选中记录框有足够空间 */
:deep(.yo-table__menu-left) {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
  min-height: 40px;
}

:deep(.yo-table__menu-left .el-button) {
  margin-right: 8px;
  margin-bottom: 0;
}

/* 表格头部操作区域样式 */
:deep(.yo-table__menu) {
  padding: 16px 0;
  background: #fff;
  border-bottom: 1px solid #ebeef5;
}

/* 响应式布局优化 */
@media (max-width: 1400px) {
  :deep(.yo-table__search .el-col) {
    padding-right: 10px;
  }
}

@media (max-width: 1200px) {
  .custom-input {
    width: 200px !important;
  }

  :deep(.yo-table__search .el-input) {
    width: 200px !important;
  }

  :deep(.yo-table__search .el-autocomplete) {
    width: 200px !important;
  }

  :deep(.yo-table__search .el-form-item__content) {
    min-width: 200px;
    max-width: 350px;
  }

  :deep(.yo-table__search .el-form-item__label) {
    min-width: 100px;
  }
}

@media (max-width: 768px) {
  :deep(.yo-table__search .el-col) {
    flex: 0 0 100%;
    max-width: 100%;
    padding-right: 0;
  }

  .custom-input {
    width: 100% !important;
  }

  :deep(.yo-table__search .el-input) {
    width: 100%;
  }

  :deep(.yo-table__search .el-autocomplete) {
    width: 100%;
  }
}

/* 脱敏字段样式 */
.sensitive-field {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sensitive-btn {
  margin-left: 8px;
  padding: 0 4px;
  font-size: 12px;
  color: #409eff;
}

.sensitive-btn:hover {
  color: #66b1ff;
}
</style>