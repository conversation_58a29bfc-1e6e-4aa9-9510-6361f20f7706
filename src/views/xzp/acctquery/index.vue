<template>
  <div class="app-container">
    <el-card class="search-form">
      <el-form :inline="true" :model="search" class="search-form" label-width="100px" size="small">
        <el-row :gutter="24">
          <el-col :span="12" style="display: flex;align-items: center;">
            <el-form-item label="账户">
              <el-autocomplete
                v-model="search.account"
                :fetch-suggestions="queryAcctInfo"
                placeholder="请输入户名或公司账户"
                @select="handleAcctSelect"
                @clear="handlerAcctClear"
                :trigger-on-focus="true"
                style="width: 200px;"
                clearable
              >
                <template slot-scope="{ item }">
                  <div class="autocomplete-item">
                    <div class="name">{{ item.value + '-' + item.item.acctNm }}</div>
                  </div>
                </template>
              </el-autocomplete>
            </el-form-item>
            <span class="tip-text">{{ search.acctNm }}</span>
          </el-col>
        </el-row>
        <el-row :gutter="24" style="margin-top: 0;">
          <el-col :span="10">
            <el-form-item label="开始日期">
              <el-date-picker 
                style="width: 200px;" 
                v-model="search.bgnDate" 
                type="date" 
                value-format="yyyyMMdd" 
                placeholder="请选择开始日期" 
                clearable 
                @change="validateEndTime"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束日期">
              <el-date-picker 
                style="width: 200px;" 
                v-model="search.endDate" 
                type="date" 
                value-format="yyyyMMdd" 
                placeholder="请选择结束日期" 
                clearable 
                @change="validateEndTime"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" class="search-btns">
            <el-button type="primary" icon="el-icon-search" @click="handleQuery" :loading="queryLoading">查询</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 查询结果展示区域 -->
    <el-card v-if="queryResult" class="result-card">
      <div slot="header" class="clearfix">
        <span>查询结果</span>
      </div>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="响应码">{{ queryResult.code }}</el-descriptions-item>
        <el-descriptions-item label="响应消息">{{ queryResult.message }}</el-descriptions-item>
        <el-descriptions-item label="查询时间">{{ queryTime }}</el-descriptions-item>
        <el-descriptions-item label="账户信息">{{ search.acctNm || search.account }}</el-descriptions-item>
      </el-descriptions>
      
      <div style="margin-top: 20px;">
        <h4>响应数据：</h4>
        <el-input
          type="textarea"
          :rows="10"
          v-model="formattedResult"
          readonly
          style="font-family: monospace;"
        ></el-input>
      </div>
    </el-card>
  </div>
</template>

<script>
import { suggestAcctInfo, businessQuery } from '@/api/acctquery';
import { instTree } from '@/api/system/dept';

export default {
  name: 'AcctQuery',
  dicts: ['xzp_wl_url'],
  data() {
    return {
      search: {
        account: '',
        acctNm: '',
        bgnDate: '',
        endDate: ''
      },
      acctLoading: false,
      queryLoading: false,
      queryResult: null,
      queryTime: '',
      formattedResult: ''
    };
  },
  async mounted() {
    // 等待用户信息加载完成后再初始化机构信息
    await this.waitForUserInfo();
    await this.initInstInfo();
  },
  methods: {
    // 等待用户信息加载完成
    async waitForUserInfo() {
      let retryCount = 0;
      const maxRetries = 10;
      
      while (retryCount < maxRetries) {
        if (this.$store.state.user.instId) {
          console.log('用户信息已加载完成');
          return;
        }
        
        console.log(`等待用户信息加载... (${retryCount + 1}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, 500));
        retryCount++;
      }
      
      console.warn('等待用户信息超时，可能用户信息未正确加载');
    },
    
    // 初始化机构信息
    async initInstInfo() {
      try {
        console.log('开始初始化机构信息...');
        const currentInstId = this.$store.state.user.instId;
        console.log('当前用户instId:', currentInstId);
        console.log('当前用户完整信息:', this.$store.state.user);
        
        if (currentInstId) {
          // 获取机构树信息
          console.log('正在获取机构树信息...');
          const response = await instTree('inst');
          console.log('机构树API响应:', response);
          
          if (response && response.length > 0) {
            // 递归查找当前用户的机构信息
            const findInstInfo = (nodes, targetId) => {
              for (let node of nodes) {
                if (node.id === targetId) {
                  return node;
                }
                if (node.children && node.children.length > 0) {
                  const found = findInstInfo(node.children, targetId);
                  if (found) return found;
                }
              }
              return null;
            };
            
            const instInfo = findInstInfo(response, currentInstId);
            console.log('查找到的机构信息:', instInfo);
            
            if (instInfo) {
              // 更新store中的机构信息
              this.$store.commit('SET_ORGCODE', instInfo.brhCode || instInfo.id);
              this.$store.commit('SET_ORGDEGREE', instInfo.brhLvl || instInfo.level);
              this.$store.commit('SET_INSTLVL', instInfo.brhLvl || instInfo.level);
              
              console.log('机构信息已更新:', {
                orgCode: instInfo.brhCode || instInfo.id,
                orgDegree: instInfo.brhLvl || instInfo.level,
                instLvl: instInfo.brhLvl || instInfo.level
              });
            } else {
              console.warn('未找到匹配的机构信息，instId:', currentInstId);
            }
          } else {
            console.warn('机构树API返回数据为空');
          }
        } else {
          console.warn('用户instId为空，无法获取机构信息');
        }
      } catch (error) {
        console.error('获取机构信息失败:', error);
      }
    },
    // 查询账户信息联想
    async queryAcctInfo(queryString, cb) {
      this.acctLoading = true;
      try {
        const { code, data } = await suggestAcctInfo(queryString);
        if (code === '0' && data && data.length > 0) {
          const results = data.map(item => ({
            value: item.cpabAccId,
            label: item.acctNm,
            item: item
          }));
          cb(results);
        } else {
          cb([]);
        }
      } catch (error) {
        console.error('Error fetching account info:', error);
        cb([]);
      } finally {
        this.acctLoading = false;
      }
    },

    // 选择账户
    handleAcctSelect(item) {
      this.search.account = item.value;
      this.search.acctNm = item.item.acctNm;
    },

    // 清除账户
    handlerAcctClear() {
      this.search.account = '';
      this.search.acctNm = '';
    },

    // 提交查询
    async handleQuery() {
      if (!this.search.account) {
        this.$message.warning('请选择账户');
        return;
      }
      if (!this.search.bgnDate || !this.search.endDate) {
        this.$message.warning('请选择开始日期和结束日期');
        return;
      }

      this.queryLoading = true;
      try {
        // 获取数据字典中w0000对应的targetUrl值
        const targetUrl = this.dict.label.xzp_wl_url['w0000'] || '';
        
        const requestData = {
          busikind: 'CHANGE',
          tradecode: 'Feedbac',
          merchid: '************',
          opecd: '101064',
          empname: this.$store.state.user.name || this.$store.state.user.userName,
          empcode: this.$store.state.user.empCode || this.$store.state.user.userId, 
          orgcode: this.$store.state.user.orgCode || this.$store.state.user.instId, 
          orgdegree: this.$store.state.user.orgDegree || this.$store.state.user.instLvl || '2',
          account: this.search.account,
          bgn_date: this.search.bgnDate,
          end_date: this.search.endDate,
          targetUrl: targetUrl,
          action: 'query'
        };

        const response = await businessQuery(requestData);
        this.queryResult = response;
        this.queryTime = new Date().toLocaleString();
        this.formattedResult = JSON.stringify(response, null, 2);
        
        this.$message.success('查询成功');
      } catch (error) {
        console.error('Query error:', error);
        this.$message.error('查询失败：' + (error.message || '未知错误'));
      } finally {
        this.queryLoading = false;
      }
    },

    // 重置查询
    resetQuery() {
      this.search = {
        account: '',
        acctNm: '',
        bgnDate: '',
        endDate: ''
      };
      this.queryResult = null;
      this.queryTime = '';
      this.formattedResult = '';
    },

    // 验证结束时间
    validateEndTime() {
      if (this.search.bgnDate && this.search.endDate) {
        if (Number(this.search.endDate) < Number(this.search.bgnDate)) {
          this.$message.warning('结束日期不能小于开始日期');
          this.search.endDate = '';
        }
      }
    }
  }
};
</script>

<style scoped>
.search-form {
  margin-bottom: 10px;
}

.app-container {
  padding: 20px;
}

.search-btns {
  text-align: right;
}

.tip-text {
  font-size: 12px;
  color: #606266;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 140px;
}

.autocomplete-item {
  display: flex;
  flex-direction: column;
}

.autocomplete-item .value {
  font-size: 14px;
  color: #606266;
}

.autocomplete-item .name {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.result-card {
  margin-top: 20px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
</style>