// 批量交易控制表信息列表、弹框项
export default {
  index: true,
  indexLabel: '序号',
  rowKey: 'batchId',
  reserveSelection: true,
  selection: false,
  align: 'center',
  card: true,
  menuAlign: 'center',
  emptyBtnIcon: 'el-icon-refresh',
  searchMenuSpan: 6,
  searchMenuPosition: 'left',
  viewTitle: '批量交易控制表',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  searchBtn: false,
  refreshBtn: false,
  emptyBtn: false,
  labelWidth: 120,
  labelPosition: 'right',
  tip: false,
  columnBtn: false,
  menu: true,
  column: [
    {
      label: '外联批次号',
      width:120,
      prop: 'batchId',
      editDisabled: true
    },
    
    { label: '业务代码', prop: 'opeCd', editDisabled: true },
    { label: '委托单位代码', prop: 'merchId',editDisabled: true,width:120 },
    { label: '计划上账日期', prop: 'planTranDt',editDisabled: true,width:120 },
    { label: '实际上账日期', prop: 'tranDt',editDisabled: true,width:120},
    { label: '总笔数', prop: 'totalQt',editDisabled: true },
    { label: '总金额', prop: 'totalQt',editDisabled: true },
    { label: '成功笔数', prop: 'succQt',editDisabled: true },
    { label: '成功金额', prop: 'succAt',editDisabled: true },
    { label: '失败笔数', prop: 'failQt',editDisabled: true },
    { label: '失败金额', prop: 'failAt',editDisabled: true },
    {
      label: '状态',
      prop: 'procFg',
      type: 'select',
      width:200,
      dicData: [], // 将通过updateDictData方法动态更新
    },
    { label: '文件入库日期', prop: 'fileInaccDt',editDisabled: true, width: 100 },
    { label: '委托方源文件名', prop: 'reqFileNameTx',editDisabled: true,width:220 },
    { label: '回盘文件名', prop: 'rspFileName',editDisabled: true, width:200 },
    { label: '中平批次号', width:120, prop: 'coreBatchId', editDisabled: true, }
  ]
}; 