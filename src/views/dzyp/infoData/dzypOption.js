// 抵质押品列表、弹框项
import validate from '@/utils/validate';
export default {
  index: true,
  indexLabel: '序号',
  rowKey: 'id',
  reserveSelection: true,
  selection: true,
  align: 'center',
  card: true,
  menuAlign: 'center',
  emptyBtnIcon: 'el-icon-refresh',
  searchMenuSpan: 6,
  searchMenuPosition: 'left',
  addTitle: '新增抵质押品',
  viewTitle: '抵质押品查询',
  editTitle: '修改抵质押品',
  editBtnText: '修改',
  updateBtnText: '保存',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  searchBtn: false,
  refreshBtn: false,
  emptyBtn: false,
  labelWidth: 120,
  labelPosition: 'right',
  tip: false,
  columnBtn: false,
  menuWidth: 100,
  column: [
    {
      label: 'ID',
      prop: 'id',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '库存序号',
      prop: 'stockNum',
      slot: true,
      formslot: true, //编辑需要
      showColumn: false,
      editDisabled: true, //不可修改
      span: 24, //一行
      addDisplay: true,

      //查询设置：
      search: true, //查询条件
      searchLabelWidth: 150, //左边长度
      // searchSpan: 6,//右边长度
      rules: [{
        required: true,
        message: "请输入库存序号",
        trigger: "change"
      }],
    },
    {
      label: '入库时间',
      prop: 'impTime',
      slot: true,
      width: 180,
      showColumn: false,
      addDisplay: true,
      editDisplay: false,
      span: 24,
      rules: [{
        required: true,
        message: "请输入入库时间",
        trigger: "change"
      }],
    },
    {
      label: '他项类型',
      prop: 'othType',
      slot: true,
      width: 150,
      showColumn: false,
      addDisplay: true,
      editDisplay: false,
      span: 24
    },
    {
      label: '他项权证号',
      prop: 'othWarrantNum',
      width: 180,
      formslot: true,
      showColumn: false,
      editDisabled: true,
      // labelWidth: 180,
      span: 24,
      addDisplay: true,
      //查询设置：
      search: true, //查询条件
      searchLabelWidth: 100, //左边长度
      rules: [{
        required: true,
        message: "请输入他项权证号",
        trigger: "change"
      }],
    },
    {
      label: '他项登记日期',
      prop: 'othRecordDate',
      width: 120,
      slot: true,
      showColumn: false,
      addDisplay: true,
      editDisplay: false,
      span: 24,
    },
    {
      label: '借款人姓名',
      prop: 'borrowName',
      width: 120,
      slot: true,
      showColumn: false,
      addDisplay: true,
      editDisplay: false,
      span: 24,
    },
    {
      label: '借款人身份证',
      prop: 'borrowIdCard',
      slot: true,
      width: 150,
      showColumn: false,
      addDisplay: true,
      editDisplay: false,
      span: 24,

    },
    {
      label: '经办机构',
      prop: 'handleOrg',
      slot: true,
      showColumn: false,
      addDisplay: true,
      editDisplay: false,
      span: 24,
    },
    {
      label: '经办客户经理',
      prop: 'handleCustManager',
      slot: true,
      width: 120,
      showColumn: false,
      addDisplay: true,
      editDisplay: false,
      span: 24,
    },
    //字典项
    {
      label: '贷款种类',
      prop: 'loanType',
      slot: true,
      showColumn: false,
      addDisplay: true,
      editDisplay: false,
      span: 24,
    },
    {
      label: '借款合同号',
      prop: 'loanContractNum',
      width: 150,
      slot: true,
      showColumn: false,
      addDisplay: true,
      editDisplay: false,
      span: 24,
    },
    {
      label: '贷款年限',
      prop: 'loanLife',
      slot: true,
      showColumn: false,
      addDisplay: true,
      editDisplay: false,
      span: 24,
    },
    {
      label: '入账价值',
      prop: 'recordedValue',
      slot: true,
      showColumn: false,
      addDisplay: true,
      editDisplay: false,
      span: 24,
    },
    {
      label: '封包编号',
      prop: 'packetNum',
      width: 150,
      slot: true,
      showColumn: false,
      addDisplay: true,
      editDisplay: false,
      span: 24,
    },
    {
      label: '交接人',
      prop: 'successor',
      slot: true,
      showColumn: false,
      addDisplay: true,
      editDisplay: false,
      span: 24,
      rules: [{
        required: true,
        message: "请输入交接人姓名",
        trigger: "change"
      }],
    },
    {
      label: '购房合同号',
      prop: 'purchaseContractNum',
      width: 150,
      slot: true,
      showColumn: false,
      addDisplay: true,
      editDisplay: false,
      span: 24,
    },
    {
      label: '备注信息',
      prop: 'noteInfo',
      width: 180,
      slot: true,
      // formslot: true,
      showColumn: false,
      addDisplay: true,
      span: 24,
    },
    {
      label: '补登记标志',
      prop: 'suppRegistMark',
      width: 120,
      type: "select",
      slot: true,
      formslot: true,
      // labelWidth: 180,
      span: 24,
      dicData: [{
        label: '已确认',
        value: "已确认"
      }, {
        label: '未确认',
        value: "未确认"
      }],
      rules: [{
        required: true,
        message: "请选择补登记标志",
        trigger: "change"
      }],
      //查询设置：
      search: true, //查询条件
      searchLabelWidth: 100, //左边长度
      // searchSpan: 6,//右边长度
    },
    {
      label: '库存状态',
      prop: 'stockStatus',
      type: "select",
      slot: true,
      formslot: true,
      // labelWidth: 180,
      span: 24,
      dicData: [{
        label: '正常',
        value: "正常"
      }, {
        label: '注销',
        value: "注销"
      }],
      rules: [{
        required: true,
        message: "请选择库存状态",
        trigger: "change"
      }],
      //查询设置：
      search: true, //查询条件
      searchLabelWidth: 120, //左边长度
      // searchSpan: 6,//右边长度
    },
    {
      label: '注销后抵质押品状态',
      prop: 'pledgeStatusAfterlogout',
      width: 180,
      type: "select",
      slot: true,
      formslot: true,
      // labelWidth: 180,
      span: 24,
      dicData: [{
        label: '不在库',
        value: "不在库"
      }, {
        label: '未领取',
        value: "未领取"
      }, {
        label: '已领取',
        value: "已领取"
      }, {
        label: '空',
        value: "kong"
      }],
      rules: [{
        required: true,
        message: "请选择注销后抵质押品状态",
        trigger: "change"
      }],
      //查询设置：
      search: true, //查询条件
      searchLabelWidth: 150, //左边长度
      // searchSpan: 6,//右边长度
    },
    {
      label: '产权证号',
      prop: 'propertycard',
      width: 150,
      slot: true,
      showColumn: false,
      addDisplay: true,
      editDisplay: false,
      span: 24,
      //查询设置：
      search: true, //查询条件
      searchLabelWidth: 100, //左边长度
      // searchSpan: 6,//右边长度
      rules: [{
        required: true,
        message: "请输入产权人证号",
        trigger: "change"
      }],
    },
    {
      label: '产权人姓名',
      prop: 'propertyownerName',
      width: 120,
      slot: true,
      showColumn: false,
      addDisplay: true,
      editDisplay: false,
      span: 24,
      //查询设置：
      search: true, //查询条件
      searchLabelWidth: 100, //左边长度
      // searchSpan: 6,//右边长度
      rules: [{
        required: true,
        message: "请输入产权人姓名",
        trigger: "change"
      }],
    },
    {
      label: '产权人身份证号',
      prop: 'propertyownerIdCard',
      width: 150,
      slot: true,
      showColumn: false,
      addDisplay: true,
      editDisplay: false,
      span: 24,
      //查询设置：
      search: true, //查询条件
      searchLabelWidth: 120, //左边长度
      // searchSpan: 6,//右边长度
    },
    {
      label: '产权地址',
      prop: 'propertyrightAddr',
      width: 180,
      slot: true,
      showColumn: false,
      addDisplay: true,
      editDisplay: false,
      span: 24,
      //查询设置：
      search: true, //查询条件
      searchLabelWidth: 150, //左边长度
      // searchSpan: 6,//右边长度
    },
    {
      label: '产权证本数',
      prop: 'propertycardNum',
      width: 120,
      slot: true,
      showColumn: false,
      addDisplay: true,
      span: 24,
    },
    {
      label: '产权证填发日期',
      prop: 'propertycardIssueDate',
      width: 150,
      slot: true,
      showColumn: false,
      addDisplay: true,
      editDisplay: false,
      span: 24,
    },
    {
      label: '份数',
      prop: 'undetermined',
      width: 120,
      slot: true,
      showColumn: false,
      addDisplay: true,
      span: 24,
    },
    {
      label: '创建人',
      prop: 'createBy',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '创建时间',
      prop: 'createTime',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '操作人',
      prop: 'updateBy',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
    },
    {
      label: '操作时间',
      prop: 'updateTime',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      dicData: [],
      //查询设置：
      search: true, //查询条件
      searchLabelWidth: 100, //左边长度
    },
    {
      label: '预留字段',
      prop: 'reserve',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '预留字段1',
      prop: 'reserveA',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '预留字段2',
      prop: 'reserveB',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '预留字段3',
      prop: 'reserveC',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '预留字段4',
      prop: 'reserveD',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
  ],
};
