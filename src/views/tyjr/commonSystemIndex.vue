<template>
    <div class="app-container">
        <yo-table
            @keyup.enter.native="handleQuery"
            v-loading="loading"
            :data="data"
            :option="option"
            ref="crud"
            :page.sync="page"
            :search.sync="search"
            :before-open="beforeOpen"
            @on-load="onLoad"
            @search-change="searchChange"
            @row-update="rowUpdate"
            @row-save="rowSave"
            @refresh-change="refresh"
            v-model="formParent"
            @selection-change="handleSelectionChange"
        >
            <template slot="searchMenu">
                <el-button
                    size="mini"
                    type="primary"
                    icon="el-icon-search"
                    @click.stop="handleQuery"
                    v-hasPermi="['admin:user:page']"
                >
                    查询
                </el-button>
                <el-button
                    size="mini"
                    icon="el-icon-refresh"
                    @click.stop="resetQuery()"
                >
                    重置
                </el-button>
            </template>
            <template slot="accessNetworkTypeForm">
                <el-select
                    :disabled="disabled"
                    v-model="formParent.accessNetworkType"
                    placeholder="请选择 访问网络类型"
                    clearable
                >
                    <el-option
                        v-for="dict in dict.type['tyjr_access_network_type']"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                </el-select>
            </template>

            <!-- 自定义列 -->
            <template slot="accessNetworkType" slot-scope="scope">
                <el-tag size="mini" effect="plain" :type="tagType(scope.row)">
                    {{ accessNetworkType(scope.row) }}
                </el-tag>
            </template>
            <!-- 自定义左侧操作栏 -->
            <template slot="menuLeft">
                <el-button
                    type="primary"
                    icon="el-icon-plus"
                    size="mini"
                    plain
                    v-hasPermi="['admin:user:add']"
                    @click.stop="handleAdd"
                >
                    新增
                </el-button>
                <el-button
                    type="danger"
                    plain
                    icon="el-icon-delete"
                    size="mini"
                    :disabled="multiple"
                    @click="handleDelete"
                    v-hasPermi="['admin:user:remove:batch']"
                >
                    批量删除
                </el-button>
            </template>
            <!-- 自定义右侧操作栏 -->
            <template slot="menuRight">
                <input
                    id="fileslist"
                    v-show="false"
                    type="file"
                    accept=".xls"
                    ref="fileRef"
                    @change="fileChange"
                />
            </template>
            <template slot-scope="{ row, size, type, index }" slot="menu">
                <el-button
                    :size="size"
                    :type="type"
                    icon="el-icon-edit"
                    @click.stop="handleEdit(row, index)"
                    v-hasPermi="['admin:user:edit']"
                >
                    修改
                </el-button>
                <el-button
                    :size="size"
                    :type="type"
                    icon="el-icon-delete"
                    @click="handleDelete(row)"
                    v-hasPermi="['admin:user:remove']"
                >
                    删除
                </el-button>
                <el-button
                    :size="size"
                    :type="type"
                    icon="el-icon-info"
                    @click="handleView(row)"
                    v-hasPermi="['admin:user:remove']"
                >
                    查看详情
                </el-button>
            </template>

            <!-- 自定义表单插槽 -->
            <template slot="sysNoForm">
                <el-input :disabled="true" v-model="sysNo" />
            </template>
        </yo-table>
        <el-dialog :title="title" :visible.sync="dialogVisible" width="30%">
            <span slot="footer" class="dialog-footer">
                <el-button
                    type="primary"
                    @click="dialogQueren(dialogType)"
                    size="mini"
                >
                    确 定
                </el-button>
                <el-button @click="dialogVisible = false" size="mini">
                    取 消
                </el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import { list, add, edit, remove, getAutoSysNo } from '@/api/tyjr';
import commonSystemOption from './infoData/commonSystemOption.js';
import { mapActions } from 'vuex';

var identity = '';
var telNumber = '';
var emailid = '';
export default {
    name: 'CommonSystem',
    dicts: ['tyjr_access_network_type'],
    components: {},
    data() {
        return {
            title: '',
            dialogType: '',
            loading: true,
            formParent: {},
            search: {},
            exportSearch: {},
            page: {
                pageSize: 10,
                pageNum: 1,
            },
            data: [],
            option: commonSystemOption,
            dialogVisible: false,
            // 非多个禁用
            multiple: true,
            disabled: false,
            sysNo: '',
        };
    },
    created() {
        /** *添加字典项数据*/
        this.updateDictData(
            this.option.column,
            'accessNetworkType',
            this.dict.type['tyjr_access_network_type']
        );
    },
    methods: {
        // 首次加载调用此方法
        onLoad(page) {
            this.getList(this.search);
            // this.$refs['crud'].toggleSelection()
        },
        handleView(row) {
            this.sysNo = row.sysNo;
            this.$refs.crud.rowView(row);
        },
        refreshList() {
            this.handleQuery();
        },
        // 更新字典数据
        updateDictData(option, key, data) {
            let column = this.findObject(option, key);
            column.dicData = data;
        },
        // 搜索按钮
        searchChange(params, done) {
            this.handleQuery();
            setTimeout(() => {
                done();
            }, 1000);
        },
        // 新增表单保存
        async rowSave(form, done, loading) {
            form['sysNo'] = this.sysNo;
            // form.deptIds = form.deptIds ? form.deptIds : [];
            let response = await add(form);
            if (response.code == 0) {
                this.$message.success(response.message);
                this.handleQuery();
                done();
            }
        },
        // 刷新按钮
        refresh() {
            this.handleQuery();
        },
        /** 查询列表数据 */
        async getList(query) {
            const params = { ...query, ...this.page };
            this.loading = true;
            let response = await list(params);
            if (response.code == 0) {
                this.data = response.data.list;
                this.page.total = response.data.total;
                this.loading = false;
            }
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.page.currentPage = 1;
            this.getList(this.search);
            this.exportSearch = this.search;
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.$refs.crud.searchReset();
            this.getList();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.sysId);
            this.multiple = !selection.length;
        },
        /** 新增按钮操作 */
        async handleAdd() {
            getAutoSysNo().then(res => {
                if (res.code == 0) {
                    this.sysNo = res.message;
                } else {
                    this.$message.error('系统编码生成错误');
                }
            });
            this.$refs.crud.rowAdd();
        },
        // 修改
        async handleEdit(row, index) {
            this.sysNo = row.sysNo;
            this.$refs.crud.rowEdit(row, index);
        },
        // 修改表单保存
        rowUpdate(form, index, done, loading) {
            edit(form).then(response => {
                done();
                if (response.code == 0) {
                    this.$message.success(response.message);
                    this.handleQuery();
                }
            });
        },
        async handleDel(id) {
            let res = await remove(id);
            if (res.code == 0) {
                this.$message.success(res.message);
                this.$refs.crud.toggleSelection();
                this.getList();
            }
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            this.$confirm(`是否永久删除选中的数据项？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                showCancelButton: true,
            })
                .then(async () => {
                    // 删除单个
                    if (row.sysId) {
                        this.handleDel(row.sysId);
                    } else {
                        //批量删除
                        if (
                            this.ids instanceof Array &&
                            this.ids != null &&
                            this.ids != '' &&
                            this.ids != undefined
                        ) {
                            this.handleDel(this.ids.join(','));
                        }
                    }
                })
                .catch(() => {});
        },

        /** 处理返回的流文件 */
        handleExportData(res) {
            if (!res) return;
            let data = res.data;
            let filename = res.headers['content-disposition'].split('=')[1];
            let _filename = decodeURI(filename);
            const link = document.createElement('a');
            //创建 Blob对象 可以存储二进制文件
            let blob = new Blob([data], { type: 'application/x-excel' });
            link.style.display = 'none';
            link.href = URL.createObjectURL(blob);
            link.download = _filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        },
        /** 下载模板 */
        exportExample() {
            downloadExample().then(res => {
                this.handleExportData(res);
            });
        },
        /** 导出按钮操作 */
        handleExport() {
            const data = { userIds: this.ids, ...this.exportSearch };
            exportUsers(data).then(res => {
                this.handleExportData(res);
            });
        },
        fileChange(event) {
            let file = event.target.files[0];
            let formData = new FormData();
            formData.append('file', file);
            importUsers(formData).then(res => {
                if (res.code == 0) {
                    this.$message.success(res.message);
                    this.handleQuery();
                }
            });
            // 清除文件，防止下次上传相同文件无反应
            event.target.value = null;
        },
        /** 导入按钮操作 */
        handleImport() {
            this.$refs.fileRef.dispatchEvent(new MouseEvent('click'));
            // this.$refs.fileRef.click();
        },
        //过滤列表数据
        filterData(data) {
            var _filterData = ele => {
                if (ele && ele.length) {
                    ele.forEach(item => {
                        if (item.type == 'inst') {
                            item.disabled = true;
                        }
                        if (item.children) {
                            _filterData(item.children);
                        }
                    });
                }
            };
            if (this.dialogType == 'dept') _filterData(data);
            return data;
        },
        /**敏感信息显隐**/
        viewUserInfo(data, type) {
            let sensitiveStatus = data[type].includes('**') ? '1' : '2';
            usersSensitive(data.userName, sensitiveStatus).then(response => {
                const res = response.data;
                this.data.forEach(item => {
                    if (item.userName == data.userName) {
                        item[type] = res[type];
                    }
                });
            });
        },
        checkChange(data, checked, node) {
            if (checked) {
                this.$refs.tree.setCheckedNodes([data]);
            }
        },
        treeShow(val) {
            this.title = val == 'dept' ? '部门列表' : '机构列表';
            this.dialogType = val;
            this.dialogVisible = true;
        },
        // 弹窗打开
        beforeOpen(done, type) {
          done();
        },
        dialogQueren(type) {
            if (type == 'dept') {
                let arr = this.$refs.tree.getCheckedNodes();
                let deptName = arr.map(i => {
                    return i.name;
                });
                this.deptName = deptName.join(',');
                let deptIds = arr.map(i => {
                    return i.id;
                });
                this.formParent.deptIds = deptIds;
            } else {
                let arr = this.$refs.tree.getCheckedNodes();
                if (!arr.length) {
                    this.instName = '';
                    this.formParent.instId = '';
                } else {
                    this.instName = arr[0].name;
                    this.formParent.instId = arr[0].id;
                }
            }
            this.dialogVisible = false;
        },
        accessNetworkType(row) {
            switch (row.accessNetworkType) {
                case '1':
                    return '内网访问';
                case '2':
                    return '外网访问';
                case '3':
                    return '内外网均可访问';
                case '4':
                    return '内外网均不可访问';
                default:
                    break;
            }
        },
        //tag样式
        tagType(row) {
            switch (row.accessNetworkType) {
                case '1':
                    return 'primary';
                case '2':
                    return 'warning';
                case '3':
                    return 'success';
                case '4':
                    return 'info';
                default:
                    break;
            }
        },
    },
};
</script>

<style scoped>
.icon-list {
    height: 220px;
    overflow-y: scroll;
}
</style>
