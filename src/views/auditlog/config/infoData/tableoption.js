import validate from "@/utils/validate"

export default {
  index: true,
  indexLabel: '序号',
  selection: true,
  align: 'center',
  card: true,
  menuAlign: 'center',
  addTitle: '新增审计日志',
  editTitle: '修改',
  editBtnText: '修改',
  updateBtnText: '保存',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  searchBtn: false,
  refreshBtn: false,
  emptyBtn: false,
  searchMenuSpan: 6,
  searchMenuPosition: 'left',
  labelPosition: 'right',
  labelWidth: 150,
  tip: false,
  columnBtn: false,
  column: [
    {
      label: '系统编号',
      prop: 'systemCode',
      search: true,
      type: "select",
      dicData: [],
      rules: [{
        required: true,
        message: "请选择系统编号",
        trigger: "blur,change"
      },
      { min: 1, max: 150, message: '长度在 1 到 150 个字符', trigger: 'blur' },
      ]
    },
    {
      label: '请求地址',
      prop: 'requestURI',
      search: true,
      rules: [{
        required: true,
        message: "请输入请求地址",
        trigger: "blur,change"
      },
      { min: 1, max: 150, message: '长度在 1 到 150 个字符', trigger: 'blur' },
      ]
    },
    {
      label: '请求方式',
      prop: 'requestType',
      type: "select",
      dicData: [],
      rules: [{
        required: true,
        message: "请选择请求方式",
        trigger: "blur,change"
      }]
    },
    {
      label: '操作类型',
      prop: 'action',
      type: "select",
      dicData: [],
      rules: [{
        required: true,
        message: "请选择操作类型",
        trigger: "blur,change"
      }]
    },
    {
      label: '交易名称',
      prop: 'message',
      search: true,
      rules: [{
        required: true,
        message: "请输入交易名称",
        trigger: "blur,change"
      },
      { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' },
      ]
    },
    {
      label: '状态',
      prop: 'status',
      search: true,
      slot: true,
      type: "select",
      dicData: [],
      rules: [{
        required: true,
        message: "请选择状态",
        trigger: "blur,change"
      }]
    },
    {
      label: '是否记录请求数据',
      prop: 'recordArgsData',
      hide: true,
      showColumn: false,
      type: "select",
      dicData: [{
        label: '是',
        value: true
      },
      {
        label: '否',
        value: false
      },
      ],
      rules: [{
        required: true,
        message: "请选择是否记录请求数据",
        trigger: "blur,change"
      }]
    },
    {
      label: '是否记录返回数据',
      prop: 'recordReturnData',
      hide: true,
      showColumn: false,
      type: "select",
      dicData: [{
        label: '是',
        value: true
      },
      {
        label: '否',
        value: false
      },
      ],
      rules: [{
        required: true,
        message: "请选择是否记录返回数据",
        trigger: "blur,change"
      }]
    },
    {
      label: '是否记录异常数据',
      prop: 'recordThrowingData',
      hide: true,
      showColumn: false,
      type: "select",
      dicData: [{
        label: '是',
        value: true
      },
      {
        label: '否',
        value: false
      },
      ],
      rules: [{
        required: true,
        message: "请选择是否记录异常数据",
        trigger: "blur,change"
      }]
    },
    {
      label: '创建用户',
      prop: 'createUser',
      addDisplay: false,
      editDisplay: false,
    },
    {
      label: '创建时间',
      prop: 'createTime',
      addDisplay: false,
      editDisplay: false,
    },
    {
      label: '更新用户',
      prop: 'updateUser',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
    },
    {
      label: '更新时间',
      prop: 'updateTime',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
    },
  ]
}