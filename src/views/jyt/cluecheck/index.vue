<template>
    <div class="app-container">
        <yo-table
            v-loading="loading"
            :data="data"
            :option="option"
            ref="crud"
            :page.sync="page"
            :search.sync="search"
            :before-open="beforeOpen"
            @on-load="onLoad"
            @search-change="searchChange"
            @row-update="rowUpdate"
            @row-save="rowSave"
            @refresh-change="refresh"
            v-model="formParent"
            @selection-change="handleSelectionChange"
        >
            <template slot-scope="{ scope }" slot="searchMenu">
                <el-button
                    size="mini"
                    type="primary"
                    icon="el-icon-search"
                    @click.stop="handleQuery"
                    v-hasPermi="['admin:user:page']"
                >
                    查询
                </el-button>
                <el-button
                    size="mini"
                    icon="el-icon-refresh"
                    @click.stop="resetQuery()"
                >
                    重置
                </el-button>
            </template>
            <template slot-scope="scope" slot="deptIdsForm">
                <el-input
                    :disabled="disabled"
                    v-model="deptName"
                    placeholder="点击按钮选择部门"
                    readonly
                >
                    <el-button
                        v-if="!disabled"
                        slot="append"
                        icon="el-icon-plus"
                        @click="treeShow('dept')"
                    ></el-button>
                </el-input>
            </template>
            <template slot-scope="scope" slot="instIdForm">
                <el-input
                    :disabled="disabled"
                    v-model="instName"
                    placeholder="点击按钮选择机构"
                    readonly
                >
                    <el-button
                        v-if="!disabled"
                        slot="append"
                        icon="el-icon-plus"
                        @click="treeShow('inst')"
                    ></el-button>
                </el-input>
            </template>
            <template slot-scope="scope" slot="userStatForm">
                <el-select
                    :disabled="disabled"
                    v-model="formParent.userStat"
                    placeholder="请选择 用户状态"
                    clearable
                >
                    <el-option
                        v-for="dict in dict.type['yoaf_user_state']"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                        :disabled="dict.value == '04'"
                    />
                </el-select>
            </template>
            <template slot-scope="scope" slot="empStatusForm">
                <el-select
                    :disabled="disabled"
                    v-model="formParent.empStatus"
                    placeholder="请选择 人员状态"
                    clearable
                >
                    <el-option
                        v-for="dict in dict.type['yoaf_emp_status']"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                </el-select>
            </template>

            <!-- 自定义列 -->
            <template slot="idCard" slot-scope="scope">
                <div>
                    <span>{{ scope.row.idCard }}</span>
                    <el-button
                        v-if="scope.row.idCard"
                        type="text"
                        icon="el-icon-view"
                        v-hasPermi="['admin:user:sensitive:get']"
                        @click="viewUserInfo(scope.row, 'idCard')"
                    ></el-button>
                </div>
            </template>
            <template slot="email" slot-scope="scope">
                <div>
                    <span>{{ scope.row.email }}</span>
                    <el-button
                        v-if="scope.row.email"
                        type="text"
                        icon="el-icon-view"
                        v-hasPermi="['admin:user:sensitive:get']"
                        @click="viewUserInfo(scope.row, 'email')"
                    ></el-button>
                </div>
            </template>
            <template slot="phoneNumber" slot-scope="scope">
                <div>
                    <span>{{ scope.row.phoneNumber }}</span>
                    <el-button
                        v-if="scope.row.phoneNumber"
                        type="text"
                        icon="el-icon-view"
                        v-hasPermi="['admin:user:sensitive:get']"
                        @click="viewUserInfo(scope.row, 'phoneNumber')"
                    ></el-button>
                </div>
            </template>
            <template slot="gender" slot-scope="scope">
                <el-tag
                    :type="scope.row.gender == '1' ? '' : 'danger'"
                    size="mini"
                >
                    {{ scope.row.gender == '1' ? '男' : '女' }}
                </el-tag>
            </template>
            <template slot="userStat" slot-scope="scope">
                <el-tag size="mini" effect="plain" :type="tagType(scope.row)">
                    {{ userStat(scope.row) }}
                </el-tag>
            </template>
            <template slot="empStatus" slot-scope="scope">
                <el-tag size="mini" effect="plain" :type="tagType2(scope.row)">
                    {{ empStatus(scope.row) }}
                </el-tag>
            </template>
            <!-- 自定义左侧操作栏 -->

            <template slot-scope="{ size }" slot="menuLeft">
                <el-button
                    type="primary"
                    icon="el-icon-document-checked"
                    size="mini"
                    @click.stop="handleAdd"
                >
                    新增
                </el-button>
            </template>

            <template slot-scope="{ row, size, type, index }" slot="menu">
                <el-button
                    :size="size"
                    :type="type"
                    @click="handleCommand('handleCheckInfo', row)"
                    icon="el-icon-info"
                >
                    详情
                </el-button>

                <el-button
                    v-if="row.getType == '1'"
                    :size="size"
                    :type="type"
                    @click="viewResult(row)"
                    icon="el-icon-document-checked"
                >
                    查看结果
                </el-button>
                <el-button
                    v-else
                    :size="size"
                    :type="type"
                    @click="getresult(row)"
                    icon="el-icon-paperclip"
                >
                    结果获取
                </el-button>
            </template>
        </yo-table>
        <resultDetail ref="resultDetail"></resultDetail>
        <el-dialog :title="title" :visible.sync="dialogVisible" width="30%">
            <el-tree
                v-if="dialogVisible && dialogType == 'dept'"
                class="icon-list"
                ref="tree"
                node-key="id"
                show-checkbox
                :check-strictly="true"
                :props="treeprops"
                :load="treeloadNode"
                lazy
            ></el-tree>
            <el-tree
                v-if="dialogVisible && dialogType == 'inst'"
                class="icon-list"
                ref="tree"
                node-key="id"
                show-checkbox
                :check-strictly="true"
                :props="treeprops"
                :load="treeloadNode"
                @check-change="checkChange"
                lazy
            ></el-tree>
            <span slot="footer" class="dialog-footer">
                <el-button
                    type="primary"
                    @click="dialogQueren(dialogType)"
                    size="mini"
                >
                    确 定
                </el-button>
                <el-button @click="dialogVisible = false" size="mini">
                    取 消
                </el-button>
            </span>
        </el-dialog>
        <!-- <user-register ref="register" @ok="refreshList" /> -->
    </div>
</template>
<script>
import {
    listUser,
    addUser,
    delUser,
    delUsers,
    updateUser,
    resetUserPwd,
    listRole,
    exportUsers,
    importUsers,
    downloadExample,
    usersSensitive,
} from '@/api/system/user';
import { listData, pushData, getResult, viewResult } from '@/api/jyt/clueCheck';
import userOption from './infoData/option.js';
import { mapActions } from 'vuex';
import Cookies from 'js-cookie';
import resultDetail from './component/resultDetail.vue';

var identity = '';
var telNumber = '';
var emailid = '';
export default {
    name: 'User',
    dicts: ['jyt_clue_type', 'jyt_is_proxy'],
    components: { resultDetail },
    data() {
        return {
            title: '',
            dialogType: '',
            loading: true,
            formParent: {},
            search: {},
            exportSearch: {},
            page: {
                pageSize: 10,
                pageNum: 1,
            },
            treeprops: {
                label: 'name',
                children: 'children',
            },
            deptName: '',
            instName: '',
            data: [],
            option: userOption,
            dialogVisible: false,
            // 非多个禁用
            multiple: true,
        };
    },
    created() {
        /** *添加字典项数据*/
        this.updateDictData(
            this.option.column,
            'clueType',
            this.dict.type['jyt_clue_type']
        );
        this.updateDictData(
            this.option.column,
            'drawerProxyWithdrawal',
            this.dict.type['jyt_is_proxy']
        );
    },
    methods: {
        ...mapActions([
            'getUser',
            'instDeptTree',
            'instTree',
            'instDeptTreeChild',
        ]),
        // 首次加载调用此方法
        onLoad(page) {
            this.getList(this.search);
            // this.$refs['crud'].toggleSelection()
        },
        // 弹窗打开
        beforeOpen(done, type) {
            if (type == 'view') this.disabled = true;
            else this.disabled = false;
            //  角色列表
            listRole().then(res => {
                this.updateDictData(this.option.column, 'roleIds', res.data);
            });
            done();
        },
        refreshList() {
            this.handleQuery();
        },
        // 更新字典数据
        updateDictData(option, key, data) {
            let column = this.findObject(option, key);
            column.dicData = data;
        },
        // 搜索按钮
        searchChange(params, done) {
            this.handleQuery();
            setTimeout(() => {
                done();
            }, 1000);
        },
        // 新增表单保存
        async rowSave(form, done, loading) {
            console.log(form);
            if (form.clueType == '01') {
                console.log(form.clueType);
                if (
                    form.reservationWithdraweeTime == null ||
                    form.reservationWithdraweeTime == '' ||
                    form.reservationWithdraweeMoney == null ||
                    form.reservationWithdraweeMoney == ''
                ) {
                    loading();
                    this.$message.error(
                        '线索类型为大额预约取现线索时，预约取现时间和预约取现金额必填！'
                    );
                    return false;
                }
            }
            if (form.clueType == '02') {
                console.log(form.clueType);
                if (
                    form.purchaserTime == null ||
                    form.purchaserTime == '' ||
                    form.purchaserNumber == null ||
                    form.purchaserNumber == '' ||
                    form.purchaserMoney == null ||
                    form.purchaserMoney == ''
                ) {
                    loading();
                    this.$message.error(
                        '线索类型为购买银行黄金线索时，购买时间、购买数量、购买金额必填！'
                    );
                    return false;
                }
            }
            let response = await pushData(form);
            if (response.code == 0) {
                this.$message.success(response.message);
            }
            this.handleQuery();
            done();
        },
        // 刷新按钮
        refresh() {
            this.handleQuery();
        },
        /** 查询用户列表 */
        async getList(query) {
            const params = { ...query, ...this.page };
            this.loading = true;
            let response = await listData(params);
            if (response.code == 0) {
                this.data = response.data.list;
                this.page.total = response.data.total;
                this.loading = false;
            }
        },
        /****注册按钮 */
        handleRegister() {
            this.$refs.register.show();
        },
        // 用户状态修改
        handleStatusChange(row) {
            let text = row.status === '0' ? '启用' : '停用';
            this.$modal
                .confirm('确认要"' + text + '""' + row.userName + '"用户吗？')
                .then(function () {
                    return changeUserStatus(row.userId, row.status);
                })
                .then(() => {
                    this.$modal.msgSuccess(text + '成功');
                })
                .catch(function () {
                    row.status = row.status === '0' ? '1' : '0';
                });
        },

        /** 搜索按钮操作 */
        handleQuery() {
            this.page.currentPage = 1;
            this.getList(this.search);
            this.exportSearch = this.search;
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.$refs.crud.searchReset();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.userName = selection.map(item => item.userName);
            this.ids = selection.map(item => item.userId);
            this.multiple = !selection.length;
        },
        // 更多操作触发
        handleCommand(command, row) {
            this.$refs.crud.rowView(row);
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.deptName = '';
            this.instName = '';
            this.$refs.crud.rowAdd();
        },
        // 修改
        async handleEdit(row, index) {
            identity = row.idCard;
            telNumber = row.phoneNumber;
            emailid = row.email;
            Cookies.set('IdCard', row.idCard);
            Cookies.set('PhoneNumber', row.phoneNumber);
            Cookies.set('email', row.email);

            const userName = row.userName;
            await this.getUser(userName).then(res => {
                res.idCard = identity;
                res.phoneNumber = telNumber;
                res.email = emailid;
                this.formParent = res;
                let deptName = res.depts.map(i => {
                    return i.deptName;
                });
                this.instName = res.inst && res.inst.instNameAbbr;
                this.deptName = deptName.join(',');
                this.$refs.crud.rowEdit(res, index);
            });
        },
        // 修改表单保存
        rowUpdate(form, index, done, loading) {
            if (form.idCard == identity) {
                delete form.idCard;
            }
            if (form.phoneNumber == telNumber) {
                delete form.phoneNumber;
            }
            if (form.email == emailid) {
                delete form.email;
            }
            updateUser(form).then(response => {
                done();
                if (response.code == 0) {
                    this.$message.success(response.message);
                    this.handleQuery();
                }
            });
        },
        /** 重置密码 */
        async handlepwd(query, delFn) {
            let res = await delFn(query);
            if (res.code == 0) {
                this.$message.success(res.message);
            }
        },
        /** 重置密码按钮操作 */
        handleResetPwd(row) {
            const query = [{ userId: row.userId, userName: row.userName }];
            this.$confirm(
                `是否将 ${row.userName} 的密码重置为默认密码?`,
                '提示',
                {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }
            )
                .then(() => {
                    this.handlepwd(query, resetUserPwd);
                })
                .catch(() => {});
        },
        async handleDelUser(userName, delFn) {
            let res = await delFn(userName);
            if (res.code == 0) {
                this.$message.success(res.message);
                this.$refs.crud.toggleSelection();
                this.getList();
            }
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const userName = row.userName || this.userName;
            const h = this.$createElement;
            this.$confirm(
                `是否永久删除用户名为" ${userName} "的数据项？`,
                '提示',
                {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                    showCancelButton: true,
                }
            )
                // this.$msgbox({
                //   title: "提示",
                //   message: h("p", null, [
                //     h(
                //       "p",
                //       { style: "word-break: break-all" },
                //       '是否永久删除用户名为"' + userName + '"的数据项？'
                //     )
                //   ]),
                //   showCancelButton: true,
                //   confirmButtonText: "确定",
                //   cancelButtonText: "取消"
                // })
                .then(async () => {
                    // 删除单个用户
                    if (row.userName) {
                        this.handleDelUser(userName, delUser);
                    } else {
                        this.handleDelUser(userName, delUsers);
                    }
                })
                .catch(() => {});
        },

        /** 处理返回的流文件 */
        handleExportData(res) {
            if (!res) return;
            let data = res.data;
            let filename = res.headers['content-disposition'].split('=')[1];
            let _filename = decodeURI(filename);
            const link = document.createElement('a');
            //创建 Blob对象 可以存储二进制文件
            let blob = new Blob([data], { type: 'application/x-excel' });
            link.style.display = 'none';
            link.href = URL.createObjectURL(blob);
            link.download = _filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        },
        /** 下载模板 */
        exportExample() {
            downloadExample().then(res => {
                this.handleExportData(res);
            });
        },
        /** 导出按钮操作 */
        handleExport() {
            const data = { userIds: this.ids, ...this.exportSearch };
            exportUsers(data).then(res => {
                this.handleExportData(res);
            });
        },
        fileChange(event) {
            let file = event.target.files[0];
            let formData = new FormData();
            formData.append('file', file);
            importUsers(formData).then(res => {
                if (res.code == 0) {
                    this.$message.success(res.message);
                    this.handleQuery();
                }
            });
            // 清除文件，防止下次上传相同文件无反应
            event.target.value = null;
        },
        /** 导入按钮操作 */
        handleImport() {
            this.$refs.fileRef.dispatchEvent(new MouseEvent('click'));
            // this.$refs.fileRef.click();
        },
        //过滤列表数据
        filterData(data) {
            var _filterData = ele => {
                if (ele && ele.length) {
                    ele.forEach(item => {
                        if (item.type == 'inst') {
                            item.disabled = true;
                        }
                        if (item.children) {
                            _filterData(item.children);
                        }
                    });
                }
            };
            if (this.dialogType == 'dept') _filterData(data);
            return data;
        },
        /**敏感信息显隐**/
        viewUserInfo(data, type) {
            let sensitiveStatus = data[type].includes('**') ? '1' : '2';
            usersSensitive(data.userName, sensitiveStatus).then(response => {
                const res = response.data;
                this.data.forEach(item => {
                    if (item.userName == data.userName) {
                        item[type] = res[type];
                    }
                });
            });
        },
        checkChange(data, checked, node) {
            if (checked) {
                this.$refs.tree.setCheckedNodes([data]);
            }
        },
        treeShow(val) {
            this.title = val == 'dept' ? '部门列表' : '机构列表';
            this.dialogType = val;
            this.dialogVisible = true;
        },
        dialogQueren(type) {
            if (type == 'dept') {
                let arr = this.$refs.tree.getCheckedNodes();
                let deptName = arr.map(i => {
                    return i.name;
                });
                this.deptName = deptName.join(',');
                let deptIds = arr.map(i => {
                    return i.id;
                });
                this.formParent.deptIds = deptIds;
            } else {
                let arr = this.$refs.tree.getCheckedNodes();
                if (!arr.length) {
                    this.instName = '';
                    this.formParent.instId = '';
                } else {
                    this.instName = arr[0].name;
                    this.formParent.instId = arr[0].id;
                }
            }
            this.dialogVisible = false;
        },
        treeloadNode(node, resolve) {
            let isDept = this.dialogType == 'dept';
            if (node.level === 0) {
                this.instTree(this.dialogType).then(res => {
                    let nodes = this.filterData(res);
                    return resolve(nodes);
                });
            }
            if (node.level == 1) {
                if (node.data.children) {
                    let nodes = this.filterData(node.data.children);
                    return resolve(nodes);
                } else {
                    return resolve([]);
                }
            }
            if (node.level > 1) {
                const query = {
                    id: node.data.id,
                    queryType: node.data.queryType,
                };
                this.instDeptTreeChild(query).then(res => {
                    let nodes = this.filterData(res);
                    return resolve(nodes);
                });
            }
        },

        userStat(row) {
            switch (row.userStat) {
                case '01':
                    return '启用';
                case '02':
                    return '停用';
                case '03':
                    return '注销';
                case '04':
                    return '锁定';
                default:
                    break;
            }
        },
        //tag样式
        tagType(row) {
            switch (row.userStat) {
                case '01':
                    return 'success';
                case '02':
                    return 'danger';
                case '03':
                    return 'info';
                case '04':
                    return 'warning';
                default:
                    break;
            }
        },
        //20240508改造新增字段：人员状态
        empStatus(row) {
            switch (row.empStatus) {
                case 'on':
                    return '在岗';
                case 'wait':
                    return '待岗';
                case 'off':
                    return '退休';
                case 'leave':
                    return '离职';
                default:
                    break;
            }
        },
        //tag样式
        tagType2(row) {
            switch (row.empStatus) {
                case 'on':
                    return 'success';
                case 'wait':
                    return 'warning';
                case 'off':
                    return 'info';
                case 'leave':
                    return 'danger';
                default:
                    break;
            }
        },
        async getresult(row) {
            const response = await getResult(row);
            if (response.code == 0) {
                this.$message.success(response.message);
                this.handleQuery();
            }
        },
        async viewResult(row) {
            const response = await viewResult(row);
        
            if (response.code == 0) {
                this.$refs.resultDetail.openModal(response.data);
            }
        },
    },
};
</script>

<style scoped>
.icon-list {
    height: 220px;
    overflow-y: scroll;
}
</style>
