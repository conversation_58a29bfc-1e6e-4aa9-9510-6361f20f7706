<template>
    <div>
        <!-- <el-button type="text" @click="dialogVisible = true">
            点击打开 Dialog
        </el-button> -->

        <el-dialog
            title="查看结果"
            :visible.sync="dialogVisible"
            width="60%"
            :before-close="handleClose"
        >
            <el-descriptions
                v-for="(item, index) in dataList"
                :key="index"
                :title="'结果' + (index + 1)"
                :border="true"
                labelStyle="{font-weight: bold}"
            >
                <el-descriptions-item label="核查结果">
                    {{ item.verificationResults }}
                </el-descriptions-item>
                <el-descriptions-item label="是否核查涉案">
                    <el-tag
                        effect="dark"
                        v-if="item.checkCase == '01'"
                        type="danger"
                    >
                        是
                    </el-tag>
                    <el-tag v-else>否</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="反馈状态">
                    {{ item.feedbackStatus == '01' ? '已反馈' : '未反馈' }}
                </el-descriptions-item>
                <el-descriptions-item label="是否需派警">
                    {{ item.dispatchPolice == '01' ? '需派警' : '无需派警' }}
                </el-descriptions-item>
                <el-descriptions-item label="核查单位">
                    {{ item.verificationUnit }}
                </el-descriptions-item>
                <el-descriptions-item label="核查时间">
                    {{ item.verificationTime }}
                </el-descriptions-item>
                <el-descriptions-item label="线索质量">
                    {{
                        item.qualityOfClues == '01'
                            ? '高'
                            : item.qualityOfClues == '02'
                            ? '中'
                            : '低'
                    }}
                </el-descriptions-item>
                <el-descriptions-item label="备注">
                    {{ item.remarks }}
                </el-descriptions-item>
            </el-descriptions>
            <span slot="footer" class="dialog-footer">
                <!-- <el-button @click="dialogVisible = false">取 消</el-button> -->
                <el-button type="primary" @click="dialogVisible = false">
                    确 定
                </el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
export default {
    data() {
        return {
            dialogVisible: false,
            formInline: {},
            dataList: [],
        };
    },
    methods: {
        handleClose(done) {
            done();
        },
        openModal(datas) {
            this.dialogVisible = true;
            console.log(datas);
            this.dataList = datas;
        },
    },
};
</script>
