import validate from '@/utils/validate';
import { Footer } from 'element-ui';
export default {
    index: true,
    indexLabel: '序号',
    rowKey: 'userId',
    reserveSelection: true,
    selection: true,
    align: 'center',
    card: true,
    menuAlign: 'center',
    emptyBtnIcon: 'el-icon-refresh',
    searchMenuSpan: 6,
    searchMenuPosition: 'left',
    addTitle: '新增线索核查',
    viewTitle: '查看线索详情',
    editTitle: '修改用户',
    editBtnText: '修改',
    updateBtnText: '保存',
    addBtn: false,
    editBtn: false,
    delBtn: false,
    searchBtn: false,
    refreshBtn: false,
    emptyBtn: false,
    labelPosition: 'right',
    labelWidth: 170,
    tip: false,
    columnBtn: false,
    saveBtnText: '确定',
    // excelBtn: true,
    column: [
        {
            label: '线索名称',
            prop: 'clueName',
            search: true,
            editDisabled: true,
            rules: [
                {
                    required: true,
                    message: '请输入线索名称',
                    trigger: 'blur',
                },
            ],
        },
        {
            label: '线索类型',
            prop: 'clueType',
            search: true,
            type: 'select',
            dicData: [],
            solt: true,
            rules: [
                {
                    required: true,
                    message: '请选择线索类型',
                    trigger: 'blur',
                },
            ],
        },
        {
            label: '所属银行',
            prop: 'bankName',
            rules: [
                {
                    required: true,
                    message: '请输入所属银行',
                    trigger: 'blur',
                },
            ],
        },
        {
            label: '银行网点',
            prop: 'depositBankBranch',
            rules: [
                {
                    required: true,
                    message: '请输入银行网点',
                    trigger: 'blur',
                },
            ],
        },
        {
            label: '线索上报日期',
            prop: 'clueTimer',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            rules: [
                {
                    required: true,
                    message: '请选择线索上报日期',
                    trigger: 'blur',
                },
            ],
        },
        {
            label: '建议反馈日期',
            prop: 'feedbackTime',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
        },
        {
            label: '线索详情',
            prop: 'clueDetails',
            type: 'textarea',
            span: 24, //一行
            rules: [
                {
                    required: true,
                    message: '请输入线索详情',
                    trigger: 'blur',
                },
            ],
        },
        {
            label: '被核查人姓名',
            prop: 'verifiedName',
            rules: [
                {
                    required: true,
                    message: '请输入被核查人姓名',
                    trigger: 'blur',
                },
            ],
        },
        {
            label: '被核查人身份证号',
            prop: 'verifiedId',
            rules: [
                {
                    required: true,
                    message: '请输入被核查人身份证号',
                    trigger: 'blur',
                },
                {
                    validator: validate.checkIdentity,
                    trigger: 'blur',
                },
            ],
        },
        {
            label: '被核查人手机号码',
            prop: 'verifiedPhone',
            rules: [
                {
                    required: true,
                    message: '请输入被核查人手机号码',
                    trigger: 'blur',
                },
                {
                    validator: validate.telNumber,
                    trigger: 'blur',
                },
            ],
        },
        {
            label: '被核查人卡号',
            prop: 'verifiedCard',
            rules: [
                {
                    required: true,
                    message: '请输入被核查人卡号',
                    trigger: 'blur',
                },
            ],
        },
        {
            label: '预约取现时间',
            prop: 'reservationWithdraweeTime',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
        },
        {
            label: '预约取现金额',
            prop: 'reservationWithdraweeMoney',
            
        },
        {
            label: '购买时间',
            prop: 'purchaserTime',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
             
        },
        {
            label: '购买数量（g）',
            prop: 'purchaserNumber',
            
        },
        {
            label: '购买金额',
            prop: 'purchaserMoney',
             
        },
        {
            label: '是否代理取款',
            prop: 'drawerProxyWithdrawal',
            type: 'select',
        },
        {
            label: '取款时间',
            prop: 'drawerTime',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
        },
        {
            label: '取款金额',
            prop: 'drawerMoney',
        },
        {
            label: '备注',
            prop: 'remarks',
            type: 'textarea',
            span: 24, //一行
        },
    ],
};
