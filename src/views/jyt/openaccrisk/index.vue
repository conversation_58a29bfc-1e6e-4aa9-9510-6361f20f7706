<!--开户风险查询列表-->
<template>
  <div class="app-container">
    <yo-table @keyup.enter.native="handleQuery" v-loading="loading" :option="option" :data="data" ref="crud"
      :page.sync="page" :search.sync="search" :before-open="beforeOpen" @on-load="onLoad" @search-change="searchChange"
      @row-update="rowUpdate" @row-save="rowSave" @refresh-change="refresh" v-model="formParent"
      @selection-change="handleSelectionChange">
      <template slot="searchMenu">
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <el-button size="mini" type="primary" icon="el-icon-search" @click.stop="handleQuery">
          查询
        </el-button>
        <el-button size="mini" icon="el-icon-refresh" @click.stop="resetQuery()">
          重置
        </el-button>
      </template>
      <!-- 自定义列 -->
      <!-- <template slot="updateTimeSearch">
        <el-date-picker v-model="search.updateTimeBox" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="截止日期" value-format="yyyyMMddHHmmss"
          :default-time="['00:00:00', '23:59:59']"></el-date-picker>
      </template> -->

      <!-- 自定义左侧操作栏 -->
      <template slot="menuLeft">
        <el-button type="primary" icon="el-icon-search" size="mini" @click.stop="handleAdd">开户风险查询</el-button>
        <input id="fileslist" v-show="false" type="file" accept=".xls" ref="fileRef" @change="fileChange" />
        <el-button type="primary" icon="el-icon-upload2" size="mini"
          @click="handleImport('batchQuery')">批量查询</el-button>
        <el-button type="warning" icon="el-icon-upload2" size="mini" @click="handleImport('batchFbk')">批量反馈</el-button>
        <el-button type="success" icon="el-icon-printer" size="mini" :disabled="!selectedRows.length"
          @click="batchPrint">
          批量打印 ({{ selectedRows.length }})
        </el-button>
      </template>

      <!-- 自定义搜索 -->
      <template slot-scope="{ disabled, size }" slot="createTimeSearch">
        <el-date-picker clearable v-model="search.createTimeBox" type="datetimerange" range-separator="至"
          start-placeholder="开始时间" end-placeholder="结束时间" value-format="yyyy-MM-dd HH:mm:ss"
          @change="changeFn()"></el-date-picker>
      </template>

      <!-- 自定义右侧操作栏 -->
      <template slot-scope="{ size }" slot="menuRight">
        <el-button type="primary" icon="el-icon-download" size="mini" @click="exportQueryExample">批量查询模板</el-button>
        <el-button type="warning" icon="el-icon-download" size="mini" @click="exportFbkExample">批量反馈模板</el-button>
        <el-button type="success" icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
      </template>


      <!-- 自定义显示隐藏列 -->
      <!--客户名称  -->
      <template slot="accountSubjectName" slot-scope="scope">
        <div>
          <span>{{scope.row.accountSubjectName}}</span>
          <el-button v-if="scope.row.accountSubjectName" type="text" icon="el-icon-view"
            @click="viewSensitive(scope.row,'accountSubjectName')"></el-button>
        </div>
      </template>
      <!--身份证号码  -->
      <template slot="accountCredentialNumber" slot-scope="scope">
        <div>
          <span>{{scope.row.accountCredentialNumber}}</span>
          <el-button v-if="scope.row.accountCredentialNumber" type="text" icon="el-icon-view"
            @click="viewSensitive(scope.row,'accountCredentialNumber')"></el-button>
        </div>
      </template>
      <!--手机号  -->
      <template slot="delayPhoneNumber" slot-scope="scope">
        <div>
          <span>{{scope.row.delayPhoneNumber}}</span>
          <el-button v-if="scope.row.delayPhoneNumber" type="text" icon="el-icon-view"
            @click="viewSensitive(scope.row,'delayPhoneNumber')"></el-button>
        </div>
      </template>
      <!--账户号  -->
      <template slot="accountNumber" slot-scope="scope">
        <div>
          <span>{{scope.row.accountNumber}}</span>
          <el-button v-if="scope.row.accountNumber" type="text" icon="el-icon-view"
            @click="viewSensitive(scope.row,'accountNumber')"></el-button>
        </div>
      </template>


      <!-- 操作列 -->
      <template slot-scope="{ row, size, type ,index}" slot="menu">
        <el-button size="mini" :type="type" icon="el-icon-edit" @click.stop="handleEdit(row,index)">
          开户反馈
        </el-button>
        <!-- <el-button size="mini" :type="type" icon="el-icon-delete" @click="handleDelete(row)">
          删除
        </el-button> -->
        <el-button size="mini" :type="type" icon="el-icon-info" @click.stop="handleView(row)">
          查看
        </el-button>
        <el-button size="mini" :type="type" icon="el-icon-printer" @click.stop="handlePrint(row)">
          打印
        </el-button>
      </template>
    </yo-table>
  </div>
</template>
<script>
import {
  listData,
  pushQueryData,
  pushFbkData,
  viewOpenacc,
  getCurrentUserInfo,
  downloadQueryExample,
  downloadFbkExample,
  exportOpenaccRisks,
  batchQueryImport,
  batchFbkImport,
  querySensitive
} from '@/api/jyt/openaccrisk';
import openaccOption from './infoData/option.js';
import { hideSensitiveInfo, autoHideSensitive } from './infoData/sensitive';
import Cookies from "js-cookie";

export default {
  name: 'khfx',
  components: {},
  dicts: [
    "jyt_cardopen_type",
    "jyt_risk_code",
    "jyt_on_opening",
    "jyt_stock_account",
    "jyt_disposal_results"
  ],
  data () {
    return {
      selectedRows: [], // 添加这行，用于存储选中的行数据
      opUserId: '',
      opUserName: '',
      printLoading: false,
      title: '',
      dialogType: '',
      loading: true,
      statusFlag: false,
      buttonAttr: '0',
      formParent: {},
      search: {},
      exportSearch: {},
      page: {
        pageSize: 10,
        pageNum: 1,
      },
      treeprops: {
        label: 'name',
        children: 'children',
      },
      deptName: '',
      instName: '',
      data: [],
      dynamicRules: {
        accountOpenTime: [],
        cardOpeningTime: []
      },
      option: openaccOption,
      dialogVisible: false,
      // 非多个禁用
      multiple: true,
      labelPosition: 'right',
      formLabelAlign: {
        name: '',
        idCard: '',
      },
    };
  },
  watch: {
    /* 'search.updateTimeBox': {
      handler (val) {
        if (val) {
          this.search.updateTimeStart = val[0];
          this.search.updateTimeEnd = val[1];
        }
      },
      deep: true,
    }, */
  },
  created () {
    /** *添加字典项数据*/
    this.updateDictData(
      this.option.column,
      "cardOpeningType",
      this.dict.type["jyt_cardopen_type"]
    );
    this.updateDictData(
      this.option.column,
      "riskCode",
      this.dict.type["jyt_risk_code"]
    );
    this.updateDictData(
      this.option.column,
      "onOpening",
      this.dict.type["jyt_on_opening"]
    );
    this.updateDictData(
      this.option.column,
      "stockAccount",
      this.dict.type["jyt_stock_account"]
    );
    this.updateDictData(
      this.option.column,
      "disposalResults",
      this.dict.type["jyt_disposal_results"]
    );
    this.page.currentPage = 1;
    this.getList();
  },
  methods: {
    // 首次加载调用此方法
    onLoad () {
      this.getList();
      this.getCurrentUserInfo();
    },
    // 弹窗打开
    beforeOpen (done, type) {
      console.log('type:', type);
      done();
    },
    refreshList () {
      this.handleQuery();
    },
    // 搜索按钮
    searchChange (params, done) {
      this.handleQuery();
      setTimeout(() => {
        done();
      }, 1000);
    },

    // 更新字典数据
    updateDictData (option, key, data) {
      let column = this.findObject(option, key);
      column.dicData = data;
    },

    // 新增表单保存-开户风险查询
    async rowSave (form, done, loading) {
      console.log("开户风险查询参数==>" + JSON.stringify(form))
      pushQueryData(form).then(res => {
        console.log("开户风险查询返回的结果--" + JSON.stringify(res))
        if (res.code == '0') {
          this.$message.success(res.message);
          this.handleQuery();
          done();
        } else {
          this.$message.error(res.message);
          this.loading = false;
        }
      });
    },
    // 刷新按钮
    refresh () {
      this.handleQuery();
    },
    /** 查询列表 */
    async getList (query) {
      const params = { ...query, ...this.page };
      console.log('查询参数：', params);
      this.loading = true;
      const { code, data } = await listData(params);
      if (code == 0) {
        this.data = data.list;
        this.page.total = data.total;
        this.loading = false;
      }
    },

    /** 搜索按钮操作 */
    handleQuery () {
      this.page.currentPage = 1;
      this.getList(this.search);
      this.exportSearch = this.search;
      this.getCurrentUserInfo();
    },
    // /** 重置按钮操作 */
    resetQuery () {
      this.$refs.crud.searchReset();
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange (selection) {
      this.statusFlag = true;
      let row = selection; //查找到的所有进行修改
      if (row != undefined) {
        this.statusFlag = false;
      }
      this.selectedRows = selection;
      //console.log("选中的所有数据："+JSON.stringify(this.selectedRows));
      this.ids = selection.map(item => item.id).join(',');
      this.multiple = !(selection.length > 0 && !this.statusFlag);
    },

    /** 新增按钮操作 */
    handleAdd () {
      this.$refs.crud.rowAdd();
    },

    // 修改
    async handleEdit (row, index) {
      const id = row.openaccId;
      await viewOpenacc(id).then((response) => {
        console.log("查看详情返回的结果：" + response.data);
        const data = response.data;
        this.formParent = data;
        this.$refs.crud.rowEdit(data, index);
      });
    },
    // 修改表单保存
    /* rowUpdate(form, index, done, loading) {
       //判断开卡类型
       console.log("修改返回的表单信息：" + form.onOpening);
       pushFbkData(form).then((response) => {
         if (response.code == 0) {
           this.$message.success(response.message);
           this.getList();
           done();
         }
       });
     }, */
    // 开户反馈-修改
    async pushFbkData (row, index) {
      console.log("反馈信息：" + JSON.stringify(row));
      var onOpen = row.onOpening;
      if (onOpen == "01") { //1-开卡，2-不开卡
        this.$confirm("确认不开卡！", "提示", {
            confirmButtonText: "确认",
            type: "warning"
          })
          .then(() => {
            //进入编辑
            var id = row.openaccId;
            viewOpenacc(id).then(res => {
              //console.log("查询返回的结果--" + JSON.stringify(res))
              if (res.code == 0) {
                this.$refs.crud.rowEdit(res.data);
              }
            });

          })
          .catch(() => {});
      } else if (onOpen == "02") { //
        this.$confirm("确认不开卡！", "提示", {
            confirmButtonText: "确认",
            type: "warning"
          })
          .then(() => {
            //进入编辑
            var id = row.openaccId;
            viewOpenacc(id).then(res => {
              //console.log("查询返回的结果--" + JSON.stringify(res))
              if (res.code == 0) {
                this.$refs.crud.rowEdit(res.data);
              }
            });

          })
          .catch(() => {});
      } else {
        //进入编辑
        var id = row.openaccId;
        viewOpenacc(id).then(res => {
          //console.log("查询返回的结果--" + JSON.stringify(res))
          if (res.code == 0) {
            this.$refs.crud.rowEdit(res.data);
          }
        });
      }

    },
    //查看
    async handleView (row) {
      this.$refs.crud.rowView(row);
    },

    // 修改表单保存
    rowUpdate (form, index, done, loading) {
      console.log("修改表单保存，loading状态：" + loading);
      // 1. 正确调用loading函数开始加载
      if (typeof loading === 'function') {
        loading(true); // 显示加载状态
      }
      console.log("反馈是否开户：" + form.onOpening);
      if (form.onOpening === '01') {
        this.$message.warning("注意，当选择开卡类型为【开户】时，客户姓名、身份证号码、手机号码、所属银行、银行开户网点、开户类型、新开户账号、开户日期、开卡处置结果、转账额度额均为必填项！")
        if (!form.accountSubjectName) {
          this.$message.error('当选择开卡类型为【开户】时，【客户姓名】为必填项');
          return done(false); // 阻止提交
        }
        if (!form.accountCredentialNumber) {
          this.$message.error('当选择开卡类型为【开户】时，【身份证号码】为必填项');
          return done(false); // 阻止提交
        }
        if (!form.delayPhoneNumber) {
          this.$message.error('当选择开卡类型为【开户】时，【手机号码】为必填项');
          return done(false); // 阻止提交
        }
        if (!form.bankName) {
          this.$message.error('当选择开卡类型为【开户】时，【所属银行】为必填项');
          return done(false); // 阻止提交
        }
        if (!form.depositBankBranch) {
          this.$message.error('当选择开卡类型为【开户】时，【银行开户网点】为必填项');
          return done(false); // 阻止提交
        }
        if (!form.stockAccount) {
          this.$message.error('当选择开卡类型为【开户】时，【开户类型】为必填项');
          return done(false); // 阻止提交
        }
        if (!form.accountNumber) {
          this.$message.error('当选择开卡类型为【开户】时，【新开户账号】为必填项');
          return done(false); // 阻止提交
        }
        if (!form.cardOpeningTime) {
          this.$message.error('当选择开卡类型为【开户】时，【开户日期】为必填项');
          return done(false); // 阻止提交
        }
        if (!form.disposalResults) {
          this.$message.error('当选择开卡类型为【开户】时，【开卡处置结果】为必填项');
          return done(false); // 阻止提交
        }

        if (!form.transferLimitAmount) {
          this.$message.error('当选择开卡类型为【开户】时，【转账额度额】为必填项');
          return done(false); // 阻止提交
        }
      }
      pushFbkData(form).then(response => {
        done()
        if (response.code == 0) {
          this.$message.success(response.message);
          this.handleQuery();
        }
      });
    },


    //获取当前登录人信息
    async getCurrentUserInfo () {
      const { data } = await getCurrentUserInfo();
      console.log("查询当前登录人信息：" + JSON.stringify(data));
      this.opUserId = data.userName; //工号
      this.opUserName = data.userNickName; //姓名

    },

    //选择是否开卡后触发变化
    handleOpeningChange (value) {
      // 当选择"是"时设置必填规则
      if (value === '1' || value === '是') { // 根据实际值调整
        this.dynamicRules.accountOpenTime = [{
          required: true,
          message: '请选择开户日期',
          trigger: 'blur'
        }]
        this.dynamicRules.cardOpeningTime = [{
          required: true,
          message: '请选择开卡时间',
          trigger: 'blur'
        }]
      } else {
        this.dynamicRules.accountOpenTime = []
        this.dynamicRules.cardOpeningTime = []
      }

      // 触发表单验证更新
      this.$nextTick(() => {
        if (this.$refs.crud && this.$refs.crud.$refs.form) {
          this.$refs.crud.$refs.form.validateField(['accountOpenTime', 'cardOpeningTime'])
        }
      })
    },
    // end
    //批量导入、导出-------------------------------------
    /** 导入按钮操作 */
    handleImport (event) {
      console.log("批量导入事件：" + event);
      this.buttonAttr = event;
      if (this.buttonAttr == 'batchQuery') { //批量查询
        this.$confirm("此操作将向公安发起批量查询，需要上传excel数据文件，确认是否继续操作？", "提示", {
            confirmButtonText: "是",
            cancelButtonText: "否",
            type: "warning"
          })
          .then(() => {
            this.$refs.fileRef.dispatchEvent(new MouseEvent('click'));
          })
          .catch(() => {});
      } else if (this.buttonAttr == 'batchFbk') { //批量反馈
        this.$confirm("此操作将向公安发起批量反馈开卡信息，需要上传excel数据文件，确认是否继续操作？", "提示", {
            confirmButtonText: "是",
            cancelButtonText: "否",
            type: "warning"
          })
          .then(() => {
            this.$refs.fileRef.dispatchEvent(new MouseEvent('click'));
          })
          .catch(() => {});

      } else {
        console.log("异常情况，未获取到按钮事件类型:" + this.buttonAttr);
      }

    },

    /** 下载模板-查询 */
    exportQueryExample () {
      downloadQueryExample().then((res) => {
        this.handleExportData(res);
      });
    },
    /** 下载模板-反馈 */
    exportFbkExample () {
      downloadFbkExample().then((res) => {
        this.handleExportData(res);
      });
    },
    //导出数据
    handleExport () {
      console.log("开始导出风险查询数据......");
      this.$confirm("此操作将根据查询条件导出相应的数据，确认是否继续操作？", "提示", {
          confirmButtonText: "是",
          cancelButtonText: "否",
          type: "warning"
        })
        .then(() => {
          exportOpenaccRisks(this.search).then(res => {
            this.handleExportData(res);
          });
        })
        .catch(() => {});

    },
    /** 处理返回的流文件 -通用方法 */
    handleExportData (res) {
      console.log('res:', res);
      if (!res) return;
      let data = res.data;
      let filename = res.headers["content-disposition"].split("=")[1];
      let _filename = decodeURI(filename);
      const link = document.createElement("a");
      //创建 Blob对象 可以存储二进制文件
      let blob = new Blob([data], { type: "application/octet-stream" });
      link.style.display = "none";
      link.href = URL.createObjectURL(blob);
      link.download = _filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },

    fileChange (event) {
      console.log("导入文件变化filechange--" + JSON.stringify(event));
      this.loading = true;
      let file = event.target.files[0];
      let formData = new FormData();
      formData.append('file', file);
      console.log("按钮属性---" + this.buttonAttr);
      if (this.buttonAttr == 'batchQuery') {
        console.log("批量查询---->");
        batchQueryImport(formData).then(res => {
          this.loading = false;
          if (res.code == 0) {
            console.log("导入成功！" + res.message);
            this.$message.success(res.message);
            this.handleQuery();
          } else {
            this.$message.error("导入异常，" + res.message + "，请等待一会儿后刷新页面，如果还无导入数据请联系管理员！");
            this.handleQuery();
          }
        });
      } else if (this.buttonAttr == 'batchFbk') {
        console.log("批量反馈---->");
        batchFbkImport(formData).then(res => {
          this.loading = false;
          if (res.code == 0) {
            console.log("导入成功！" + res.message);
            this.$message.success(res.message);
            this.handleQuery();
          } else {
            this.$message.error("导入异常，" + res.message + "，请等待一会儿后刷新页面，如果还无导入数据请联系管理员！");
            this.handleQuery();
          }
        });
      } else {
        console.log("未知按钮属性，无导入操作");
      }
      // 清除文件，防止下次上传相同文件无反应
      event.target.value = null;
    },
    //批量导入、导出结束------------------------------------------------------------

    /**敏感信息显隐**/
    viewSensitive (data, type) {
      let sensitiveStatus = data[type].includes("*") ? "1" : "2";
      querySensitive(data.openaccId, sensitiveStatus).then(response => {
        const res = response.data;
        console.log("敏感信息响应结果：" + JSON.stringify(res));
        this.data.forEach(item => {
          if (item.openaccId == data.openaccId) {
            item[type] = res[type];
          }
        });
      });
    },


    ///////////////////////////////////////
    async handlePrint (row) {
      this.printLoading = true;
      try {
        // 1. 获取详情数据（带数组处理，出现数据错误的情况会有多条记录出现）
        const { code, data, message } = await viewOpenacc(row.openaccId).catch(error => {
          console.error('API请求失败:', error);
          throw new Error('API请求详情数据接口失败');
        });
        if (code !== '0') throw new Error(message || '获取详情失败');

        // 2. 定义需要转换的字段映射
        const dictFieldMap = {
          cardOpeningType: 'jyt_cardopen_type',
          riskCode: 'jyt_risk_code',
          onOpening: 'jyt_on_opening',
          stockAccount: 'jyt_stock_account',
          disposalResults: 'jyt_disposal_results'
        };

        // 3. 执行字典转换
        const translatedData = this.translateDictValues(data, dictFieldMap);

        // 2. 直接生成打印内容
        const printHtml = this.generatePrintHtml(translatedData);

        // 3. 执行打印
        const printWindow = window.open('', '_blank');
        if (!printWindow) {
          throw new Error('请允许弹出窗口以进行打印');
        }

        printWindow.document.write(printHtml);
        printWindow.document.close();

        // 等待内容加载
        printWindow.onload = () => {
          setTimeout(() => {
            printWindow.print();
            setTimeout(() => printWindow.close(), 500);
          }, 300);
        };

      } catch (error) {
        console.error('打印失败:', error);
        this.$message.error(`打印失败: ${error.message}`);
      } finally {
        this.printLoading = false;
      }
    },

    // 生成打印HTML内容
    generatePrintHtml (data) {

      // 生成表单行
      const generateFormRow = (label, value) => {
        if (value === undefined || value === null){
          console.log(`过滤掉空值字段: ${label}`);
          return '';
        }

        return `
          <div class="form-row">
            <div class="form-label">${label}：</div>
            <div class="form-value">${value}</div>
          </div>
        `;
      };

      return `
        <!DOCTYPE html>
        <html>
          <head>
            <title>开户风险查询信息</title>
            <meta charset="utf-8">
            <style>
              body {
                font-family: "Microsoft YaHei";
                padding: 15px;
                color: #333;
              }
              .print-header {
                text-align: center;
                margin-bottom: 20px;
                padding-bottom: 10px;
                border-bottom: 1px solid #eee;
              }
              .form-row {
                margin-bottom: 12px;
                display: flex;
                line-height: 1.5;
              }
              .form-label {
                width: 150px;
                text-align: right;
                padding-right: 10px;
                font-weight: bold;
              }
              .form-value {
                flex: 1;
                word-break: break-all;
              }
              @page {
                size: A4;
                margin: 10mm;
              }
              @media print {
                body {
                  padding: 0 10px !important;
                  font-size: 14px !important;
                }
                .print-footer {
                            display: block;
                            position: fixed;
                            bottom: 0;
                            left: 0;
                            width: 100%;
                            text-align: center;
                            font-size: 10px;
                            color: #666;
                            padding: 5px 0;
                }
              }
              .print-footer {
                  display: none;
              }
            </style>
          </head>
          <body>
            <div class="print-header">
              <h2>开户风险查询信息</h2>
              <div>操作人：${this.opUserName} - 打印时间：${new Date().toLocaleString()}</div>
            </div>

            ${generateFormRow('开卡类型', data.cardOpeningType)}
            ${generateFormRow('客户姓名', hideSensitiveInfo(data.accountSubjectName,1,0))}
            ${generateFormRow('身份证号码', hideSensitiveInfo(data.accountCredentialNumber))}
            ${generateFormRow('手机号码', hideSensitiveInfo(data.delayPhoneNumber))}
            ${generateFormRow('所属银行', data.bankName)}
            ${generateFormRow('开户网点', data.depositBankBranch)}
            ${generateFormRow('备注', data.remarks)}
            ${generateFormRow('风险预警', data.riskCode)}
            ${generateFormRow('是否开卡', data.onOpening)}
            ${generateFormRow('开户类型', data.stockAccount)}
            ${generateFormRow('开户日期', data.accountOpenTime)}
            ${generateFormRow('开卡时间', data.cardOpeningTime)}
            ${generateFormRow('新开户账号', hideSensitiveInfo(data.accountNumber))}
            ${generateFormRow('开卡处置结果', data.disposalResults)}
            ${generateFormRow('交易跟踪号', data.transSerialNumber)}
            ${generateFormRow('记录时间', data.createTime)}

            <div class="print-footer">
                ${data.bankName || '公司名称'} - 机密文件 - 操作人: ${this.opUserName} - 打印时间: ${new Date().toLocaleString()}
            </div>
          </body>
        </html>
      `;
    },
    /**
     * 获取字典标签
     * @param {String} dictType 字典类型，如 'jyt_cardopen_type'
     * @param {String|Number} value 字典值
     * @returns {String} 字典标签
     */
    getDictLabel (dictType, value) {
      if (value == null) return '';
      const dictItems = this.dict.type[dictType];
      if (!dictItems) {
        console.warn(`字典${dictType}未定义`);
        return value;
      }
      const item = dictItems.find(item => item.value == value);
      return item ? item.label : value;
    },

    /**
     * 批量转换对象中的字典值
     * @param {Object} data 原始数据对象
     * @param {Object} fieldMap 字段映射 {字段名: 字典类型}
     * @returns {Object} 转换后的数据
     */
    translateDictValues (data, fieldMap) {
      return Object.entries(data).reduce((result, [key, value]) => {
        result[key] = fieldMap[key] ?
          this.getDictLabel(fieldMap[key], value) :
          value;
        return result;
      }, {});
    },

    //--------------------------------批量打印方法开始---------------------------------------------------

    async batchPrint (row) {
      this.printLoading = true;
      try {
        // 确定要打印的数据：如果传入row则打印单行，否则打印选中的多行
        let printRows = [];
        if (row) {
          printRows = this.selectedRows;
        } else {
          printRows = this.$refs.crud.selectionList || [];
          if (!printRows || printRows.length === 0) {
            this.$message.warning('请先选择要打印的数据');
            return;
          }
        }

        console.log("要打印的row:" + JSON.stringify(printRows));
        // 格式化日期时间
        const formatTime = (time) => {
          if (!time) return '';
          try {
            return new Date(time).toLocaleString();
          } catch {
            return time;
          }
        };


        // 2. 处理敏感信息和字典转换
        const printData = printRows.map((item, index) => ({
          index: index + 1,
          name: hideSensitiveInfo(item.accountSubjectName, 1, 0),
          idNumber: hideSensitiveInfo(item.accountCredentialNumber),
          branch: item.depositBankBranch,
          cardNumber: hideSensitiveInfo(item.accountNumber),
          time: item.createTime,
          result: this.getDictLabel('jyt_disposal_results', item.disposalResults),
          riskCode: this.getDictLabel('jyt_risk_code', item.riskCode),
          onOpening: this.getDictLabel('jyt_on_opening', item.onOpening)
        }));

        // 3. 生成打印内容
        const printHtml = this.generateTablePrintHtml(printData);

        // 4. 执行打印
        const printWindow = window.open('', '_blank');
        if (!printWindow) {
          throw new Error('请允许弹出窗口以进行打印');
        }

        printWindow.document.write(printHtml);
        printWindow.document.close();

        printWindow.onload = () => {
          setTimeout(() => {
            printWindow.print();
            setTimeout(() => printWindow.close(), 500);
          }, 300);
        };

      } catch (error) {
        console.error('打印失败:', error);
        this.$message.error(`打印失败: ${error.message}`);
      } finally {
        this.printLoading = false;
      }
    },
    // 生成表格打印HTML
    generateTablePrintHtml (data) {
      return `
     <!DOCTYPE html>
     <html>
       <head>
         <title>开户风险查询信息列表</title>
         <meta charset="utf-8">
         <style>
           body {
             font-family: "Microsoft YaHei";
             padding: 15px;
             color: #333;
           }
           .print-header {
             text-align: center;
             margin-bottom: 20px;
             padding-bottom: 10px;
             border-bottom: 1px solid #eee;
           }
           table {
             width: 100%;
             border-collapse: collapse;
             margin-bottom: 20px;
             font-size: 12px;
           }
           th, td {
             border: 1px solid #ddd;
             padding: 8px;
             text-align: center;
           }
           th {
             background-color: #f2f2f2;
             font-weight: bold;
           }
           .print-footer {
             text-align: center;
             font-size: 12px;
             color: #666;
             margin-top: 20px;
           }
           @page {
             size: A4 landscape;
             margin: 10mm;
           }
           @media print {
             body {
               padding: 0 10px !important;
               font-size: 12px !important;
             }
             .print-footer {
               position: fixed;
               bottom: 0;
               left: 0;
               width: 100%;
             }
           }
         </style>
       </head>
       <body>
         <div class="print-header">
           <h2>开户风险查询信息列表</h2>
           <div>操作人：${this.opUserName} - 打印时间：${new Date().toLocaleString()}</div>
         </div>

         <table>
           <thead>
             <tr>
               <th width="5%">序号</th>
               <th width="8%">姓名</th>
               <th width="15%">证件号</th>
               <th width="15%">网点名称</th>
               <th width="15%">卡号</th>
               <th width="12%">时间</th>
               <th width="10%">风险预警</th>
               <th width="10%">是否开卡</th>
               <th width="10%">开卡处置结果</th>
             </tr>
           </thead>
           <tbody>
             ${data.map(item => `
              <tr>
                <td>${item.index}</td>
                <td>${item.name}</td>
                <td>${item.idNumber}</td>
                <td>${item.branch}</td>
                <td>${item.cardNumber}</td>
                <td>${item.time}</td>
                <td>${item.riskCode}</td>
                <td>${item.onOpening}</td>
                <td>${item.result}</td>
              </tr>
             `).join('')}
           </tbody>
         </table>

         <div class="print-footer">
           共 ${data.length} 条记录
         </div>
       </body>
     </html>
   `;
    }

    //--------------------------------批量打印方法结束-----------------------------------------------

  },


};
</script>

<style scoped>
.icon-list {
  height: 220px;
  overflow-y: scroll;
}

/* 打印样式 */
@media print {
  body * {
    visibility: hidden;
  }

  body {
    padding: 10px !important;
    font-size: 14px !important;
  }

  .el-form-item__label {
    width: 120px !important;
    text-align: right !important;
  }

  .el-dialog,
  .el-dialog * {
    visibility: visible;
  }

  .el-dialog {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    margin: 0;
    box-shadow: none;
  }

  .no-print,
  .el-dialog__header,
  .el-dialog__footer {
    display: none !important;
  }
}

/* 详情弹框样式调整 */
.el-dialog__body .el-form-item {
  margin-bottom: 18px;
}
</style>
