import validate from '@/utils/validate';
import { Footer } from 'element-ui';
export default {
  index: false,
  indexLabel: '序号',
  rowKey: 'openacc_id',
  reserveSelection: true,
  selection: true,
  align: 'center',
  card: true,
  menuAlign: 'center',
  emptyBtnIcon: 'el-icon-refresh',
  searchMenuSpan: 6,
  searchLabelWidth: 120,
  searchMenuPosition: 'left',
  addTitle: '开户风险查询',
  viewTitle: '查看开户信息',
  editTitle: '开户反馈',
  editBtnText: '反馈',
  updateBtnText: '保存',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  searchBtn: false,
  refreshBtn: false,
  emptyBtn: false,
  labelPosition: 'right',
  labelWidth: 120,
  //searchMenuSpan: 100,
  tip: true,
  columnBtn: true,
  saveBtnText: '确定',
  // excelBtn: true,
  column: [
    {
      label: 'ID',
      prop: 'openaccId',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '开卡类型',
      prop: 'cardOpeningType',
      //查询设置：
      search: true, //查询条件
      type: "select",
      slot: true,
      width: 120,
      showColumn: false,
      addDisplay: true,
      editDisplay: true,
      editDisabled: true, //不可修改
      dicData: [],
      span: 24,
      rules: [{
        required: true,
        message: "请选择开卡类型",
        trigger: "blur,change"
      }],
    },
    {
      label: '客户姓名',
      prop: 'accountSubjectName',
      //查询设置：
      search: true, //查询条件
      rules: [
      {
        required: true,
        message: '请输入客户姓名',
        trigger: 'blur',
      }, ],
      addDisplay: true,
      editDisplay: true,
      editDisabled: true, //不可修改
      span: 24,
    },
    {
      label: '客户身份证号码',
      prop: 'accountCredentialNumber',
      //查询设置：
      search: true, //查询条件
      rules: [
      {
        required: true,
        message: '请输入客户身份证号码',
        trigger: 'blur',
      }, {
        validator: validate.checkIdentity,
        trigger: 'blur'
      }],
      addDisplay: true,
      editDisplay: true,
      editDisabled: true, //不可修改
      span: 24,
    },
    {
      label: '客户居住地址',
      prop: 'residentAddress',
      rules: [
      {
        required: false,
        message: '请输入客户居住地址',
        trigger: 'blur',
      }, ],
      addDisplay: true,
      editDisplay: true,
      span: 24,
    },
    {
      label: '手机号号码',
      prop: 'delayPhoneNumber',
      rules: [
      {
        required: false,
        message: '请输入手机号',
        trigger: 'blur',
      }, {
        validator: validate.telNumber,
        trigger: 'blur'
      }],
      addDisplay: true,
      editDisplay: true,
      span: 24,
    },
    {
      label: '所属银行',
      prop: 'bankName',
      default: '邮储银行',
      rules: [
      {
        required: true,
        message: '请输入所属银行',
        trigger: 'blur',
      }, ],
      addDisplay: true,
      editDisplay: true,
      span: 24,
    },
    {
      label: '银行开户网点',
      prop: 'depositBankBranch',
      rules: [
      {
        required: true,
        message: '请输入银行开户网点',
        trigger: 'blur',
      }, ],
      addDisplay: true,
      editDisplay: true,
      span: 24,
    },
    {
      label: '备注',
      prop: 'remarks',
      addDisplay: true,
      editDisplay: true,
      span: 24,
    },
    {
      label: '风险预警',
      prop: 'riskCode',
      //查询设置：
      search: true, //查询条件
      type: "select",
      dicData: [],
      slot: true,
      width: 150,
      showColumn: false,
      span: 24,
      addDisplay: false,
      editDisplay: true,
      editDisabled: true, //不可修改
    },
    {
      label: '是否开卡',
      prop: 'onOpening',
      //查询设置：
      search: true, //查询条件
      type: "select",
      dicData: [],
      slot: true,
      width: 150,
      showColumn: false,
      addDisplay: false,
      editDisplay: true,
      rules: [
      {
        required: true,
        message: '请选择是否开卡',
        trigger: 'blur',
      }, ],
    },
    {
      label: '开户类型',
      prop: 'stockAccount',
      type: "select",
      dicData: [],
      slot: true,
      width: 150,
      showColumn: false,
      addDisplay: false,
      editDisplay: true,
    },
    {
      label: '开户日期',
      prop: 'accountOpenTime',
      type: "datetime",
      format: "yyyy-MM-dd",
      addDisplay: false,
      editDisplay: true,
    },
    {
      label: '开卡时间',
      prop: 'cardOpeningTime',
      type: "time", // 使用 time 类型而不是 datetime
      format: "HH:mm:ss", // 注意大写 HH 表示24小时制
      valueFormat: "HH:mm:ss", // 存储格式
      addDisplay: false,
      editDisplay: true,
      pickerOptions: {
        // 可选配置
        selectableRange: '00:00:00 - 23:59:59' // 可选时间范围
      },
    },

    {
      label: '新开户账号',
      prop: 'accountNumber',
      addDisplay: false,
      editDisplay: true,
      span: 24,
    },

    {
      label: '开卡处置结果',
      prop: 'disposalResults',
      type: "select",
      dicData: [],
      slot: true,
      width: 150,
      showColumn: false,
      addDisplay: false,
      editDisplay: true,
    },
    {
      label: '转账额度额(元)',
      prop: 'transferLimitAmount',
      slot: true,
      width: 150,
      rules: [{
        validator: validate.validateAmount,
        trigger: 'blur'
      }],
      addDisplay: false,
      editDisplay: true,
    },
    {
      label: '交易跟踪号',
      prop: 'transSerialNumber',
      search: true, //查询条件
      addDisplay: false,
      editDisplay: false,
      span: 24,
    },
    {
      label: '记录时间',
      prop: 'createTime',
      addDisplay: false,
      editDisabled: true,
      search: true, //查询条件
      searchLabelWidth: 80, //左边长度
      searchSpan: 80, //右边长度
      span: 24,
    },
  ],
};
