/**
 * 通用敏感信息隐藏方法
 * @param {string} value - 要处理的敏感信息
 * @param {number} [keepHead=3] - 保留前几位，默认3
 * @param {number} [keepTail=4] - 保留后几位，默认4
 * @param {string} [maskChar='*'] - 替换字符，默认*
 * @param {number} [minLength=7] - 最小处理长度，小于此值不处理
 * @returns {string} 处理后的字符串
 */
export const hideSensitiveInfo = (value, keepHead = 3, keepTail = 4, maskChar = '*', minLength = 7) => {
  if (!value || typeof value !== 'string') return '';

  // 如果字符串长度小于最小处理长度，直接返回原值或部分隐藏
  if (value.length < minLength) {
    // 根据需求决定是否对短字符串也进行隐藏处理
    // 方案1: 完全显示
    // return value;

    // 方案2: 部分隐藏(保留首1位)
    // const shortKeep = Math.min(1, Math.floor(value.length / 2));
    // const head = value.substring(0, shortKeep);
    // const tail = value.substring(value.length - shortKeep);
    // const maskedPart = value.length - shortKeep * 2 > 0
    //   ? maskChar.repeat(value.length - shortKeep * 2)
    //   : '';
    // return `${head}${maskedPart}`;
  }

  // 计算需要替换的部分
  const head = value.substring(0, keepHead);
  const tail = keepTail > 0 ? value.substring(value.length - keepTail) : '';
  const maskedLength = value.length - keepHead - keepTail;

  // 创建替换字符串
  const maskedPart = maskedLength > 0 ? maskChar.repeat(maskedLength) : '';
  return `${head}${maskedPart}${tail}`;
};

/**
 * 根据类型自动隐藏敏感信息
 * @param {string} value - 要处理的值
 * @param {string} type - 类型: 'name'|'idCard'|'phone'|'bankCard'|'email'|'address'
 * @returns {string} 处理后的字符串
 */
export const autoHideSensitive = (value, type = 'default') => {
  if (!value || typeof value !== 'string') return '';

  const strategies = {
    name: { keepHead: 1, keepTail: 0, minLength: 2 },       // 姓名: 张*
    idCard: { keepHead: 3, keepTail: 4, minLength: 10 },    // 身份证: 110***********1234
    phone: { keepHead: 3, keepTail: 4, minLength: 7 },      // 手机号: 138****1234
    bankCard: { keepHead: 4, keepTail: 4, minLength: 8 },   // 银行卡: 6222****1234
    email: {                                                 // 邮箱: a****@example.com
      processor: (val) => {
        const atIndex = val.indexOf('@');
        if (atIndex <= 1) return val;
        return `${val.substring(0, 1)}****${val.substring(atIndex)}`;
      }
    },
    address: { keepHead: 6, keepTail: 4, minLength: 10 },  // 地址: 北京市海淀区****123号
    default: { keepHead: 3, keepTail: 3, minLength: 6 }     // 默认处理
  };

  const strategy = strategies[type] || strategies.default;

  if (strategy.processor) {
    return strategy.processor(value);
  }

  return hideSensitiveInfo(
    value,
    strategy.keepHead,
    strategy.keepTail,
    '*',
    strategy.minLength
  );
};
