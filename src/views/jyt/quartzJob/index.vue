<template>
    <div class="app-container">
        <yo-table
            @keyup.enter.native="handleQuery"
            v-loading="loading"
            :data="data"
            :option="option"
            ref="crud"
            :page.sync="page"
            :search.sync="search"
            @on-load="onLoad"
            @search-change="searchChange"
            @row-update="rowUpdate"
            @row-save="rowSave"
            @refresh-change="refresh"
            v-model="formParent"
            @selection-change="handleSelectionChange"
        >
            <template slot="searchMenu">
                <el-button
                    size="mini"
                    type="primary"
                    icon="el-icon-search"
                    @click.stop="handleQuery"
                    v-hasPermi="['admin:user:page']"
                >
                    查询
                </el-button>
                <el-button
                    size="mini"
                    icon="el-icon-refresh"
                    @click.stop="resetQuery()"
                >
                    重置
                </el-button>
            </template>
            <template slot="deptIdsForm">
                <el-input
                    :disabled="disabled"
                    v-model="deptName"
                    placeholder="点击按钮选择部门"
                    readonly
                >
                    <el-button
                        v-if="!disabled"
                        slot="append"
                        icon="el-icon-plus"
                        @click="treeShow('dept')"
                    ></el-button>
                </el-input>
            </template>
            <template slot="instIdForm">
                <el-input
                    :disabled="disabled"
                    v-model="instName"
                    placeholder="点击按钮选择机构"
                    readonly
                >
                    <el-button
                        v-if="!disabled"
                        slot="append"
                        icon="el-icon-plus"
                        @click="treeShow('inst')"
                    ></el-button>
                </el-input>
            </template>

            <!-- 自定义列 -->
            <template slot="status" slot-scope="scope">
                <el-tag
                    :type="scope.row.status == '1' ? '' : 'danger'"
                    size="mini"
                >
                    {{ scope.row.status == '1' ? '正常' : '停止' }}
                </el-tag>
            </template>
            <!-- 自定义左侧操作栏 -->
            <template slot="menuLeft">
                <el-button
                    type="primary"
                    icon="el-icon-plus"
                    size="mini"
                    plain
                    @click.stop="handleAdd"
                >
                    新增
                </el-button>
                <el-button
                    type="danger"
                    plain
                    icon="el-icon-delete"
                    size="mini"
                    :disabled="multiple"
                    @click="handleDelete"
                    v-hasPermi="['admin:user:remove:batch']"
                >
                    批量删除
                </el-button>
                <el-button
                    type="primary"
                    plain
                    icon="el-icon-check"
                    size="mini"
                    :disabled="multipleFlag"
                    @click="runNow"
                >
                    立即执行
                </el-button>
            </template>
            <!-- 自定义右侧操作栏 -->
            <template slot="menuRight">
                <input
                    id="fileslist"
                    v-show="false"
                    type="file"
                    accept=".xls"
                    ref="fileRef"
                    @change="fileChange"
                />
            </template>
            <template slot-scope="{ row, size, type, index }" slot="menu">
                <!-- <el-button
                    :size="size"
                    :type="type"
                    icon="el-icon-edit"
                    @click.stop="handleEdit(row, index)"
                    v-hasPermi="['admin:user:edit']"
                >
                    修改
                </el-button> -->
                <!-- @click="openQuartz(row) -->
                <el-button :size="size" :type="type" @click="openOrClose(row)">
                    <!-- 启动 -->
                    <span :class="btnClass(row)">
                        {{ row.status == '1' ? '停止' : '启动' }}
                    </span>
                </el-button>
                <el-dropdown
                    :size="size"
                    @command="command => handleCommand(command, row, index)"
                    v-hasPermi="['admin:user:get', 'admin:user:passwd:reset']"
                >
                    <span class="el-dropdown-link">
                        <i class="el-icon-d-arrow-right el-icon--right"></i>
                        更多
                    </span>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item
                            command="handleEdit"
                            icon="el-icon-edit"
                        >
                            修改
                        </el-dropdown-item>
                        <el-dropdown-item
                            command="handleCheckInfo"
                            icon="el-icon-info"
                        >
                            查看
                        </el-dropdown-item>
                        <el-dropdown-item
                            command="handleDelete"
                            icon="el-icon-delete"
                        >
                            删除
                        </el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
            </template>
        </yo-table>
        <el-dialog :title="title" :visible.sync="dialogVisible" width="30%">
            <el-tree
                v-if="dialogVisible && dialogType == 'dept'"
                class="icon-list"
                ref="tree"
                node-key="id"
                show-checkbox
                :check-strictly="true"
                :props="treeprops"
                :load="treeloadNode"
                lazy
            ></el-tree>
            <el-tree
                v-if="dialogVisible && dialogType == 'inst'"
                class="icon-list"
                ref="tree"
                node-key="id"
                show-checkbox
                :check-strictly="true"
                :props="treeprops"
                :load="treeloadNode"
                @check-change="checkChange"
                lazy
            ></el-tree>
            <span slot="footer" class="dialog-footer">
                <el-button
                    type="primary"
                    @click="dialogQueren(dialogType)"
                    size="mini"
                >
                    确 定
                </el-button>
                <el-button @click="dialogVisible = false" size="mini">
                    取 消
                </el-button>
            </span>
        </el-dialog>
        <run-now ref="runQuartz" @ok="refreshList"></run-now>
    </div>
</template>
<script>
import { list, add, remove, edit, resume, pauseJob } from '@/api/jytQuartzJob';
import jobOption from './infoData/jobOption.js';
import { mapActions } from 'vuex';
import runNow from './component/runNow.vue';

// import userRegister from './component/userRegister';
var identity = '';
var telNumber = '';
var emailid = '';
export default {
    name: 'User',
    dicts: ['yoaf_quartz_status'],
    components: { runNow },
    data() {
        return {
            disabledFlag: false,
            title: '',
            dialogType: '',
            loading: true,
            formParent: {},
            search: {},
            exportSearch: {},
            page: {
                pageSize: 10,
                pageNum: 1,
            },
            treeprops: {
                label: 'name',
                children: 'children',
            },
            deptName: '',
            instName: '',
            data: [],
            option: jobOption,
            dialogVisible: false,
            // 非多个禁用
            multiple: true,
            multipleFlag: true,
            disabled: false,
            status: '1',
            formParent: {},
            selectionList: [],
        };
    },
    created() {
        /** *添加字典项数据*/
        this.updateDictData(
            this.option.column,
            'status',
            this.dict.type['yoaf_quartz_status']
        );
    },
    methods: {
        ...mapActions([
            'getUser',
            'instDeptTree',
            'instTree',
            'instDeptTreeChild',
        ]),
        // 首次加载调用此方法
        onLoad(page) {
            this.getList(this.search);
            // this.$refs['crud'].toggleSelection()
        },

        refreshList() {
            this.multipleFlag = true;
            this.$refs['crud'].toggleSelection();
            this.handleQuery();
        },
        // 更新字典数据
        updateDictData(option, key, data) {
            let column = this.findObject(option, key);
            column.dicData = data;
        },
        // 搜索按钮
        searchChange(params, done) {
            this.handleQuery();
            setTimeout(() => {
                done();
            }, 1000);
        },

        // 新增表单保存
        async rowSave(form, done, loading) {
            let response = await add(form);
            if (response.code == 0) {
                this.$message.success(response.message);
                this.handleQuery();
                done();
            }
            loading();
        },
        // 刷新按钮
        refresh() {
            this.handleQuery();
        },
        /** 查询用户列表 */
        async getList(query) {
            const params = { ...query, ...this.page };
            this.loading = true;
            let response = await list(params);
            if (response.code == 0) {
                this.data = response.data.list;
                this.page.total = response.data.total;
                this.loading = false;
            }
        },
        handleView(row) {
            this.$refs.crud.rowView(row);
        },

        /** 搜索按钮操作 */
        handleQuery() {
            this.page.currentPage = 1;
            this.getList(this.search);
            this.exportSearch = this.search;
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.$refs.crud.searchReset();
            this.getList();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.selectionList = selection;
            this.ids = selection.map(item => item.id);
            this.multiple = !selection.length;
            if (selection.length == 1) {
                this.multipleFlag = false;
            } else {
                this.multipleFlag = true;
            }
        },
        // 更多操作触发
        handleCommand(command, row, index) {
            switch (command) {
                case 'handleDelete':
                    this.handleDelete(row);
                    break;
                case 'handleCheckInfo':
                    this.handleView(row);
                    break;
                case 'handleEdit':
                    this.handleEdit(row, index);
                    break;
                default:
                    break;
            }
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.status = '1';
            this.disabledFlag = false;
            this.$refs.crud.rowAdd();
        },
        // 修改
        async handleEdit(row, index) {
            this.formParent = row;
            this.$refs.crud.rowEdit(row, index);
        },
        // 修改表单保存
        rowUpdate(form, index, done, loading) {
            edit(form).then(response => {
                done();
                if (response.code == 0) {
                    this.$message.success(response.message);
                    this.handleQuery();
                }
            });
        },
        openOrClose(row) {
            let tipWord = '';
            if (row.status == '1') {
                tipWord = '是否停止该定时任务？';
            } else if (row.status == '2') {
                tipWord = '是否启动该定时任务？';
            }
            this.$confirm(tipWord, '提示', {})
                .then(async () => {
                    if (row.status == '1') {
                        //停止定时任务
                        this.closQuartz(row);
                    } else if (row.status == '2') {
                        //启动定时任务
                        this.openQuartz(row);
                    }
                })
                .catch(() => {});
        },
        /**启动定时任务 */
        async openQuartz(row) {
            this.loading = true;
            let res = await resume(row);
            if (res.code == 0) {
                this.$message.success(res.message);
            } else {
                this.$message.error('启动失败！');
            }
            this.getList();
            this.loading = false;
        },
        /**停止定时任务 */
        async closQuartz(row) {
            this.loading = true;
            let res = await pauseJob(row);
            if (res.code == 0) {
                this.$message.success(res.message);
            } else {
                this.$message.error('停止失败！');
            }
            this.getList();
            this.loading = false;
        },

        async handleDel(id) {
            let res = await remove(id);
            if (res.code == 0) {
                this.$message.success(res.message);
                this.$refs.crud.toggleSelection();
                this.getList();
            }
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            this.$confirm(`是否永久删除选中的数据项？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                showCanidscelButton: true,
            })
                .then(async () => {
                    // 删除单个
                    if (row.id) {
                        this.handleDel(row.id);
                    } else {
                        //批量删除
                        if (
                            this.ids instanceof Array &&
                            this.ids != null &&
                            this.ids != '' &&
                            this.ids != undefined
                        ) {
                            this.handleDel(this.ids.join(','));
                        }
                    }
                })
                .catch(() => {});
        },
        /**立即执行定时任务 */
        runNow() {
            this.$refs.runQuartz.show(this.selectionList[0]);
        },
        /** 处理返回的流文件 */
        handleExportData(res) {
            if (!res) return;
            let data = res.data;
            let filename = res.headers['content-disposition'].split('=')[1];
            let _filename = decodeURI(filename);
            const link = document.createElement('a');
            //创建 Blob对象 可以存储二进制文件
            let blob = new Blob([data], { type: 'application/x-excel' });
            link.style.display = 'none';
            link.href = URL.createObjectURL(blob);
            link.download = _filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        },
        fileChange(event) {
            let file = event.target.files[0];
            let formData = new FormData();
            formData.append('file', file);
            importUsers(formData).then(res => {
                if (res.code == 0) {
                    this.$message.success(res.message);
                    this.handleQuery();
                }
            });
            // 清除文件，防止下次上传相同文件无反应
            event.target.value = null;
        },

        //过滤列表数据
        filterData(data) {
            var _filterData = ele => {
                if (ele && ele.length) {
                    ele.forEach(item => {
                        if (item.type == 'inst') {
                            item.disabled = true;
                        }
                        if (item.children) {
                            _filterData(item.children);
                        }
                    });
                }
            };
            if (this.dialogType == 'dept') _filterData(data);
            return data;
        },
        /**敏感信息显隐**/
        viewUserInfo(data, type) {
            let sensitiveStatus = data[type].includes('**') ? '1' : '2';
            usersSensitive(data.userName, sensitiveStatus).then(response => {
                const res = response.data;
                this.data.forEach(item => {
                    if (item.userName == data.userName) {
                        item[type] = res[type];
                    }
                });
            });
        },
        checkChange(data, checked, node) {
            if (checked) {
                this.$refs.tree.setCheckedNodes([data]);
            }
        },
        treeShow(val) {
            this.title = val == 'dept' ? '部门列表' : '机构列表';
            this.dialogType = val;
            this.dialogVisible = true;
        },
        dialogQueren(type) {
            if (type == 'dept') {
                let arr = this.$refs.tree.getCheckedNodes();
                let deptName = arr.map(i => {
                    return i.name;
                });
                this.deptName = deptName.join(',');
                let deptIds = arr.map(i => {
                    return i.id;
                });
                this.formParent.deptIds = deptIds;
            } else {
                let arr = this.$refs.tree.getCheckedNodes();
                if (!arr.length) {
                    this.instName = '';
                    this.formParent.instId = '';
                } else {
                    this.instName = arr[0].name;
                    this.formParent.instId = arr[0].id;
                }
            }
            this.dialogVisible = false;
        },
        treeloadNode(node, resolve) {
            let isDept = this.dialogType == 'dept';
            if (node.level === 0) {
                this.instTree(this.dialogType).then(res => {
                    let nodes = this.filterData(res);
                    return resolve(nodes);
                });
            }
            if (node.level == 1) {
                if (node.data.children) {
                    let nodes = this.filterData(node.data.children);
                    return resolve(nodes);
                } else {
                    return resolve([]);
                }
            }
            if (node.level > 1) {
                const query = {
                    id: node.data.id,
                    queryType: node.data.queryType,
                };
                this.instDeptTreeChild(query).then(res => {
                    let nodes = this.filterData(res);
                    return resolve(nodes);
                });
            }
        },
        iconChange(row) {
            if (row.status == '2') {
                return 'el-icon-switch-button';
            } else if (row.status == '2') {
                return 'el-icon-close';
            }
        },
        btnClass(row) {
            if (row.status == '2') {
                return 'success';
            } else if (row.status == '1') {
                return 'warning';
            }
        },
    },
};
</script>

<style scoped>
.icon-list {
    height: 220px;
    overflow-y: scroll;
}

.success {
    color: #409eff;
}
.warning {
    color: #f56c6c;
}
</style>
