<template>
    <el-dialog
        style="text-align: center"
        :visible.sync="visible"
        append-to-body
        width="25%"
    >
        <div slot="title" class="yo-table__dialog__header">
            <span class="el-dialog__title">立即执行(单次)</span>
        </div>

        <el-form
            ref="ruleForm"
            label-position="top"
            :inline="true"
            :model="formInline"
            class="demo-form-inline"
        >
            <el-form-item label="请输入参数">
                <el-input
                    rows="6"
                    style="width: 120%"
                    type="textarea"
                    v-model="formInline.parameter"
                    placeholder="请输入参数"
                ></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer" v-loading="loading">
            <el-button
                type="primary"
                :size="option.size || 'small'"
                @click="handleRun"
            >
                立即执行
            </el-button>
            <el-button :size="option.size || 'small'" @click="cancelSelectUser">
                取 消
            </el-button>
        </div>
    </el-dialog>
</template>

<script>
import { immediate } from '@/api/jytQuartzJob';

export default {
    dicts: ['yoaf_teller_sex'],
    data() {
        return {
            formInline: {
                parameter: '',
            },
            formParent: {},
            fullscreen: false,
            search: {},
            loading: false,
            page: {
                pageSize: 10,
                currentPage: 1,
            },
            option: {
                selection: true,
                align: 'center',
                menu: false,
                emptyBtn: false,
                addBtn: false,
                editBtn: false,
                delBtn: false,
                columnBtn: false,
                refreshBtn: false,
                tip: false,
                searchShowBtn: false,
                rowInfo: {},
            },
            // 遮罩层
            visible: false,
            // 选中数组值
            instIds: [],
            // 授权用户数据
            instList: [],
        };
    },
    computed: {},
    methods: {
        // 显示弹框
        show(row) {
            this.loading = false;
            this.rowInfo = row;
            this.formInline.parameter = row.parameter;
            this.visible = true;
            this.fullscreen = false;
        },
        closeDialog() {
            this.cancelSelectUser();
        },
        handleFullScreen() {
            this.fullscreen = !this.fullscreen;
        },
        cancelSelectUser() {
            this.visible = false;
            this.fullscreen = false;
        },
        async handleRun() {
            this.loading = true;
            this.rowInfo.parameter = this.formInline.parameter;
            let res = await immediate(this.rowInfo);
            if (res.code == 0) {
                this.$message.success(res.message);
            } else {
                this.$message.error(res.message);
                this.loading = false;
            }
            this.closeDialog();
            this.$emit('ok');
        },
    },
};
</script>
<style scoped></style>
