<template>
    <div>
        <el-dialog
            title="参数配置"
            :visible.sync="dialogVisible"
            width="85%"
            :before-close="handleClose"
        >
            <el-button type="primary" @click="addRow">新增行</el-button>
            <el-row>
                <el-col :span="16">
                    <el-table
                        :data="data"
                        style="width: 100%; margin-bottom: 20px"
                        row-key="id"
                        default-expand-all
                        :tree-props="{
                            children: 'children',
                            hasChildren: 'hasChildren',
                        }"
                    >
                        <el-table-column label="" width="60"></el-table-column>

                        <el-table-column prop="header" label="头部" width="180">
                            <template slot-scope="scope">
                                <el-input
                                    @input="dataChange"
                                    v-model="scope.row.header"
                                ></el-input>
                            </template>
                        </el-table-column>

                        <el-table-column prop="content" label="内容">
                            <template slot-scope="scope">
                                <el-input
                                    @input="dataChange"
                                    v-model="scope.row.content"
                                ></el-input>
                            </template>
                        </el-table-column>

                        <el-table-column prop="idEdit" label="是否可编辑">
                            <template slot-scope="scope">
                                <el-input v-model="scope.row.idEdit"></el-input>
                            </template>
                        </el-table-column>

                        <el-table-column label="操作" width="280">
                            <template slot-scope="scope">
                                <el-button @click="addLow(scope)">
                                    添加下级
                                </el-button>
                                <el-button
                                    type="denger"
                                    @click="deleteData(scope)"
                                >
                                    删除
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-col>
                <el-col :span="8">
                    <div
                        class="grid-content bg-purple-light"
                        style="background-color: antiquewhite"
                    >
                        <!-- <span style="white-space: pre-wrap">{{ yldata }}</span> -->
                        <el-card class="card">
                            <span>配置预览</span>
                            <pre v-html="yldata"></pre>
                        </el-card>
                    </div>
                </el-col>
            </el-row>

            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="sureBtn">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
export default {
    name: 'Auditlog',
    dicts: [],
    components: {},
    data() {
        return {
            dialogVisible: false,
            tableData: [],
            par: {
                id: '',
                header: '',
                content: '',
                defaultValue: '',
                idEdit: '',
                children: [],
                flor: '',
            },

            multipleSelection: [],
            yldata: '{}',
            lastId: '',
            newData: [],
            data: [],
        };
    },
    created() {},
    methods: {
        handleClose(done) {
            this.$confirm('确认关闭？')
                .then(_ => {
                    done();
                })
                .catch(_ => {});
        },
        openModal() {
            // this.tableData = [];
            this.data = this.handlerChild(this.tableData);
            this.dialogVisible = true;
        },
        toggleSelection(rows) {
            if (rows) {
                rows.forEach(row => {
                    this.$refs.multipleTable.toggleRowSelection(row);
                });
            } else {
                this.$refs.multipleTable.clearSelection();
            }
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        addRow() {
            let uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
                /[xy]/g,
                function (c) {
                    let r = (Math.random() * 16) | 0,
                        v = c === 'x' ? r : (r & 0x3) | 0x8;
                    return v.toString(16);
                }
            );
            let par = {
                id: uuid,
                header: '',
                content: '',
                defaultValue: '',
                idEdit: '',
                children: [],
                flor: 1,
                pid: '',
            };
            this.tableData.push(par);
            this.data = this.handlerChild(this.tableData);
        },
        addLow(scope) {
            let uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
                /[xy]/g,
                function (c) {
                    let r = (Math.random() * 16) | 0,
                        v = c === 'x' ? r : (r & 0x3) | 0x8;
                    return v.toString(16);
                }
            );
            let par = {
                id: uuid,
                header: '',
                content: '',
                defaultValue: '',
                idEdit: '',
                children: [],
                flor: scope.row.flor + 1,
                pid: scope.row.id,
            };
            this.tableData.push(par);
            this.data = this.handlerChild(this.tableData);
        },
        deleteData(scope) {
            this.tableData = this.tableData.filter(
                item => item.id != scope.row.id
            );
            this.data = this.handlerChild(this.tableData);
            this.zhDATA(this.data);
        },
        handlerChild(list) {
            list.forEach(item => {
                item.children = [];
            });
            return this.transListDataToTreeData(list, '');
        },
        transListDataToTreeData(list, root) {
            const arr = [];
            // 1.遍历
            list.forEach(item => {
                // 2.首次传入空字符串  判断list的pid是否为空 如果为空就是一级节点
                if (item.pid === root) {
                    // 找到之后就要去找item下面有没有子节点  以 item.id 作为 父 id, 接着往下找
                    const children = this.transListDataToTreeData(
                        list,
                        item.id
                    );
                    if (children.length > 0) {
                        // 如果children的长度大于0,说明找到了子节点
                        item.children = children;
                    }
                    // 将item项, 追加到arr数组中
                    arr.push(item);
                }
            });
            return arr;
        },
        dataChange() {
            this.zhDATA(this.data);
        },
        zhDATA(arr) {
            this.yldata = '{}';
            let s = '{ \n';
            arr.forEach(item => {
                s =
                    s +
                    this.getNULL(item.flor, 1) +
                    '"' +
                    item.header +
                    '"' +
                    ':' +
                    this.judgeChild(item, item.content) +
                    ', \n';
            });
            s = s + this.getNULL(arr[0].flor ? arr[0].flor : 0, 0) + '}';
            this.yldata = s;
            return s;
        },
        judgeChild(row, c) {
            if (row.children.length > 0) {
                return this.zhDATA(row.children);
            } else {
                return c == '{}' ? c : '"' + c + '"';
            }
        },
        getNULL(num, flag) {
            let str = '';
            for (let i = 1; i < num + flag; i++) {
                let newSart = '   ';
                str += newSart;
            }
            return str;
        },
        sureBtn() {
            this.$emit('handleData', this.tableData);
            this.dialogVisible = false;
        },
    },
};
</script>
