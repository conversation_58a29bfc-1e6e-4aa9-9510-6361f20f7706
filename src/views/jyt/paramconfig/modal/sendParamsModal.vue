<template>
    <div>
        <el-dialog
            title="发送参数"
            :visible.sync="dialogVisible"
            width="85%"
            :before-close="handleClose"
        >
            <el-table
                :data="data"
                style="width: 100%; margin-bottom: 20px"
                row-key="id"
                default-expand-all
            >
                <el-table-column label="" width="60"></el-table-column>

                <el-table-column prop="header" label="头部" width="180">
               
                </el-table-column>

                <el-table-column prop="content" label="内容">
                    <template slot-scope="scope">
                        <el-input
                            :disabled="scope.row.idEdit != '是'"
                            v-model="scope.row.content"
                        ></el-input>
                    </template>
                </el-table-column>
                <!-- <el-table-column label="操作" width="280">
                    <template slot-scope="scope">
                        <el-button @click="addLow(scope)">添加下级</el-button>
                        <el-button type="denger" @click="deleteData(scope)">
                            删除
                        </el-button>
                    </template>
                </el-table-column> -->
            </el-table>
        </el-dialog>
    </div>
</template>
<script>
export default {
    name: 'Auditlog',
    dicts: [],
    components: {},
    data() {
        return {
            dialogVisible: false,
            data: [],
        };
    },
    created() {},
    methods: {
        handleClose() {
            this.dialogVisible = false;
        },
        openModal(param) {
            this.data = [];
            let datas = param;
            datas.forEach(element => {
                element.children = [];
                if (element.idEdit == '是') {
                    this.data.push(element);
                }
            });
            this.dialogVisible = true;
        },
    },
};
</script>
