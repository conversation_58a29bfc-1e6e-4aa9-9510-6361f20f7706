<template>
    <div>
        <el-button type="primary" @click="paramsConfig">参数配置</el-button>
        <el-button type="primary" @click="sendParam">发送参数</el-button>

        <paramsConfig
            ref="paramsConfigRef"
            @handleData="handleData"
        ></paramsConfig>
        <sendparamModel ref="sendparamModelRef"></sendparamModel>
    </div>
</template>
<script>
import paramsConfig from './modal/paramsConfigModal.vue';
import sendparamModel from './modal/sendParamsModal.vue';
export default {
    name: 'Auditlog',
    dicts: [],
    components: { paramsConfig, sendparamModel },
    data() {
        return {
            tableData: [],
        };
    },
    created() {},
    methods: {
        paramsConfig() {
            this.$refs.paramsConfigRef.openModal();
        },
        sendParam() {
            this.$refs.sendparamModelRef.openModal(this.tableData);
        },
        handleData(data) {
            
            this.tableData = data;
        },
    },
};
</script>
