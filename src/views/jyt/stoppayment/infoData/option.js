import validate from '@/utils/validate';
import { Footer } from 'element-ui';
export default {
    index: true,
    indexLabel: '序号',
    rowKey: 'userId',
    reserveSelection: true,
    selection: true,
    align: 'center',
    card: true,
    menuAlign: 'center',
    emptyBtnIcon: 'el-icon-refresh',
    searchMenuSpan: 6,
    searchLabelWidth: 100,
    searchMenuPosition: 'left',
    addTitle: '预警数据报送',
    viewTitle: '止付保护措施详情',
    editTitle: '修改用户',
    editBtnText: '修改',
    updateBtnText: '保存',
    addBtn: false,
    editBtn: false,
    delBtn: false,
    searchBtn: false,
    refreshBtn: false,
    emptyBtn: false,
    labelPosition: 'right',
    labelWidth: 170,
    tip: false,
    columnBtn: false,
    saveBtnText: '确定',
    // excelBtn: true,
    column: [
        {
            label: '发起类型',
            prop: 'stopType',
            search: true,
            type: 'select',
            dicData: [
                { label: '止付', value: '止付' },
                { label: '解止付', value: '解止付' },
            ],
        },
        {
            label: '止付发起时间',
            prop: 'initiationTime',
            editDisabled: true,
            
            search: true,
            width: 180,
        },
        {
            label: '管控对象类型',
            prop: 'accountType',
            search: true,
            type: 'select',
            dicData: [],
        },
        {
            label: '管控对象',
            prop: 'accountName',
            search: true,
        },
        {
            label: '管控类型',
            prop: 'reason',
            type: 'select',
        },
        {
            label: '管控金额',
            prop: 'controlAmount',
        },
        {
            label: '管控时长',
            prop: 'controlDuration',
        },
        {
            label: '发起单位',
            prop: 'sendUnitName',
        },
        {
            label: '发起人',
            prop: 'sendPersonName',
        },
        {
            label: '发起人联系电话',
            prop: 'sendPersonPhone',
        },
        {
            label: '请求是否成功',
            prop: 'isSuccess',
        },
        {
            label: '备注（请求失败时信息记录）',
            prop: 'respDesc',
            span: 24,
            width: '200',
        },
        {
            label: '附件',
            prop: 'fileNameList',
            span: 24,
            hide: true,
            slot: true,
        },
    ],
};
