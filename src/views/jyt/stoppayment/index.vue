<template>
    <div class="app-container">
        <yo-table
            v-loading="loading"
            :data="data"
            :option="option"
            ref="crud"
            :page.sync="page"
            :search.sync="search"
            @on-load="onLoad"
            @search-change="searchChange"
            @refresh-change="refresh"
            v-model="formParent"
            @selection-change="handleSelectionChange"
        >
            <template slot-scope="{ scope }" slot="searchMenu">
                <el-button
                    size="mini"
                    type="primary"
                    icon="el-icon-search"
                    @click.stop="handleQuery"
                    v-hasPermi="['admin:user:page']"
                >
                    查询
                </el-button>
                <el-button
                    size="mini"
                    icon="el-icon-refresh"
                    @click.stop="resetQuery()"
                >
                    重置
                </el-button>
            </template>
            <template slot-scope="scope" slot="deptIdsForm">
                <el-input
                    :disabled="disabled"
                    v-model="deptName"
                    placeholder="点击按钮选择部门"
                    readonly
                >
                    <el-button
                        v-if="!disabled"
                        slot="append"
                        icon="el-icon-plus"
                        @click="treeShow('dept')"
                    ></el-button>
                </el-input>
            </template>
            <template slot-scope="scope" slot="fileNameListForm">
                <span v-for="item in scope.value">
                    <el-button icon="el-icon-paperclip" @click="downFile(item)">
                        {{ item }}
                    </el-button>
                </span>
            </template>
            <template slot-scope="scope" slot="userStatForm">
                <el-select
                    :disabled="disabled"
                    v-model="formParent.userStat"
                    placeholder="请选择 用户状态"
                    clearable
                >
                    <el-option
                        v-for="dict in dict.type['yoaf_user_state']"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                        :disabled="dict.value == '04'"
                    />
                </el-select>
            </template>
            <template slot-scope="scope" slot="empStatusForm">
                <el-select
                    :disabled="disabled"
                    v-model="formParent.empStatus"
                    placeholder="请选择 人员状态"
                    clearable
                >
                    <el-option
                        v-for="dict in dict.type['yoaf_emp_status']"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                </el-select>
            </template>

            <!-- 自定义列 -->
            <template slot="idCard" slot-scope="scope">
                <div>
                    <span>{{ scope.row.idCard }}</span>
                    <el-button
                        v-if="scope.row.idCard"
                        type="text"
                        icon="el-icon-view"
                        v-hasPermi="['admin:user:sensitive:get']"
                        @click="viewUserInfo(scope.row, 'idCard')"
                    ></el-button>
                </div>
            </template>
            <template slot-scope="scope" slot="accountTypeForm">
                <el-select
                    :disabled="disabled"
                    v-model="formParent.accountType"
                    placeholder="请选择 管控对象类型"
                    clearable
                >
                    <el-option
                        v-for="dict in dict.type['jyt_account_type']"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                        :disabled="dict.value == '04'"
                    />
                </el-select>
            </template>
            <template slot="phoneNumber" slot-scope="scope">
                <div>
                    <span>{{ scope.row.phoneNumber }}</span>
                    <el-button
                        v-if="scope.row.phoneNumber"
                        type="text"
                        icon="el-icon-view"
                        v-hasPermi="['admin:user:sensitive:get']"
                        @click="viewUserInfo(scope.row, 'phoneNumber')"
                    ></el-button>
                </div>
            </template>
            <template slot="gender" slot-scope="scope">
                <el-tag
                    :type="scope.row.gender == '1' ? '' : 'danger'"
                    size="mini"
                >
                    {{ scope.row.gender == '1' ? '男' : '女' }}
                </el-tag>
            </template>
            <template slot="userStat" slot-scope="scope">
                <el-tag size="mini" effect="plain" :type="tagType(scope.row)">
                    {{ userStat(scope.row) }}
                </el-tag>
            </template>
            <template slot="empStatus" slot-scope="scope">
                <el-tag size="mini" effect="plain" :type="tagType2(scope.row)">
                    {{ empStatus(scope.row) }}
                </el-tag>
            </template>

            <!-- 自定义搜索 -->
            <template
                slot-scope="{ disabled, size }"
                slot="initiationTimeSearch"
            >
                <el-date-picker
                    clearable
                    v-model="search.initiationTimeBox"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    @change="changeFn()"
                ></el-date-picker>
            </template>
            <template slot-scope="{ size }" slot="menuRight">
                <el-button
                    type="success"
                    icon="el-icon-download"
                    size="mini"
                    @click="handleExport"
                >
                    导出
                </el-button>
            </template>
            <!-- 自定义左侧操作栏 -->

            <template slot-scope="{ size }" slot="menuLeft">
                <!-- <el-button
                    type="primary"
                    icon="el-icon-document-checked"
                    size="mini"
                    @click.stop="handleAdd"
                >
                    报送
                </el-button> -->
            </template>

            <template slot-scope="{ row, size, type, index }" slot="menu">
                <el-button
                    :size="size"
                    :type="type"
                    @click="handleCommand('handleCheckInfo', row)"
                    icon="el-icon-info"
                >
                    查看
                </el-button>
            </template>
        </yo-table>
        <el-dialog :title="title" :visible.sync="dialogVisible" width="30%">
            <el-tree
                v-if="dialogVisible && dialogType == 'dept'"
                class="icon-list"
                ref="tree"
                node-key="id"
                show-checkbox
                :check-strictly="true"
                :props="treeprops"
                :load="treeloadNode"
                lazy
            ></el-tree>
            <el-tree
                v-if="dialogVisible && dialogType == 'inst'"
                class="icon-list"
                ref="tree"
                node-key="id"
                show-checkbox
                :check-strictly="true"
                :props="treeprops"
                :load="treeloadNode"
                @check-change="checkChange"
                lazy
            ></el-tree>
            <span slot="footer" class="dialog-footer">
                <el-button
                    type="primary"
                    @click="dialogQueren(dialogType)"
                    size="mini"
                >
                    确 定
                </el-button>
                <el-button @click="dialogVisible = false" size="mini">
                    取 消
                </el-button>
            </span>
        </el-dialog>
        <!-- <user-register ref="register" @ok="refreshList" /> -->
    </div>
</template>
<script>
import { listData, downFile,exportData } from '@/api/jyt/stopPayment';
import userOption from './infoData/option.js';

import Cookies from 'js-cookie';

var identity = '';
var telNumber = '';
var emailid = '';
export default {
    name: 'User',
    dicts: ['jyt_account_type', 'jyt_reason'],
    components: {},
    data() {
        return {
            title: '',
            dialogType: '',
            loading: true,
            formParent: {},
            search: {},
            exportSearch: {},
            page: {
                pageSize: 10,
                pageNum: 1,
            },
            treeprops: {
                label: 'name',
                children: 'children',
            },
            deptName: '',
            instName: '',
            data: [],
            option: userOption,
            dialogVisible: false,
            // 非多个禁用
            multiple: true,
            currentFileName: [],
            currentRow: {},
            disabled: true,
        };
    },
    created() {
        /** *添加字典项数据*/
        this.updateDictData(
            this.option.column,
            'accountType',
            this.dict.type['jyt_account_type']
        );

        this.updateDictData(
            this.option.column,
            'reason',
            this.dict.type['jyt_reason']
        );
    },
    methods: {
        // 首次加载调用此方法
        onLoad(page) {
            this.getList(this.search);
            // this.$refs['crud'].toggleSelection()
        },

        refreshList() {
            this.handleQuery();
        },
        // 更新字典数据
        updateDictData(option, key, data) {
            let column = this.findObject(option, key);
            column.dicData = data;
        },
        // 搜索按钮
        searchChange(params, done) {
            this.handleQuery();
            setTimeout(() => {
                done();
            }, 1000);
        },

        // 刷新按钮
        refresh() {
            this.handleQuery();
        },
        /** 查询用户列表 */
        async getList(query) {
            const params = { ...query, ...this.page };
            this.loading = true;
            let response = await listData(params);
            if (response.code == 0) {
                this.data = response.data.list;
                this.page.total = response.data.total;
                this.loading = false;
            }
        },
        /****注册按钮 */
        handleRegister() {
            this.$refs.register.show();
        },

        /** 搜索按钮操作 */
        handleQuery() {
            this.page.currentPage = 1;
            this.getList(this.search);
            this.exportSearch = this.search;
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.$refs.crud.searchReset();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.userName = selection.map(item => item.userName);
            this.ids = selection.map(item => item.userId);
            this.multiple = !selection.length;
        },
        // 更多操作触发
        handleCommand(command, row) {
            this.currentRow = row;
            this.$refs.crud.rowView(row);
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.deptName = '';
            this.instName = '';
            this.$refs.crud.rowAdd();
        },

        /** 处理返回的流文件 */
        handleExportData(res) {
            if (!res) return;
            let data = res.data;
            let filename = res.headers['content-disposition'].split('=')[1];
            let _filename = decodeURI(filename);
            const link = document.createElement('a');
            //创建 Blob对象 可以存储二进制文件
            let blob = new Blob([data], { type: 'application/x-excel' });
            link.style.display = 'none';
            link.href = URL.createObjectURL(blob);
            link.download = _filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        },
        /** 下载模板 */
        exportExample() {
            downloadExample().then(res => {
                this.handleExportData(res);
            });
        },
        /** 导出按钮操作 */
        handleExport() {
            console.log('导出数据：', this.search);
            exportData(this.search).then(res => {
                console.log('导出的返回数据：', res);
                this.handleExportData(res);
            });
        },

        fileChange(event) {
            let file = event.target.files[0];
            let formData = new FormData();
            formData.append('file', file);
            importUsers(formData).then(res => {
                if (res.code == 0) {
                    this.$message.success(res.message);
                    this.handleQuery();
                }
            });
            // 清除文件，防止下次上传相同文件无反应
            event.target.value = null;
        },
        /** 导入按钮操作 */
        handleImport() {
            this.$refs.fileRef.dispatchEvent(new MouseEvent('click'));
            // this.$refs.fileRef.click();
        },
        //过滤列表数据
        filterData(data) {
            var _filterData = ele => {
                if (ele && ele.length) {
                    ele.forEach(item => {
                        if (item.type == 'inst') {
                            item.disabled = true;
                        }
                        if (item.children) {
                            _filterData(item.children);
                        }
                    });
                }
            };
            if (this.dialogType == 'dept') _filterData(data);
            return data;
        },
        /**敏感信息显隐**/
        viewUserInfo(data, type) {
            let sensitiveStatus = data[type].includes('**') ? '1' : '2';
            usersSensitive(data.userName, sensitiveStatus).then(response => {
                const res = response.data;
                this.data.forEach(item => {
                    if (item.userName == data.userName) {
                        item[type] = res[type];
                    }
                });
            });
        },
        checkChange(data, checked, node) {
            if (checked) {
                this.$refs.tree.setCheckedNodes([data]);
            }
        },
        treeShow(val) {
            this.title = val == 'dept' ? '部门列表' : '机构列表';
            this.dialogType = val;
            this.dialogVisible = true;
        },
        dialogQueren(type) {
            if (type == 'dept') {
                let arr = this.$refs.tree.getCheckedNodes();
                let deptName = arr.map(i => {
                    return i.name;
                });
                this.deptName = deptName.join(',');
                let deptIds = arr.map(i => {
                    return i.id;
                });
                this.formParent.deptIds = deptIds;
            } else {
                let arr = this.$refs.tree.getCheckedNodes();
                if (!arr.length) {
                    this.instName = '';
                    this.formParent.instId = '';
                } else {
                    this.instName = arr[0].name;
                    this.formParent.instId = arr[0].id;
                }
            }
            this.dialogVisible = false;
        },
        treeloadNode(node, resolve) {
            let isDept = this.dialogType == 'dept';
            if (node.level === 0) {
                this.instTree(this.dialogType).then(res => {
                    let nodes = this.filterData(res);
                    return resolve(nodes);
                });
            }
            if (node.level == 1) {
                if (node.data.children) {
                    let nodes = this.filterData(node.data.children);
                    return resolve(nodes);
                } else {
                    return resolve([]);
                }
            }
            if (node.level > 1) {
                const query = {
                    id: node.data.id,
                    queryType: node.data.queryType,
                };
                this.instDeptTreeChild(query).then(res => {
                    let nodes = this.filterData(res);
                    return resolve(nodes);
                });
            }
        },
        // async downFile(fileName) {
        //     this.currentRow.fileName = fileName;
        //     const response = await downFile(this.currentRow);
        //     console.log(response);
        // },
        async downFile(fileName) {
            console.log(fileName);
            this.currentRow.fileName = fileName;
            try {
                const response = await downFile(this.currentRow);
                // 创建临时链接
                const url = window.URL.createObjectURL(
                    new Blob([response.data])
                );
                const link = document.createElement('a');
                link.href = url;

                // 从响应头获取文件名
                const fileName =
                    response.headers['content-disposition']
                        ?.split('filename=')[1]
                        ?.replace(/"/g, '') || 'file';

                link.setAttribute('download', fileName);
                document.body.appendChild(link);
                link.click();

                // 清理资源
                link.remove();
                window.URL.revokeObjectURL(url);
            } catch (error) {
                console.error('下载失败:', error);
                alert('文件下载失败，请重试！');
            }
        },
        userStat(row) {
            switch (row.userStat) {
                case '01':
                    return '启用';
                case '02':
                    return '停用';
                case '03':
                    return '注销';
                case '04':
                    return '锁定';
                default:
                    break;
            }
        },
        //tag样式
        tagType(row) {
            switch (row.userStat) {
                case '01':
                    return 'success';
                case '02':
                    return 'danger';
                case '03':
                    return 'info';
                case '04':
                    return 'warning';
                default:
                    break;
            }
        },
        //20240508改造新增字段：人员状态
        empStatus(row) {
            switch (row.empStatus) {
                case 'on':
                    return '在岗';
                case 'wait':
                    return '待岗';
                case 'off':
                    return '退休';
                case 'leave':
                    return '离职';
                default:
                    break;
            }
        },
        //tag样式
        tagType2(row) {
            switch (row.empStatus) {
                case 'on':
                    return 'success';
                case 'wait':
                    return 'warning';
                case 'off':
                    return 'info';
                case 'leave':
                    return 'danger';
                default:
                    break;
            }
        },
    },
};
</script>

<style scoped>
.icon-list {
    height: 220px;
    overflow-y: scroll;
}
</style>
