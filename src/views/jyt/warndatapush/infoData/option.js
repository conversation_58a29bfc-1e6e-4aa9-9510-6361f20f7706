import validate from '@/utils/validate';
import { Footer } from 'element-ui';
export default {
    index: true,
    indexLabel: '序号',
    rowKey: 'userId',
    reserveSelection: true,
    selection: true,
    align: 'center',
    card: true,
    menuAlign: 'center',
    emptyBtnIcon: 'el-icon-refresh',
    searchMenuSpan: 6,
    searchMenuPosition: 'left',
    addTitle: '预警数据报送',
    viewTitle: '查看预警数据',
    editTitle: '修改用户',
    editBtnText: '修改',
    updateBtnText: '保存',
    addBtn: false,
    editBtn: false,
    delBtn: false,
    searchBtn: false,
    refreshBtn: false,
    emptyBtn: false,
    labelPosition: 'right',
    labelWidth: 170,
    searchMenuSpan: 200 ,
    tip: false,
    columnBtn: false,
    saveBtnText: '确定',
    // excelBtn: true,
    column: [
        {
            label: '资金档案日期',
            prop: 'fundFileData',
            search: true,
            editDisabled: true,
            rules: [
                {
                    required: true,
                    message: '请选择资金档案日期',
                    trigger: 'blur',
                },
            ],
            type: 'date',
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
        },
        {
            label: '卡号',
            prop: 'cardNumber',
            search: true,
            rules: [
                {
                    required: true,
                    message: '请输入卡号',
                    trigger: 'blur',
                },
            ],
        },
        {
            label: '开户人',
            prop: 'accountHolder',
            rules: [
                {
                    required: true,
                    message: '请输入开户人',
                    trigger: 'blur',
                },
            ],
        },
        {
            label: '开户人身份证',
            prop: 'personnelId',
            rules: [
                {
                    required: true,
                    message: '请输入开户人身份证',
                    trigger: 'blur',
                },
            ],
        },
        {
            label: '开户人手机号',
            prop: 'personnelMobile',
            rules: [
                {
                    required: true,
                    message: '请输入开户人手机号',
                    trigger: 'blur',
                },
            ],
        },
        {
            label: '开户日期',
            prop: 'openingDate',
            rules: [
                {
                    required: true,
                    message: '请选择开户日期',
                    trigger: 'blur',
                },
            ],
            type: 'date',
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
        },
        {
            label: '历史出账交易对手',
            prop: 'outboundCounterparty',
            rules: [
                {
                    required: true,
                    message: '请输入历史出账交易对手',
                    trigger: 'blur',
                },
            ],
        },
        {
            label: '历史进账交易对手',
            prop: 'incomingCounterparty',
            rules: [
                {
                    required: true,
                    message: '请输入历史进账交易对手',
                    trigger: 'blur',
                },
            ],
        },
        {
            label: '转出天数',
            prop: 'transferOutDays',
            rules: [
                {
                    required: true,
                    message: '请输入转出天数',
                    trigger: 'blur',
                },
            ],
        },
        {
            label: '平均每日转出金额',
            prop: 'dailyTransferAmount',
            rules: [
                {
                    required: true,
                    message: '请输入平均每日转出金额',
                    trigger: 'blur',
                },
            ],
        },
        {
            label: '转出笔数',
            prop: 'numberOfTransfersOut',
            rules: [
                {
                    required: true,
                    message: '请输入转出笔数',
                    trigger: 'blur',
                },
            ],
        },
        {
            label: '单笔平均转出金额',
            prop: 'averageTransferAmount',
            rules: [
                {
                    required: true,
                    message: '请输入单笔平均转出金额',
                    trigger: 'blur',
                },
            ],
        },
        {
            label: '账户静默时间',
            prop: 'silenceTime',
            rules: [
                {
                    required: true,
                    message: '请输入账户静默时间',
                    trigger: 'blur',
                },
            ],
        },
        {
            label: '命中规则',
            prop: 'hitRules',
            rules: [
                {
                    required: true,
                    message: '请输入命中规则',
                    trigger: 'blur',
                },
            ],
        },
        {
            label: '总分值',
            prop: 'totalScore',
            rules: [
                {
                    required: true,
                    message: '请输入总分值',
                    trigger: 'blur',
                },
            ],
        },
        {
            label: '备注',
            prop: 'remarks',
        },
        {
            label: '是否成功',
            prop: 'isSuccess',
            addDisplay: false,
            slot: true
        },
        {
            label: '失败原因',
            prop: 'respDesc',
            addDisplay: false,
            slot: true
        },
    ],
};
