import validate from '@/utils/validate';
import { Footer } from 'element-ui';
export default {
  index: false,
  indexLabel: '序号',
  rowKey: 'openacc_id',
  // reserveSelection: true,
  // selection: true,
  align: 'center',
  card: true,
  menuAlign: 'center',
  emptyBtnIcon: 'el-icon-refresh',
  searchMenuSpan: 6,
  searchMenuPosition: 'left',
  addTitle: '存量卡数据推送记录查询',
  viewTitle: '查看卡信息',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  searchBtn: false,
  refreshBtn: false,
  emptyBtn: false,
  labelPosition: 'right',
  labelWidth: 120,
  searchMenuSpan: 100,
  tip: true,
  columnBtn: true,
  saveBtnText: '确定',
  // excelBtn: true,
  column: [
    {
      label: 'ID',
      prop: 'cId',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '交易跟踪号',
      prop: 'transSerialNumber',
      //查询设置：
      search: true, //查询条件
      searchLabelWidth: 120, //左边长度
      searchSpan: 120, //右边长度
      addDisplay: false,
      editDisplay: false,
      span: 24,
    },
    {
      label: '推送条数',
      prop: 'pushNum',
      addDisplay: false,
      editDisplay: false,
      span: 24,
    },
    {
          label: '运行状态',
          prop: 'runningStatus',
          type: "select",
          dicData: [],
          slot: true,
          width: 150,
          showColumn: false,
          addDisplay: false,
          editDisplay: true,
        },
/*    {
      label: '推送状态',
      prop: 'sendStatus',
      type: "select",
      dicData: [],
      slot: true,
      width: 150,
      showColumn: false,
      addDisplay: false,
      editDisplay: true,
    }, */
    {
      label: '备注',
      prop: 'remarks',
      addDisplay: true,
      editDisplay: true,
      span: 24,
    },
  /* {
      label: '推送人',
      prop: 'createBy',
      addDisplay: false,
      editDisplay: false,
    }, */
    {
       label: '推送时间',
       prop: 'createTime',
       addDisplay: false,
       editDisplay: false,
     },
    /* {
        label: '更新人',
        prop: 'updateBy',
        addDisplay: false,
        editDisplay: false,
      }, */
    {
       label: '更新时间',
       prop: 'updateTime',
       addDisplay: false,
       editDisplay: false,
     },
  ],
};
