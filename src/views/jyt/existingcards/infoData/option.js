import validate from '@/utils/validate';
import { Footer } from 'element-ui';
export default {
  index: false,
  indexLabel: '序号',
  rowKey: 'openacc_id',
  reserveSelection: true,
  selection: true,
  align: 'center',
  card: true,
  menuAlign: 'center',
  emptyBtnIcon: 'el-icon-refresh',
  searchMenuSpan: 6,
  searchMenuPosition: 'left',
  addTitle: '存量卡查询',
  viewTitle: '查看卡信息',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  searchBtn: false,
  refreshBtn: false,
  emptyBtn: false,
  labelPosition: 'right',
  labelWidth: 120,
  searchMenuSpan: 100,
  tip: true,
  columnBtn: true,
  saveBtnText: '确定',
  // excelBtn: true,
  column: [
    {
      label: 'ID',
      prop: 'existId',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '交易跟踪号',
      prop: 'transSerialNumber',
      //查询设置：
      search: true, //查询条件
      searchLabelWidth: 120, //左边长度
      searchSpan: 120, //右边长度
      addDisplay: false,
      editDisplay: false,
      span: 24,
    },
    {
      label: '客户姓名',
      prop: 'accountSubjectName',
      //查询设置：
      search: true, //查询条件
      searchLabelWidth: 120, //左边长度
      searchSpan: 120, //右边长度
      rules: [
      {
        required: true,
        message: '请输入客户姓名',
        trigger: 'blur',
      }, ],
      addDisplay: true,
      editDisplay: true,
      editDisabled: true, //不可修改
      span: 24,
    },
    {
      label: '身份证号码',
      prop: 'accountCredentialNumber',
      //查询设置：
      search: true, //查询条件
      searchLabelWidth: 120, //左边长度
      searchSpan: 120, //右边长度
      rules: [
      {
        required: true,
        message: '请输入客户身份证号码',
        trigger: 'blur',
      }, {
        validator: validate.checkIdentity,
        trigger: 'blur'
      }],
      addDisplay: true,
      editDisplay: true,
      editDisabled: true, //不可修改
      span: 24,
    },
    {
      label: '预留手机号',
      prop: 'delayPhoneNumber',
      rules: [
      {
        required: false,
        message: '请输入手机号',
        trigger: 'blur',
      }, {
        validator: validate.telNumber,
        trigger: 'blur'
      }],
      addDisplay: true,
      editDisplay: true,
      span: 24,
    },
    {
      label: '所属银行',
      prop: 'bankName',
      rules: [
      {
        required: true,
        message: '请输入所属银行',
        trigger: 'blur',
      }, ],
      addDisplay: true,
      editDisplay: true,
      span: 24,
    },
    {
      label: '银行开户网点编码',
      prop: 'depositBankBranch',
      rules: [
      {
        required: true,
        message: '请输入银行开户网点编码',
        trigger: 'blur',
      }, ],
      addDisplay: true,
      editDisplay: true,
      span: 24,
    },
    {
      label: '账户类型',
      prop: 'stockAccount',
      type: "select",
      dicData: [],
      slot: true,
      width: 150,
      showColumn: false,
      addDisplay: false,
      editDisplay: true,
    },
    {
      label: '账户号码',
      prop: 'accountNumber',
      addDisplay: false,
      editDisplay: true,
      span: 24,
    },
    {
      label: '风险预警',
      prop: 'riskCode',
      type: "select",
      dicData: [],
      slot: true,
      width: 150,
      showColumn: false,
      span: 24,
      addDisplay: false,
      editDisplay: true,
      editDisabled: true, //不可修改
    },
    {
      label: '备注',
      prop: 'remarks',
      addDisplay: true,
      editDisplay: true,
      span: 24,
    },

    {
      label: '数据日期',
      prop: 'summDate',
      type: "datetime",
      format: "yyyyMMdd",
      valueFormat: "yyyyMMdd",  // 存储格式
      //查询设置：
      search: true, //查询条件
      searchLabelWidth: 120, //左边长度
      searchSpan: 120, //右边长度
      addDisplay: false,
      editDisplay: true,
    },
    {
      label: '运行状态',
      prop: 'runningStatus',
      type: "select",
      dicData: [],
      slot: true,
      width: 150,
      showColumn: false,
      addDisplay: false,
      editDisplay: true,
    },

    {
       label: '创建时间',
       prop: 'createTime',
       hide: true,
       showColumn: false,
       addDisplay: false,
       editDisplay: false,
     },

    {
       label: '更新时间',
       prop: 'updateTime',
       hide: true,
       showColumn: false,
       addDisplay: false,
       editDisplay: false,
     },
  ],
};
