<!--存量卡查询列表-明细表-->
<template>
  <div class="app-container">
    <yo-table @keyup.enter.native="handleQuery" v-loading="loading" :option="option" :data="data" ref="crud"
      :page.sync="page" :search.sync="search" :before-open="beforeOpen" @on-load="onLoad" @search-change="searchChange"
      @row-update="rowUpdate" @row-save="rowSave" @refresh-change="refresh" v-model="formParent"
      @selection-change="handleSelectionChange">
      <template slot="searchMenu">
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <el-button size="mini" type="primary" icon="el-icon-search" @click.stop="handleQuery">
          查询
        </el-button>
        <el-button size="mini" icon="el-icon-refresh" @click.stop="resetQuery()">
          重置
        </el-button>
      </template>


      <!-- 自定义显示隐藏列 -->
     <!--客户名称  -->
     <template slot="accountSubjectName" slot-scope="scope">
        <div>
          <span>{{scope.row.accountSubjectName}}</span>
          <el-button v-if="scope.row.accountSubjectName" type="text" icon="el-icon-view"
            @click="viewSensitive(scope.row,'accountSubjectName')"></el-button>
        </div>
      </template>
      <!--身份证号码  -->
      <template slot="accountCredentialNumber" slot-scope="scope">
        <div>
          <span>{{scope.row.accountCredentialNumber}}</span>
          <el-button v-if="scope.row.accountCredentialNumber" type="text" icon="el-icon-view"
            @click="viewSensitive(scope.row,'accountCredentialNumber')"></el-button>
        </div>
      </template>
      <!--手机号  -->
      <template slot="delayPhoneNumber" slot-scope="scope">
        <div>
          <span>{{scope.row.delayPhoneNumber}}</span>
          <el-button v-if="scope.row.delayPhoneNumber" type="text" icon="el-icon-view"
            @click="viewSensitive(scope.row,'delayPhoneNumber')"></el-button>
        </div>
      </template>
      <!--账户号  -->
      <template slot="accountNumber" slot-scope="scope">
        <div>
          <span>{{scope.row.accountNumber}}</span>
          <el-button v-if="scope.row.accountNumber" type="text" icon="el-icon-view"
            @click="viewSensitive(scope.row,'accountNumber')"></el-button>
        </div>
      </template>

      <!-- 自定义左侧操作栏 -->
      <template slot="menuLeft">
        <el-button type="primary" plain icon="el-icon-edit" size="mini" :disabled="multiple"  @click="pushCardsData">数据推送</el-button>
      </template>

      <!-- 操作列 -->
      <template slot-scope="{ row, size, type ,index}" slot="menu">
        <el-button size="mini" :type="type" icon="el-icon-info" @click.stop="handleView(row)">
          详细信息
        </el-button>
      </template>
    </yo-table>
  </div>
</template>
<script>
import {
  listData,
  pushData,
  viewDtl,
  sendExistCard,
  sendExistCards,
  querySensitive
} from '@/api/jyt/existcards';
import openaccOption from './infoData/option.js';
import Cookies from "js-cookie";

export default {
  name: 'existcards',
  components: {},
  dicts: [
    "jyt_risk_code",
    "jyt_stock_account",
    "jyt_existcard_runstatus"
  ],
  data () {
    return {
      title: '',
      dialogType: '',
      loading: true,
      statusFlag: false,
      buttonAttr: '0',
      formParent: {},
      search: {},
      exportSearch: {},
      ids:{},
      idsList:[],
      page: {
        pageSize: 10,
        pageNum: 1,
      },
      treeprops: {
        label: 'name',
        children: 'children',
      },
      deptName: '',
      instName: '',
      data: [],
      option: openaccOption,
      dialogVisible: false,
      // 非多个禁用
      multiple: true,
      labelPosition: 'right',
      formLabelAlign: {
        name: '',
        idCard: '',
      },
    };
  },
  watch: {
    'search.updateTimeBox': {
      handler (val) {
        if (val) {
          this.search.updateTimeStart = val[0];
          this.search.updateTimeEnd = val[1];
        }
      },
      deep: true,
    },
  },
  created () {
    /** *添加字典项数据*/
    this.updateDictData(
      this.option.column,
      "riskCode",
      this.dict.type["jyt_risk_code"]
    );
    this.updateDictData(
      this.option.column,
      "stockAccount",
      this.dict.type["jyt_stock_account"]
    );
    this.updateDictData(
      this.option.column,
      "runningStatus",
      this.dict.type["jyt_existcard_runstatus"]
    );

    this.page.currentPage = 1;
    this.getList();
  },
  methods: {
    // 首次加载调用此方法
    onLoad () {
      this.getList();
    },
    // 弹窗打开
    beforeOpen (done, type) {
      console.log('type:', type);
      done();
    },
    refreshList () {
      this.handleQuery();
    },
    // 搜索按钮
    searchChange (params, done) {
      this.handleQuery();
      setTimeout(() => {
        done();
      }, 1000);
    },

    // 修改表单保存
    rowUpdate(form, index, done, loading) {
       /* if (form.idCard == identity) {
            delete form.idCard;
        }
        if (form.phoneNumber == telNumber) {
            delete form.phoneNumber;
        }
        if (form.email == emailid) {
            delete form.email;
        }
        updateUser(form).then(response => {
            done();
            if (response.code == 0) {
                this.$message.success(response.message);
                this.handleQuery();
            }
        }); */
    },

    // 更新字典数据
    updateDictData (option, key, data) {
      let column = this.findObject(option, key);
      column.dicData = data;
    },

    // 新增表单保存-存量卡查询
    async rowSave (form, done, loading) {
      console.log("存量卡查询参数==>" + JSON.stringify(form))
      pushQueryData(form).then(res => {
        console.log("存量卡查询返回的结果--" + JSON.stringify(res))
        if (res.code == '0') {
          this.$message.success(res.message);
          this.handleQuery();
          done();
        } else {
          this.$message.error(res.message);
          this.loading = false;
        }
      });
    },
    // 刷新按钮
    refresh () {
      this.handleQuery();
    },
    /** 查询列表 */
    async getList (query) {
      const params = { ...query, ...this.page };
      console.log('查询参数：', params);
      this.loading = true;
      const { code, data } = await listData(params);
      if (code == 0) {
        this.data = data.list;
        this.page.total = data.total;
        this.loading = false;
      }
    },

    /** 搜索按钮操作 */
    handleQuery () {
      this.page.currentPage = 1;
      this.getList(this.search);
      this.exportSearch = this.search;
    },
    // /** 重置按钮操作 */
    resetQuery () {
      this.$refs.crud.searchReset();
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange (selection) {
      this.statusFlag = true;
      let row = selection; //查找到的所有进行修改
      if (row != undefined) {
        this.statusFlag = false;
      }
      this.ids = selection.map(item => item.existId).join(',');
      this.idsList = selection.map(item => item.existId);
      this.multiple = !(selection.length > 0 && !this.statusFlag);
    },

    /** 新增按钮操作 */
    handleAdd () {
      this.$refs.crud.rowAdd();
    },


    //查看
    async handleView (row) {
      this.$refs.crud.rowView(row);
    },


    /** 批量推送操作 */
    pushCardsData (row) {
     console.log("推送操作的时候数组：" + this.idsList);
      const h = this.$createElement;
      this.$msgbox({
          title: '提示',
          message: h('p', null, [
            h('p', { style: 'word-break: break-all' },
              '是否推送选中的' + this.idsList.length + '条数据？'
            ),
          ]),
          showCancelButton: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        })
        .then(async () => {
          let res;
          this.title = "存量卡数据推送";
          if (row.existId != undefined) {
            res = await sendExistCard({ id: row.existId });
          } else {
            if (
              this.ids != null &&
              this.ids != '' &&
              this.ids != undefined
            ) {
              res = await sendExistCards(this.idsList);
            }
          }
          console.log("数据推送结果------" + JSON.stringify(res));
          if (res.code == 0) {
            this.$message.success(res.message);
            this.$refs.crud.toggleSelection();
          } else {
            this.$message.error(res.message);
          }
          this.getList(this.search);
        })
        .catch(() => {
          console.log("数据推送异常返回------");
        });
    },

    //推送数据
    async handleSendList (id, sendFen) {
      let res = await sendFen(id);
      if (res.code == 0) {
        this.$message.success(res.message);
        this.$refs.crud.toggleSelection();
        this.getList();
      }
    },

    /**敏感信息显隐**/
    viewSensitive (data, type) {
      let sensitiveStatus = data[type].includes("*") ? "1" : "2";
      querySensitive(data.existId, sensitiveStatus).then(response => {
        const res = response.data;
        console.log("存量卡敏感信息响应结果：" + JSON.stringify(res));
        this.data.forEach(item => {
          if (item.existId == data.existId) {
            item[type] = res[type];
          }
        });
      });
    },
    // end

  },
};
</script>

<style scoped>
.icon-list {
  height: 220px;
  overflow-y: scroll;
}
</style>
