<!--存量卡数据推送查询表-总控表-->
<template>
  <div class="app-container">
    <!--  @row-update="rowUpdate" @row-save="rowSave"  -->
    <yo-table @keyup.enter.native="handleQuery" v-loading="loading" :option="option" :data="data" ref="crud"
      :page.sync="page" :search.sync="search" :before-open="beforeOpen" @on-load="onLoad" @search-change="searchChange"
      @refresh-change="refresh" v-model="formParent"
      @selection-change="handleSelectionChange">
      <template slot="searchMenu">
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <el-button size="mini" type="primary" icon="el-icon-search" @click.stop="handleQuery">
          查询
        </el-button>
        <el-button size="mini" icon="el-icon-refresh" @click.stop="resetQuery()">
          重置
        </el-button>
      </template>

      <!-- 自定义显示隐藏列 -->
    <!-- <template slot="accountSubjectName" slot-scope="scope">
        <div>
          <span>{{scope.row.accountSubjectName}}</span>
          <el-button v-if="scope.row.accountSubjectName" type="text" icon="el-icon-view"
            @click="viewSensitive(scope.row,'accountSubjectName')"></el-button>
        </div>
      </template>
      <template slot="accountCredentialNumber" slot-scope="scope">
        <div>
          <span>{{scope.row.accountCredentialNumber}}</span>
          <el-button v-if="scope.row.accountCredentialNumber" type="text" icon="el-icon-view"
            @click="viewSensitive(scope.row,'accountCredentialNumber')"></el-button>
        </div>
      </template> -->

      <!-- 自定义左侧操作栏 -->
    <!--  <template slot="menuLeft">
        <el-button type="primary" icon="el-icon-edit" size="mini" @click.stop="handleAdd">重新推送</el-button>
      </template> -->

      <!-- 操作列 -->
      <template slot-scope="{ row, size, type ,index}" slot="menu" >
        <el-button size="mini" :type="type" icon="el-icon-edit" @click.stop="pushData(row,index)">
          重新推送
        </el-button>
        <el-button size="mini" :type="type" icon="el-icon-search" @click.stop="getStatus(row,index)">
          状态查询
        </el-button>
        <el-button size="mini" :type="type" icon="el-icon-search" @click.stop="getResult(row,index)">
          结果获取
        </el-button>
      </template>
    </yo-table>
  </div>
</template>
<script>
import {
  ctrlListData,
  pushExistCardsBySerialno,
  getStatus,
  getResult
} from '@/api/jyt/existcards';
import openaccOption from './infoData/pushOption.js';
import Cookies from "js-cookie";

export default {
  name: 'existcardsctrl',
  components: {},
  dicts: [
    "jyt_existcard_runstatus",
  ],
  data () {
    return {
      title: '',
      dialogType: '',
      loading: true,
      statusFlag: false,
      buttonAttr: '0',
      formParent: {},
      search: {},
      exportSearch: {},
      page: {
        pageSize: 10,
        pageNum: 1,
      },
      treeprops: {
        label: 'name',
        children: 'children',
      },
      deptName: '',
      instName: '',
      data: [],
      option: openaccOption,
      dialogVisible: false,
      // 非多个禁用
      multiple: true,
      labelPosition: 'right',
      formLabelAlign: {
        name: '',
        idCard: '',
      },
    };
  },
  watch: {
    'search.updateTimeBox': {
      handler (val) {
        if (val) {
          this.search.updateTimeStart = val[0];
          this.search.updateTimeEnd = val[1];
        }
      },
      deep: true,
    },
  },
  created () {
    /** *添加字典项数据*/
    this.updateDictData(
      this.option.column,
      "runningStatus",
      this.dict.type["jyt_existcard_runstatus"]
    );
    this.page.currentPage = 1;
    this.getList();
  },
  methods: {
    // 首次加载调用此方法
    onLoad () {
      this.getList();
    },
    // 弹窗打开
    beforeOpen (done, type) {
      console.log('type:', type);
      done();
    },
    refreshList () {
      this.handleQuery();
    },
    // 搜索按钮
    searchChange (params, done) {
      this.handleQuery();
      setTimeout(() => {
        done();
      }, 1000);
    },

    // 更新字典数据
    updateDictData (option, key, data) {
      let column = this.findObject(option, key);
      column.dicData = data;
    },

    // 新增表单保存-存量卡查询
    async rowSave (form, done, loading) {
      console.log("存量卡查询参数==>" + JSON.stringify(form))
      pushQueryData(form).then(res => {
        console.log("存量卡查询返回的结果--" + JSON.stringify(res))
        if (res.code == '0') {
          this.$message.success(res.message);
          this.handleQuery();
          done();
        } else {
          this.$message.error(res.message);
          this.loading = false;
        }
      });
    },
    // 刷新按钮
    refresh () {
      this.handleQuery();
    },
    /** 查询列表 */
    async getList (query) {
      const params = { ...query, ...this.page };
      console.log('查询参数：', params);
      this.loading = true;
      const { code, data } = await ctrlListData(params);
      if (code == 0) {
        this.data = data.list;
        this.page.total = data.total;
        this.loading = false;
      }
    },

    /** 搜索按钮操作 */
    handleQuery () {
      this.page.currentPage = 1;
      this.getList(this.search);
      this.exportSearch = this.search;
    },
    // /** 重置按钮操作 */
    resetQuery () {
      this.$refs.crud.searchReset();
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange (selection) {
      this.statusFlag = true;
      let row = selection; //查找到的所有进行修改
      if (row != undefined) {
        this.statusFlag = false;
      }
      this.ids = selection.map(item => item.id).join(',');
      this.multiple = !(selection.length > 0 && !this.statusFlag);
    },



    // 推送数据，只能选1条
    async pushData (row, index) {
      const serialno = row.transSerialNumber;
      const response = await pushExistCardsBySerialno(row);
      if (response.code == 0) {
        this.$message.success("数据推送成功！");
        this.handleQuery();
      } else {
        this.$message.error ("数据推送失败！" + response.message);
      }
    },

    //查看
    async handleView (row) {
      this.$refs.crud.rowView(row);
    },

    //结果获取
    async getResult (row, index) {
      const response = await getResult(row);
      console.log("获取结果返回响应信息：" + response);
      if (response.code == 0) {
        this.$message.success(response.message);
        this.handleQuery();
      } else {
         this.$message.error(response.message);
      }
    },

    //查询状态
    async getStatus (row, index) {
      const response = await getStatus(row);
         console.log("状态查询返回响应信息：" + response);
      if (response.code == 0) {
        this.$message.success(response.message);
        this.handleQuery();
      } else {
         this.$message.error(response.message);
      }
    },


    /**敏感信息显隐**/
    viewSensitive (data, type) {
      let sensitiveStatus = data[type].includes("*") ? "1" : "2";
      querySensitive(data.id, sensitiveStatus).then(response => {
        const res = response.data;
        console.log("监管账户敏感信息响应结果：" + res);
        this.data.forEach(item => {
          if (item.id == data.id) {
            item[type] = res[type];
          }
        });
      });
    },
    // end

  },
};
</script>

<style scoped>
.icon-list {
  height: 220px;
  overflow-y: scroll;
}
</style>
