<template>
  <!-- LDAP登录 -->
  <el-form ref="loginForm" :model="loginForm" :rules="loginRules">
    <el-form-item prop="username">
      <el-input
        v-model="loginForm.username"
        type="text"
        auto-complete="off"
        placeholder="用户名"
      >
        <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />
      </el-input>
    </el-form-item>
    <el-form-item prop="password" v-if="pwdOnOff">
      <el-input
        v-model="loginForm.password"
        type="password"
        auto-complete="off"
        placeholder="密码"
        @keyup.enter.native="handleLogin"
        show-password
      >
        <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
      </el-input>
    </el-form-item>
    <el-form-item prop="imageCaptcha" v-if="captchaOnOff">
      <el-input
        v-model="loginForm.imageCaptcha"
        auto-complete="off"
        placeholder="图形验证码"
        style="width: 63%"
        @keyup.enter.native="handleLogin"
      >
        <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
      </el-input>
      <div class="login-code">
        <img :src="codeUrl" @click="getCode" class="login-code-img" />
      </div>
    </el-form-item>
    <el-form-item prop="captcha" v-if="msgOnOff">
      <el-input
        v-model="loginForm.captcha"
        auto-complete="off"
        placeholder="短信验证码"
        style="width: 63%"
        @keyup.enter.native="handleLogin"
      >
        <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
      </el-input>
      <div class="login-code">
        <el-button
          style="width:100%;color:#666;height:38px;"
          :disabled="sendDisabled"
          @click="onSend"
        >{{text}}</el-button>
      </div>
    </el-form-item>
    <el-form-item style="width:100%;">
      <el-button
        :loading="loading"
        size="medium"
        type="primary"
        style="width:100%;"
        @click.native.prevent="handleLogin"
      >
        <span v-if="!loading">登 录</span>
        <span v-else>登 录 中...</span>
      </el-button>
    </el-form-item>
  </el-form>
</template>

<script>
const INIT_TEXT = "获取验证码";
const TIP_TEXT = "{{time}}s后重获取";
import {
  getCodeImg,
  getCodeSms,
  getCodePhoneSms,
  loginType,
  getuaasLogin
} from "@/api/login";
import { encrypt } from "@/utils/smencrypt.js";
import local from "@/plugins/cache";

export default {
  props: {
    // 密码显示开关
    pwdOnOff: {
      type: Boolean,
      default: false
    },
    // 图片验证码显示开关
    captchaOnOff: {
      type: Boolean,
      default: false
    },
    //短信验证码显示开关
    msgOnOff: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      codeUrl: "",
      loginForm: {
        username: "",
        password: "",
        captcha: "",
        imageCaptcha: "",
        imageCaptchaKey: ""
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "请输入您的账号" }
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入您的密码" }
        ],
        imageCaptcha: [{ required: true, trigger: "change", message: "请输入图片验证码" }],
        captcha: [{ required: true, trigger: "blur", message: "请输入验证码" }]
      },
      loading: false,
      redirect: undefined,
      text: "",
      // 动态令牌开关
      dynamicToken: false,
      check: null,
      text: "",
      nowtime: "",
      countDownTime: 60,
      dynamicPassword: ""
    };
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true
    }
  },
  computed: {
    sendDisabled() {
      return !this.validatenull(this.check);
    }
  },
  created() {
    this.getCode();
    let sendEndTime = local.local.get("startTimePhonePwd");
    if (sendEndTime) {
      this.countDown();
    } else {
      this.text = INIT_TEXT;
    }
  },
  methods: {
    getCodeSms() {
      if (!this.loginForm.username) {
        return;
      }
      this.dynamicPassword = "";
      const data = {
        username: this.loginForm.username
      };
      getCodeSms(data).then(res => {
        if (res.data.phone) {
          getCodePhoneSms(res.data.phone).then(response => {
            if (response.message) {
              this.dynamicPassword = response.message;
            }
          });
        }
      });
    },
    onSend() {
      if (this.sendDisabled) return;
      this.getCodeSms();
      this.countDown();
    },
    getCode() {
      if (!this.captchaOnOff) return;
      getCodeImg().then(res => {
        this.codeUrl = "data:image/gif;base64," + res.data.imageCaptchaBase64;
        this.loginForm.imageCaptchaKey = res.data.imageCaptchaKey;
      });
    },
    countDown() {
      let startTime = local.local.get("startTimePhonePwd");
      let nowTime = new Date().getTime();
      if (startTime) {
        let surplus =
          this.countDownTime - parseInt((nowTime - startTime) / 1000, 10);
        this.countDownTime = surplus <= 0 ? 0 : surplus;
      } else {
        this.countDownTime = 60;
        local.local.set("startTimePhonePwd", nowTime);
      }
      this.check = setInterval(() => {
        this.countDownTime--;
        this.text = TIP_TEXT.replace("{{time}}", this.countDownTime);
        if (this.countDownTime <= 0) {
          local.local.remove("startTimePhonePwd");
          this.text = INIT_TEXT;
          clearInterval(this.check);
          this.check = null;
          this.countDownTime = 60;
        } else {
          this.text = TIP_TEXT.replace("{{time}}", this.countDownTime);
        }
      }, 1000);
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true;
          const { username, password, captcha, imageCaptcha, imageCaptchaKey,} = this.loginForm;
          const submitData = {
            username,
            password: encrypt(password),
            // phone,
            captcha,
            imageCaptcha,
            imageCaptchaKey
          };
          this.dynamicToken = false;
          this.$store
            .dispatch("Login", submitData)
            .then(() => {
              // 开启不输入手机号动态验证时
              if (this.dynamicToken) {
                this.$prompt("请输入动态验证码", "提示", {
                  confirmButtonText: "确定",
                  cancelButtonText: "取消",
                  closeOnClickModal: false
                })
                  .then(({ value }) => {
                    this.$router
                      .push({ path: this.redirect || "/" })
                      .catch(() => {});
                  })
                  .catch(() => {
                    this.loading = false;
                    this.dynamicToken = false;
                  });
                this.dynamicToken = false;
              } else {
                this.$router
                  .push({ path: this.redirect || "/" })
                  .catch(() => {});
              }
            })
            .catch(() => {
              this.loading = false;
              // local.local.remove("startTimePhonePwd");
              // this.text = INIT_TEXT;
              // clearInterval(this.check);
              // this.check = null;
              // this.countDownTime = 60;
              this.getCode();
            });
        }
      });
    }
  }
};
</script>