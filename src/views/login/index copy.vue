<template>
  <div class="login">
    <div class="title-logo">
      <img width="100%" :src="titleLogo" />
    </div>
    <div class="slogon">
      <img width="100%" :src="slogon">
    </div>
    <div class="login-form">
      <h3 class="title">厦门分行新一代综合管理平台</h3>
      <el-form ref="loginForm" :model="loginForm" :rules="loginRules">
        <el-form-item prop="username">
          <el-input v-model="loginForm.username" type="text" auto-complete="off" placeholder="用户名/身份证号码/手机号/电子邮箱">
            <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input v-model="loginForm.password" type="password" auto-complete="off" placeholder="密码"
            @keyup.enter.native="handleLogin" show-password>
            <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
          </el-input>
        </el-form-item>
        <el-form-item prop="imageCaptcha" v-if="captchaOnOff">
          <el-input v-model="loginForm.imageCaptcha" auto-complete="off" placeholder="图形验证码" style="width: 63%"
            @keyup.enter.native="handleLogin">
            <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
          </el-input>
          <div class="login-code">
            <img :src="codeUrl" @click="getCode" class="login-code-img" />
          </div>
        </el-form-item>
        <el-form-item prop="captcha" v-if="captchaOnOff">
          <el-input v-model="loginForm.captcha" auto-complete="off" placeholder="短信验证码" style="width: 63%"
            @keyup.enter.native="handleLogin">
            <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
          </el-input>
          <div class="login-code">
            <el-button style="width:100%;color:#666;height:38px;" :disabled="sendDisabled" @click="onSend">
              {{text}}</el-button>
          </div>
        </el-form-item>
        <el-form-item style="width:100%;">
          <el-button :loading="loading" size="medium" type="primary" style="width:100%;"
            @click.native.prevent="handleLogin">
            <span v-if="!loading">登 录</span>
            <span v-else>登 录 中...</span>
          </el-button>
        </el-form-item>
      </el-form>

    </div>

    <!--  底部  -->
    <div class="el-login-footer">
      <span>版权所有© 2018-2022 中国邮政储蓄银行</span>
    </div>
  </div>
</template>

<script>
  const INIT_TEXT = '获取验证码'
  const TIP_TEXT = '{{time}}s后重获取'
  import titleLogo from '@/assets/logo/logo3.png'
  import slogon from '@/assets/images/title-text7.png'
  import { getCodeImg, getCodeSms, getCodePhoneSms } from "@/api/login";
  import local from '@/plugins/cache'
  // import Cookies from "js-cookie";
  import { encrypt } from '@/utils/smencrypt.js'

  export default {
    name: "Login",
    data() {
      var validateCaptcha = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请输入短信验证码'));
        } else {
          callback();
        }
      }
      return {
        titleLogo: titleLogo,
        slogon: slogon,
        codeUrl: "",
        loginForm: {
          username: "",
          password: "",
          captcha: '',
          imageCaptcha: "",
          imageCaptchaKey: "",
          dynamicPassword: ''
        },
        loginRules: {
          username: [
            { required: true, trigger: "blur", message: "请输入您的账号" }
          ],
          password: [
            { required: true, trigger: "blur", message: "请输入您的密码" }
          ],
          imageCaptcha: [{ required: true, trigger: "change", message: "请输入验证码" }],
          captcha: [
            { require: true, trigger: 'blur', message: '请输入短信验证码' },
            { validator: validateCaptcha, trigger: 'blur' }
          ]
        },
        loading: false,
        // 验证码开关
        captchaOnOff: true,
        // 注册开关
        register: false,
        redirect: undefined,
        text: '',
        // 动态令牌开关
        dynamicToken: false,
        check: null,
        text: '',
        nowtime: '',
        countDownTime: 60,
        dynamicPassword: ''
      };
    },
    computed: {
      sendDisabled() {
        return !this.validatenull(this.check)
      },
    },
    watch: {
      $route: {
        handler: function (route) {
          this.redirect = route.query && route.query.redirect;
        },
        immediate: true
      }
    },
    created() {
      this.getCode();
      let sendEndTime = local.local.get('startTimePhonePwd')
      if (sendEndTime) {
        this.countDown()
      } else {
        this.text = INIT_TEXT;
      }

    },
    methods: {
      getCode() {
        getCodeImg().then(res => {
          this.codeUrl = "data:image/gif;base64," + res.data.imageCaptchaBase64;
          this.loginForm.imageCaptchaKey = res.data.imageCaptchaKey;
        });
      },
      getCodeSms() {
        if (!this.loginForm.username) {
          return
        }
        this.dynamicPassword = ''
        const data = {
          username: this.loginForm.username
        }
        getCodeSms(data).then(res => {
          if (res.data.phone) {
            getCodePhoneSms(res.data.phone).then((response) => {
              if (response.message) {
                this.dynamicPassword = response.message
              }
            })
          }
        });
      },
      onSend() {

        if (this.sendDisabled) return
        this.getCodeSms()
        this.countDown()
      },
      countDown() {
        let startTime = local.local.get('startTimePhonePwd')
        let nowTime = new Date().getTime()
        if (startTime) {
          let surplus = this.countDownTime - parseInt((nowTime - startTime) / 1000, 10)
          this.countDownTime = surplus <= 0 ? 0 : surplus
        } else {
          this.countDownTime = 60
          local.local.set('startTimePhonePwd', nowTime)
        }
        this.check = setInterval(() => {
          this.countDownTime--
          this.text = TIP_TEXT.replace('{{time}}', this.countDownTime);
          if (this.countDownTime <= 0) {
            local.local.remove('startTimePhonePwd')
            this.text = INIT_TEXT;
            clearInterval(this.check);
            this.check = null;
            this.countDownTime = 60
          } else {
            this.text = TIP_TEXT.replace('{{time}}', this.countDownTime);
          }
        }, 1000)
      },

      handleLogin() {
        this.$refs.loginForm.validate(valid => {
          if (valid) {
            this.loading = true;
            const { username, password, captcha, imageCaptcha, imageCaptchaKey, } = this.loginForm
            const submitData = {
              username,
              password: encrypt(password),
              // phone,
              captcha,
              imageCaptcha,
              imageCaptchaKey
            }
            this.dynamicToken = false
            this.$store.dispatch("Login", submitData).then(() => {
              // 开启不输入手机号动态验证时
              if (this.dynamicToken) {
                this.$prompt('请输入动态验证码', "提示", {
                  confirmButtonText: "确定",
                  cancelButtonText: "取消",
                  closeOnClickModal: false,
                }).then(({ value }) => {
                  this.$router.push({ path: this.redirect || "/" }).catch(() => { });
                }).catch(() => {
                  this.loading = false;
                  this.dynamicToken = false
                });
                this.dynamicToken = false
              } else {
                this.$router.push({ path: this.redirect || "/" }).catch(() => { });
              }

            }).catch(() => {
              this.loading = false;
              if (this.captchaOnOff) {
                local.local.remove('startTimePhonePwd')
                this.text = INIT_TEXT;
                clearInterval(this.check);
                this.check = null;
                this.countDownTime = 60
                this.getCode();
              }
            });
          }
        });
      }
    }
  };
</script>

<style rel="stylesheet/scss" lang="scss">
  .login {
    position: fixed;
    height: 100%;
    width: 100%;
    background-image: url('../../assets/images/bg-new1.png');
    background-size: cover;

    .title-logo {
      position: absolute;
      top: 30px;
      left: 30px;
      width: 300px;
    }

    .slogon {
      position: absolute;
      right: 0;
      top: 0;
      width: 80%;
      opacity: 0.8;
    }

    .login-form {
      position: absolute;
      width: 28%;
      z-index: 1;
      top: 40%;
      right: 8%;
      background: url('../../assets/images/bg-large.png') no-repeat 0 0;
      background-size: 100% 100%;
      padding: 25px;
      padding-bottom: 5px;

      .el-input {
        height: 38px;

        input {
          height: 38px;
        }
      }

      .input-icon {
        height: 39px;
        width: 14px;
        margin-left: 2px;
      }

      .title {
        margin: 0px auto 30px auto;
        text-align: center;
        color: #fff;
        opacity: 0.8;
      }
    }
  }

  .login-tip {
    font-size: 13px;
    text-align: center;
    color: #bfbfbf;
  }

  .login-code {
    width: 33%;
    height: 38px;
    float: right;

    img {
      cursor: pointer;
      vertical-align: middle;
    }
  }

  .el-login-footer {
    height: 40px;
    line-height: 40px;
    position: fixed;
    bottom: 0;
    width: 100%;
    text-align: center;
    color: #fff;
    font-family: Arial;
    font-size: 12px;
    letter-spacing: 1px;
  }

  .login-code-img {
    width: 100%;
    height: 38px;
  }
</style>
