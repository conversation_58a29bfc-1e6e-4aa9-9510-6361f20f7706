<template>
    <div class="app-container">
        <yo-table
            @keyup.enter.native="handleQuery"
            v-loading="loading"
            :option="option"
            :data="data"
            ref="crud"
            :page.sync="page"
            :search.sync="search"
            :before-open="beforeOpen"
            @on-load="onLoad"
            @search-change="searchChange"
            @row-save="rowSave"
            @refresh-change="refresh"
            v-model="formParent"
            @selection-change="handleSelectionChange"
        >
            <template slot="searchMenu">
                <el-button
                    size="mini"
                    type="primary"
                    icon="el-icon-search"
                    @click.stop="handleQuery"
                >
                    查询
                </el-button>
                <el-button
                    size="mini"
                    icon="el-icon-refresh"
                    @click.stop="resetQuery()"
                >
                    重置
                </el-button>
            </template>
            <!-- 自定义列 -->
            <template slot="idCard" slot-scope="scope">
                <div>
                    <span>{{ scope.row.idCard }}</span>
                    <el-button
                        v-if="scope.row.idCard"
                        type="text"
                        icon="el-icon-view"
                        @click="viewDataInfo(scope.row, 'idCard')"
                    ></el-button>
                </div>
            </template>
            <template slot="createTimeSearch">
                <el-date-picker
                    v-model="search.createTimeBox"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="截止日期"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    :default-time="['00:00:00', '23:59:59']"
                ></el-date-picker>
            </template>
            <template slot="status" slot-scope="scope">
                <el-tag
                    :type="scope.row.status == '1' ? 'success' : ''"
                    size="mini"
                >
                    {{ scope.row.status == '1' ? '已授权' : '待授权' }}
                </el-tag>
            </template>

            <template slot="userStat" slot-scope="scope">
                <el-tag size="mini" effect="plain" :type="tagType(scope.row)">
                    {{ userStat(scope.row) }}
                </el-tag>
            </template>
            <template slot="menuRight">
                <el-button
                    v-if="isAuth"
                    type="primary"
                    plain
                    icon="el-icon-user"
                    size="mini"
                    :disabled="multiple"
                    @click="batchAuthorization"
                >
                    批量授权
                </el-button>
            </template>
            <!-- 自定义左侧操作栏 -->
            <template slot="menuLeft">
                <input
                    id="fileslist"
                    v-show="false"
                    type="file"
                    accept=".xls"
                    ref="fileRef"
                    @change="fileChange"
                />
                <el-button
                    type="warning"
                    icon="el-icon-upload2"
                    size="mini"
                    @click="handleImport"
                >
                    批量导入查询
                </el-button>

                <el-button
                    type="primary"
                    icon="el-icon-download"
                    size="mini"
                    @click="exportExample"
                >
                    下载模板
                </el-button>

                <el-button
                    type="success"
                    icon="el-icon-download"
                    size="mini"
                    @click="handleExport"
                >
                    导出
                </el-button>
            </template>
            <!-- 自定义操作栏 -->
            <template slot-scope="{ row, size, type }" slot="menu">
                <el-button
                    :size="size"
                    :type="type"
                    icon="el-icon-user"
                    @click.stop="batchAuthorization(row)"
                    v-if="row.status == '0' && isAuth"
                >
                    授权
                </el-button>

                <el-button
                    :size="size"
                    :type="type"
                    icon="el-icon-info"
                    @click.stop="showDetail(row)"
                >
                    详情
                </el-button>
            </template>
        </yo-table>

        <el-dialog :title="title" :visible.sync="dialogVisible" width="30%">
            <span slot="footer" class="dialog-footer">
                <el-button
                    style="margin-right: 15px"
                    type="primary"
                    @click="dialogQueren(dialogType)"
                    size="mini"
                >
                    确 定
                </el-button>
                <el-button @click="dialogVisible = false" size="mini">
                    取 消
                </el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import { downloadExample, getCurrentUserInfo } from '@/api/xsm_352';
import {
    add,
    list,
    authorization,
    importXsm,
    exportXsm,
    dataSensitive,
} from '@/api/xsm_468';
import xsmOption from './infoData/xsmOption.js';

export default {
    name: 'xsm',
    dicts: ['xsm_query_status', 'xsm_citizen_type', 'xsm_is_not'],
    components: {},
    data() {
        return {
            title: '',
            dialogType: '',
            loading: true,
            statusFlag: false,
            formParent: {},
            search: {},
            exportSearch: {},
            page: {
                pageSize: 10,
                pageNum: 1,
            },
            treeprops: {
                label: 'name',
                children: 'children',
            },
            deptName: '',
            instName: '',
            data: [],
            option: xsmOption,
            dialogVisible: false,
            // 非多个禁用
            multiple: true,
            labelPosition: 'right',
            formLabelAlign: {
                name: '',
                idCard: '',
            },
            authRoles: [], //当前登录人员的所有角色列表
            isAuth: false, //是否有授权查询的权限
        };
    },
    watch: {
        'search.createTimeBox': {
            handler(val) {
                if (val) {
                    this.search.createTimeStart = val[0];
                    this.search.createTimeEnd = val[1];
                }
            },
            deep: true,
        },
    },
    created() {
        /** *添加字典项数据*/
        this.updateDictData(
            this.option.column,
            'status',
            this.dict.type['xsm_query_status']
        );
        this.updateDictData(
            this.option.column,
            'sxbxzr',
            this.dict.type['xsm_is_not']
        );
        this.updateDictData(
            this.option.column,
            'xsm',
            this.dict.type['xsm_is_not']
        );
        this.updateDictData(
            this.option.column,
            'heimd',
            this.dict.type['xsm_is_not']
        );
        this.updateDictData(
            this.option.column,
            'mzjz',
            this.dict.type['xsm_is_not']
        );
        this.updateDictData(
            this.option.column,
            'tsyqc150',
            this.dict.type['xsm_is_not']
        );
        this.updateDictData(
            this.option.column,
            'age2260',
            this.dict.type['xsm_is_not']
        );
        this.updateDictData(
            this.option.column,
            'xzcf',
            this.dict.type['xsm_is_not']
        );
        this.updateDictData(
            this.option.column,
            'zdrq',
            this.dict.type['xsm_is_not']
        );
        this.updateDictData(
            this.option.column,
            'hongmd',
            this.dict.type['xsm_is_not']
        );
        this.updateDictData(
            this.option.column,
            'bzjj',
            this.dict.type['xsm_is_not']
        );
        this.updateDictData(
            this.option.column,
            'wmxw',
            this.dict.type['xsm_is_not']
        );
        this.updateDictData(
            this.option.column,
            'gjjdkyqqs6',
            this.dict.type['xsm_is_not']
        );
        this.updateDictData(
            this.option.column,
            'gjjdkdqyq',
            this.dict.type['xsm_is_not']
        );
        this.page.currentPage = 1;
        this.getList();
        this.getCurrentUserInfo();
    },
    methods: {
        // 首次加载调用此方法
        onLoad() {
            this.getList();
            // this.$refs['crud'].toggleSelection();
        },
        // 弹窗打开
        beforeOpen(done, type) {
            console.log('type:', type);
            // if (type == 'view') this.disabled = true;
            // else this.disabled = false;
            //  角色列表

            done();
        },
        refreshList() {
            this.handleQuery();
        },
        // 更新字典数据
        updateDictData(option, key, data) {
            let column = this.findObject(option, key);
            column.dicData = data;
        },
        // 搜索按钮
        searchChange(params, done) {
            this.handleQuery();
            setTimeout(() => {
                done();
            }, 1000);
        },
        // 新增表单保存
        async rowSave(form, done, loading) {
            const { name, idCard } = form;
            const submitData = {
                name: name,
                idCard: idCard,
            };
            add(submitData).then(res => {
                if (res.code == '0') {
                    this.$message.success(res.message);
                    this.handleQuery();
                    done();
                } else {
                    this.$message.error(res.message);
                    loading();
                }
            });
        },
        // 刷新按钮
        refresh() {
            this.handleQuery();
        },
        /** 查询列表 */
        async getList(query) {
            // let params = Object.assign({}, query, this.page);

            const params = { ...query, ...this.page };
            console.log('参数：', params);

            this.loading = true;
            const { code, data } = await list(params);
            if (code == 0) {
                this.data = data.list;
                this.page.total = data.total;
                this.loading = false;
            }
        },
        async getCurrentUserInfo() {
            const { data } = await getCurrentUserInfo();
            this.authRoles = data.authRoles;
            //是否有网点业务主管的角色
            this.isAuth = this.authRoles.includes('wdywzg');
        },

        /** 搜索按钮操作 */
        handleQuery() {
            this.page.currentPage = 1;
            this.getList(this.search);
            this.exportSearch = this.search;
        },
        // /** 重置按钮操作 */
        resetQuery() {
            this.$refs.crud.searchReset();
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.statusFlag = true;
            let row = selection.find(item => item.status == '1');
            console.log(row);
            if (row != undefined) {
                this.statusFlag = false;
            }
            console.log(this.statusFlag);
            this.ids = selection.map(item => item.id).join(',');
            this.multiple = selection.length > 0 && !this.statusFlag;
        },

        /** 新增按钮操作 */
        handleAdd() {
            this.deptName = '';
            this.instName = '';
            this.$refs.crud.rowAdd();
        },
        /**敏感信息显隐**/
        viewDataInfo(data, type) {
            let sensitiveStatus = data[type].includes('**') ? '1' : '2';
            dataSensitive(data.id, sensitiveStatus).then(response => {
                const res = response.data;
                this.data.forEach(item => {
                    if (item.id == data.id) {
                        item[type] = res[type];
                    }
                });
            });
        },

        /** 处理返回的流文件 */
        handleExportData(res) {
            console.log('res:', res);
            if (!res) return;
            let data = res.data;
            let filename = res.headers['content-disposition'].split('=')[1];
            let _filename = decodeURI(filename);
            const link = document.createElement('a');
            //创建 Blob对象 可以存储二进制文件
            let blob = new Blob([data], { type: 'application/x-excel' });
            link.style.display = 'none';
            link.href = URL.createObjectURL(blob);
            link.download = _filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        },
        /** 下载模板 */
        exportExample() {
            downloadExample().then(res => {
                this.handleExportData(res);
            });
        },
        /** 导出按钮操作 */
        handleExport() {
            const data = { userIds: this.ids, ...this.exportSearch };
            console.log('导出数据：', this.search);
            exportXsm(this.search).then(res => {
                console.log('导出的返回数据：', res);
                this.handleExportData(res);
            });
        },
        fileChange(event) {
            let file = event.target.files[0];
            let formData = new FormData();
            formData.append('file', file);
            importXsm(formData).then(res => {
                if (res.code == 0) {
                    this.$message.success(res.message);
                    this.handleQuery();
                }
            });
            // 清除文件，防止下次上传相同文件无反应
            event.target.value = null;
        },
        /** 导入按钮操作 */
        handleImport() {
            this.$refs.fileRef.dispatchEvent(new MouseEvent('click'));
            // this.$refs.fileRef.click();
        },
        //过滤列表数据
        filterData(data) {
            return data;
        },
        checkChange(data, checked, node) {
            if (checked) {
                this.$refs.tree.setCheckedNodes([data]);
            }
        },

        dialogQueren(type) {
            if (type == 'dept') {
                let arr = this.$refs.tree.getCheckedNodes();
                let deptName = arr.map(i => {
                    return i.name;
                });
                this.deptName = deptName.join(',');
                let deptIds = arr.map(i => {
                    return i.id;
                });
                this.formParent.deptIds = deptIds;
            } else if (type == 'auth') {
                //新市民查询
                this.queryList();
            } else {
                let arr = this.$refs.tree.getCheckedNodes();
                if (!arr.length) {
                    this.instName = '';
                    this.formParent.instId = '';
                } else {
                    this.instName = arr[0].name;
                    this.formParent.instId = arr[0].id;
                }
            }
            this.dialogVisible = false;
        },
        tagType(row) {
            switch (row.userStat) {
                case '01':
                    return 'success';
                case '02':
                    return 'danger';
                case '03':
                    return 'info';
                case '04':
                    return 'warning';
                default:
                    break;
            }
        },
        async batchAuthorization(row) {
            const h = this.$createElement;
            this.$msgbox({
                title: '提示',
                message: h('p', null, [
                    h(
                        'p',
                        { style: 'word-break: break-all' },
                        '是否确认授权选中数据？'
                    ),
                ]),
                showCancelButton: true,
                confirmButtonText: '确定',
                cancelButtonText: '取消',
            })
                .then(async () => {
                    debugger;
                    let res;
                    if (row.id != undefined) {
                        if (row.status == '1') {
                            alert('已授权');
                            return;
                        }
                        res = await authorization({ id: row.id });
                    } else {
                        if (
                            this.ids != null &&
                            this.ids != '' &&
                            this.ids != undefined
                        ) {
                            if (!this.statusFlag) {
                                alert('已授权');
                                return;
                            }
                            res = await authorization({ id: this.ids });
                        }
                    }
                    if (res.code == 0) {
                        this.$message.success(res.message);
                        this.getList(this.search);
                    } else {
                        this.$message.error(res.message);
                    }
                })
                .catch(() => {});
        },
        showDetail(row) {
            console.log('点击详情;', row);
            this.$refs.crud.rowView(row);
        },
    },
};
</script>

<style scoped>
.icon-list {
    height: 220px;
    overflow-y: scroll;
}
</style>
