import validate from '@/utils/validate';
export default {
    index: true,
    indexLabel: '序号',
    rowKey: 'id',
    reserveSelection: true,
    selection: true,
    align: 'center',
    card: true,
    menuAlign: 'center',
    emptyBtnIcon: 'el-icon-refresh',
    searchMenuSpan: 6,
    searchMenuPosition: 'left',
    editTitle: '修改用户',
    editBtnText: '修改',
    updateBtnText: '',
    addTitle: '添加信用评分查询',
    addBtn: true,
    addBtnText: '添加信用评分查询',
    addBtnIcon: 'el-icon-search',
    editBtn: false,
    delBtn: false,
    searchBtn: false,
    refreshBtn: false,
    emptyBtn: false,
    searchShowBtn: false,
    searchRow: false,
    labelWidth: 120,
    dialogWidth: 800,
    columnBtn: false,
    excelBtn: false,
    // menuWidth: 100,
    column: [
        {
            label: '查询批次号',
            prop: 'queryNo',
            search: true,
            editDisabled: false,
            addDisplay: false,
            searchLabelWidth: 90,
            width: 160,
        },

        {
            label: '姓名',
            prop: 'name',
            search: true,
            editDisabled: false,
            searchSpan: 5,
            searchLabelWidth: 50,
            rules: [
                {
                    required: true,
                    message: '请输入用户名',
                    trigger: 'blur',
                },
            ],
        },
        {
            label: '身份证号码',
            prop: 'idCard',
            // searchSpan: 8,
            search: true,
            searchLabelWidth: 90,
            slot: true,
            width: 170,
            rules: [
                {
                    required: true,
                    message: '请输入身份证号',
                    trigger: 'blur',
                },
                {
                    validator: validate.checkIdentity,
                    trigger: 'blur',
                },
            ],
        },
        {
            label: '状态',
            prop: 'status',
            searchLabelWidth: 50,
            searchSpan: 5,
            search: true,
            type: 'select',
            dicData: [],
            addDisplay: false,
        },
        {
            label: '申请时间',
            prop: 'createTime',
            showColumn: false,
            addDisplay: false,
            searchslot: true,
            search: true,
            dicData: [],
            width: 160,
        },
        {
            label: '信用评分',
            prop: 'score',
            showColumn: false,
            addDisplay: false,
            editDisplay: false,
        },
        {
            label: '序列号',
            prop: 'seqNo',
            showColumn: false,
            addDisplay: false,
            editDisplay: false,
            hide: true,
        },

        {
            label: '是否新市民',
            prop: 'xsm',
            hide: true,
            addDisplay: false,
            editDisplay: false,
            type: 'select',
        },
        {
            label: '失信被执行人',
            prop: 'sxbxzr',
            showColumn: false,
            addDisplay: false,
            editDisplay: false,
            hide: true,
            type: 'select',
        },
        {
            label: '黑名单',
            prop: 'heimd',
            showColumn: false,
            addDisplay: false,
            editDisplay: false,
            hide: true,
            type: 'select',
        },
        {
            label: '红名单',
            prop: 'hongmd',
            showColumn: false,
            addDisplay: false,
            editDisplay: false,
            hide: true,
            type: 'select',
        },
        {
            label: '民政救助',
            prop: 'mzjz',
            showColumn: false,
            addDisplay: false,
            editDisplay: false,
            hide: true,
            type: 'select',
        },

        {
            label: '行政处罚',
            prop: 'xzcf',
            showColumn: false,
            addDisplay: false,
            editDisplay: false,
            hide: true,
            type: 'select',
        },

        {
            label: '重点人群',
            prop: 'zdrq',
            showColumn: false,
            addDisplay: false,
            editDisplay: false,
            hide: true,
            type: 'select',
        },

        {
            label: '表彰嘉奖',
            prop: 'bzjj',
            showColumn: false,
            addDisplay: false,
            editDisplay: false,
            hide: true,
            type: 'select',
        },
        {
            label: '文明行为',
            prop: 'wmxw',
            showColumn: false,
            addDisplay: false,
            editDisplay: false,
            hide: true,
            type: 'select',
        },

        {
            label: '图书逾期超过150天',
            prop: 'tsyqc150',
            showColumn: false,
            addDisplay: false,
            editDisplay: false,
            hide: true,
            type: 'select',
        },
        {
            label: '年龄22岁以下或60岁以上',
            prop: 'age2260',
            showColumn: false,
            addDisplay: false,
            editDisplay: false,
            hide: true,
            type: 'select',
        },

        {
            label: '公积金贷款逾期数大于等于6次',
            prop: 'gjjdkyqqs6',
            showColumn: false,
            addDisplay: false,
            editDisplay: false,
            hide: true,
            type: 'select',
        },
        {
            label: '公积金贷款当前逾期',
            prop: 'gjjdkdqyq',
            showColumn: false,
            addDisplay: false,
            editDisplay: false,
            hide: true,
            type: 'select',
        },
    ],
};
