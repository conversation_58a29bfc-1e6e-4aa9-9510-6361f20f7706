<template>
  <div>
    <yo-row>
      <yo-col :span="12">
        <div class="grid-content bg-purple">
          <div class="app-container">
            <yo-table
              v-loading="tableLoading"
              :data="data"
              :option="option"
              ref="crud"
              :page.sync="page"
              :search.sync="search"
              @search-change="searchChange"
              @on-load="onLoad"
              @row-save="rowSave"
              @row-del="moreRowDel"
              @row-update="rowUpdate"
              @selection-change="selectionChange"
              v-model="formParent"
              @current-row-change="handleCurrentRowChange"
            >
            
              <template slot-scope="{scope}" slot="searchMenu">
                <el-button
                  size="mini"
                  type="primary"
                  icon="el-icon-search"
                  @click.stop="handleQuery"
                  v-hasPermi="['admin:dict:types:page']"
                  style="marginLeft: 16px;"
                >查询</el-button>
                <el-button size="mini" icon="el-icon-refresh" @click.stop="resetQuery">重置</el-button>
              </template>
              <!-- 自定义左侧操作栏 -->
              <template slot-scope="{size}" slot="menuLeft">
                <el-button
                  type="primary"
                  icon="el-icon-plus"
                  :size="size"
                  plain
                  v-hasPermi="['admin:dict:types:add']"
                  @click.stop="handleAdd()"
                >新增</el-button>
                <el-button
                  type="danger"
                  icon="el-icon-delete"
                  :size="size"
                  plain
                  v-hasPermi="['admin:dict:types:remove:batch']"
                  @click.stop="handleDel()"
                >批量删除</el-button>
              </template>
              <!-- 自定义右侧操作栏 -->
              <template slot-scope="{size}" slot="menuRight">
                <input
                  id="fileslist"
                  v-show="false"
                  type="file"
                  accept=".xls"
                  ref="fileRef"
                  @change="fileChange"
                />
                <el-button
                  type="primary"
                  icon="el-icon-download"
                  size="mini"
                  @click="exportExample"
                  v-hasPermi="['admin:dict:types:download']"
                >下载模板</el-button>
                <el-button
                  type="warning"
                  icon="el-icon-upload2"
                  size="mini"
                  @click="handleImport"
                  v-hasPermi="['admin:dict:types:import']"
                >导入</el-button>
                <el-button
                  type="success"
                  icon="el-icon-download"
                  size="mini"
                  @click="handleExport"
                  v-hasPermi="['admin:dict:types:export']"
                >导出</el-button>
              </template>
              <!-- 自定义列 -->
              <template slot="status" slot-scope="scope">
                <el-tag size="mini" effect="plain" :type="tagType(scope.row)">{{status(scope.row)}}</el-tag>
              </template>
              <!-- 自定义操作按钮 -->
              <template slot-scope="{row,size,type,index}" slot="menu">
                <el-button
                  :size="size"
                  :type="type"
                  icon="el-icon-document"
                  @click.stop="goEntries(row,index)"
                >列表</el-button>
                <el-dropdown
                  :size="size"
                  @command="(command) => handleCommand(command, row)"
                  v-hasPermi="['admin:dict:types:edit','admin:dict:types:remove']"
                >
                  <span class="el-dropdown-link">
                    <i class="el-icon-d-arrow-right el-icon--right"></i>更多
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                      command="handleList"
                      icon="el-icon-edit"
                      v-hasPermi="['admin:dict:types:edit']"
                    >修改</el-dropdown-item>
                    <el-dropdown-item
                      command="handleDelete"
                      icon="el-icon-delete"
                      v-hasPermi="['admin:dict:types:remove']"
                    >删除</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </template>
            </yo-table>
          </div>
        </div>
      </yo-col>
      <yo-col :span="12">
        <entries-dictionary ref="child"></entries-dictionary>
      </yo-col>
    </yo-row>
  </div>
</template>
<script>
import {
  dictList,
  delDict,
  addDict,
  delMoreDict,
  updateDict,
  importDict,
  exportDict,
  downloadExample
} from "@/api/system/dictionary";
import dictListOption from "./infoData/dictListOption.js";
import entriesDictionary from "./entriesDictionary";
export default {
  name: "dictionary",
  components: { entriesDictionary },
  dicts: ["yoaf_dict_type_state"],
  data() {
    return {
      showContainer: true, //显示主界面
      formParent: {},
      search: {},
      exportSearch: {},
      page: {
        layout: "total,prev,pager, next",
        pageSize: 10,
        currentPage: 1,
        pagerCount: 5
      },
      data: [],
      entriesData: [],
      option: dictListOption,
      // 遮罩层
      loading: true,
      tableLoading: true,
      // 表单参数
      checkPageAll: false, // 操作权限弹窗中全选
      isIndeterminate: false, // 全选的不确定状态 只要有一个子集不为true 即设置true
      delMoreArr: [], //批量删除数组
      entriesIndex: 0,
      entriesActives: false
    };
  },
  created() {
    /** *添加字典项数据*/
    this.updateDictData(
      this.option.column,
      "status",
      this.dict.type["yoaf_dict_type_state"]
    );
  },
  methods: {
    // 首次加载调用此方法
    onLoad(page) {
      this.getList(this.search);
    },
    // 更新字典数据
    updateDictData(option, key, data) {
      let column = this.findObject(option, key);
      column.dicData = data;
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case "handleList":
          this.handleList(row);
          break;
        case "handleDelete":
          this.handleDelete(row);
          break;
        default:
          break;
      }
    },
    /** 查询字典列表 */
    getList(params) {
      const data = { ...this.page, ...params };
      dictList(data).then(response => {
        // this.entriesActives = false;
        const res = response.data;
        this.data = res.list;
        this.page.total = res.total;
        this.tableLoading = false;
      });
    },
    //新增按钮操作
    handleAdd() {
      this.$refs.crud.rowAdd();
    },
    //删除按钮操作
    handleDel() {
      this.$refs.crud.rowDel();
    },
    //多选字典
    selectionChange(list) {
      this.delMoreArr = [];
      list.forEach(e => {
        this.delMoreArr.push(e.dictTypeId);
      });
    },
    // 新增表单保存
    rowSave(form, done, loading) {
      addDict(form)
        .then(response => {
          if (response.code == 0) {
            this.$modal.msgSuccess(response.message);
            this.getList();
            done();
          }
        })
        .catch(error => {
          loading();
        });
    },
    //批量删除表单
    moreRowDel(form, done, loading) {
      if (this.delMoreArr.length > 0) {
        this.$modal
        .confirm('是否确认删除字典类型"' + this.delMoreArr + '"及其包含的所有数据项？')
        .then(()=> {
          delMoreDict(this.delMoreArr)
          .then(response => {
            if (response.code == 0) {
              this.$modal.msgSuccess(response.message);
              this.getList();
              this.$refs.child.getList();
              done();
            } else {
              loading();
            }
          })
          .catch(error => {
            loading();
          });
        })
      } else {
        this.$modal.msgWarning("请选择删除数据！");
      }
    },
    /** 导出按钮操作 */
    handleExport() {
      console.log(this.delMoreArr)
      const data = { dictTypeIds: this.delMoreArr, ...this.exportSearch };
      exportDict(data).then(res => {
        this.handleExportData(res);
      });
    },
    /** 处理返回的流文件 */
    handleExportData(res) {
      if (!res) return;
      let data = res.data;
      let filename = res.headers["content-disposition"].split("=")[1];
      let _filename = decodeURI(filename);
      const link = document.createElement("a");
      //创建 Blob对象 可以存储二进制文件
      let blob = new Blob([data], { type: "application/x-excel" });
      link.style.display = "none";
      link.href = URL.createObjectURL(blob);
      link.download = _filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    /** 下载模板 */
    exportExample() {
      downloadExample().then(res => {
        this.handleExportData(res);
      });
    },
    fileChange(event) {
      let file = event.target.files[0];
      let formData = new FormData();
      formData.append("file", file);
      importDict(formData).then(res => {
        if (res.code == 0) {
          this.$message.success(res.message);
          this.handleQuery();
        }
      });
      // 清除文件，防止下次上传相同文件无反应
      event.target.value = null;
    },
    /** 导入按钮操作 */
    handleImport() {
      this.$refs.fileRef.click();
    },
    // 修改表单保存
    rowUpdate(form, index, done, loading) {
      updateDict(form)
        .then(response => {
          if (response.code == 0) {
            this.$modal.msgSuccess(response.message);
            this.getList(this.search);
            done();
          } else {
            loading();
          }
        })
        .catch(error => {
          loading();
        });
    },
    // 搜索按钮
    searchChange(params, done) {
      this.handleQuery();
      setTimeout(() => {
        done();
      }, 1000);
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.page.currentPage = 1;
      this.getList(this.search);
      // this.$refs.child.getList();
      this.$refs.crud.setCurrentRow()
      this.exportSearch = this.search;
      this.$refs.child.dictTypeId="";
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.$refs.crud.searchReset();
      // this.entriesActives = false;
      // this.$refs.child.resetQuery(1);
    },
    //列表按钮操作
    goEntries(row, index) {
      this.$refs.crud.setCurrentRow(row);
    },
    handleCurrentRowChange(row){
      if(!row)return
       const data = {
        from: 1,
        dictTypeId: row.dictTypeId
      };
      this.$refs.child.resetQuery();
      this.$refs.child.handleQuery(row.dictTypeId);
    },
    /** 修改按钮操作 */
    handleList(row) {
      this.formParent = row;
      this.$refs.crud.rowEdit(row);
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      // console.log(row);
      const dictTypeId = row.dictTypeId;
      this.$modal
        .confirm('是否确认删除字典类型"' + dictTypeId + '"及其包含的所有数据项？')
        .then(function() {
          return delDict(dictTypeId);
        })
        .then(res => {
          if (res.code == 0) {
            this.getList(this.search);
            this.$refs.child.getList();
            this.$refs.child.dictTypeId = "";
            this.$message.success(res.message);
          }
        })
        .catch(() => {});
    },
    status(row) {
      switch (row.status) {
        case "1":
          return "启用";
        case "2":
          return "停用";
        case "3":
          return "注销";
        case "4":
          return "锁定";
        default:
          break;
      }
    },
    //tag样式
    tagType(row) {
      switch (row.status) {
        case "1":
          return "success";
        case "2":
          return "danger";
        case "3":
          return "info";
        case "4":
          return "warning";
        default:
          break;
      }
    }
  }
};
</script>
<style scoped>
.yo-table__dialog__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.yo-table__dialog__menu {
  padding-right: 20px;
}
.entriesActive {
  color: #ffba00 !important;
}
</style>