import validate from "@/utils/validate"
export default {
  height:500,
  align: 'center',
  card: true,
  menuAlign: 'center',
  searchBtnText: '查询',
  emptyBtnText: '重置',
  emptyBtnIcon: 'el-icon-refresh',
  searchSpan: 8,
  searchMenuSpan:8,
  searchMenuPosition: 'left',
  addTitle: '添加字典项',
  editTitle: '修改字典项',
  saveBtnText: '确定',
  editBtnText: '修改',
  updateBtnText: '确定',
  // rowKey:'dictId',
  selection: true,
  tip:false,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  refreshBtn: false,
  searchBtn: false,
  emptyBtn: false,
  labelPosition: 'right',
  searchLabelWidth: 10,
  labelWidth: 120,
  columnBtn:false,
  column: [
    {
      label: '字典类型',
      prop: 'dictTypeId',
      search: false,
      hide: true,
      editDisabled: true,
      rules: [{
        required: true,
        message: "字典类型不能为空",
        trigger: "blur"
      },
      { min: 1, max: 64, message: '长度在 1 到 64 个字符', trigger: 'blur' },
      {
        validator: validate.roleIdValidate, trigger: 'blur'
      }
    ]
    },
    {
      label: '字典项代码',
      prop: 'dictId',
      search: true,
      searchLabel: '',
      searchLabelWidth: 0,
      placeholder: '字典项代码',
      editDisabled: true,
      rules: [{
        required: true,
        message: "字典项代码不能为空",
        trigger: "blur"
      },
      { min: 1, max: 64, message: '长度在 1 到 64 个字符', trigger: 'blur' },
      {
        validator: validate.roleIdValidate, trigger: 'blur'
      }
    ]
    },
    {
      label: '字典项名称',
      prop: 'dictName',
      search: true,
      searchLabel: '',
      searchLabelWidth: 0,
      placeholder: '字典项名称',
      rules: [{
        required: true,
        message: "字典项名称不能为空",
        trigger: "blur"
      },
      { min: 1, max: 255, message: '长度在 1 到 255 个字符', trigger: 'blur' }]
    },
    {
      label: '状态',
      prop: 'status',
      type: "select",
      slot: true,
      dicData: [],
      search: true,
      searchLabel: '',
      searchLabelWidth: 0,
      placeholder: '状态',
      rules: [{
        required: true,
        message: "请选择状态",
        trigger: "blur,change"
      }]
    },
    {
      label: '排序',
      prop: 'displayOrder',
      search: false,
      hide: true,
      type: 'number',
      minRows: 0,
      maxRows: 10000,
    }
  ],
}