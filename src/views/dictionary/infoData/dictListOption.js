import validate from "@/utils/validate"
export default {
  height:500,
  align: 'center',
  card: true,
  menuAlign: 'center',
  searchBtnText: '查询',
  emptyBtnText: '重置',
  emptyBtnIcon: 'el-icon-refresh',
  searchSpan: 8,
  searchMenuSpan:8,
  searchMenuPosition: 'left',
  addTitle: '添加字典',
  editTitle: '修改字典',
  saveBtnText: '确定',
  editBtnText: '修改',
  updateBtnText: '确定',
  rowKey:'dictTypeId',
  selection: true,
  tip:false,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  refreshBtn: false,
  searchBtn: false,
  emptyBtn: false,
  searchLabelWidth: 10,
  labelWidth: 120,
  simplePage:false,
  columnBtn:false,
  highlightCurrentRow: true,
  column: [
    {
      label: '字典类型',
      prop: 'dictTypeId',
      search: true,
      searchLabel: '',
      searchLabelWidth: 0,
      placeholder: '字典类型',
      editDisabled: true,
      rules: [{
        required: true,
        message: "字典类型不能为空",
        trigger: "blur"
      },
      { min: 1, max: 64, message: '长度在 1 到 64 个字符', trigger: 'blur' },
      { validator: validate.roleIdValidate, trigger: 'blur'}
    ]
    },
    {
      label: '字典名称',
      prop: 'dictTypeName',
      search: true,
      searchLabel: '',
      searchLabelWidth: 0,
      placeholder: '字典名称',
      rules: [{
        required: true,
        message: "字典名称不能为空",
        trigger: "blur"
      },
      { min: 1, max: 255, message: '长度在 1 到 255 个字符', trigger: 'blur' }]
    },
    {
      label: '状态',
      prop: 'status',
      type: "select",
      slot: true,
      dicData: [],
      search: true,
      searchLabel: '',
      searchLabelWidth: 0,
      placeholder: '状态',
      rules: [{
        required: true,
        message: "请选择状态",
        trigger: "blur,change"
      }]
    },
    {
      label: '数据来源',
      prop: 'dataSource',
      search: true,
      searchLabel: '',
      searchLabelWidth: 0,
      placeholder: '数据来源',
      rules: [{ 
        min: 1, 
        max: 64, 
        message: '长度在 1 到 64 个字符', 
        trigger: 'blur' 
      }]
    },
    {
      label: '描述',
      prop: 'dictTypeDesc',
      search: false,
      hide: true,
      rules: [{ 
        min: 1, 
        max: 255, 
        message: '长度在 1 到 255 个字符', 
        trigger: 'blur' 
      }]
    }
  ],
}