import validate from "@/utils/validate"
export default {
  index: true,
  indexLabel: "序号",
  selection: true,
  tip:false,
  align: "center",
  card: true,
  menuAlign: "center",
  searchBtnText: "查询",
  emptyBtnText: "重置",
  emptyBtnIcon: "el-icon-refresh",
  searchMenuSpan: 4,
  searchMenuPosition: "left",
  addTitle: "添加工作组 ",
  editTitle: "修改工作组",
  saveBtnText: "确定",
  editBtnText: "修改",
  updateBtnText: "确定",
  addBtn: false,
  editBtn: false,
  delBtn: false,
  refreshBtn: false,
  searchBtn: false,
  emptyBtn: false,
  columnBtn: false,
  searchLabelWidth: 100,
  column: [
    {
      label: "工作组编号",
      prop: "groupId",
      search: true,
      editDisabled: true,
      labelWidth: 100,
      rules: [
        {
          required: true,
          message: "工作组编号不能为空",
          trigger: "blur"
        },
        { min: 1, max: 20, message: "长度在 1 到 20 个字符", trigger: "blur" },
        {
          validator: validate.roleIdValidate, trigger: 'blur'
        }
      ]
    },
    {
      label: "工作组名称",
      prop: "groupName",
      labelWidth: 100,
      search: true,
      rules: [
        {
          required: true,
          message: "工作组名称不能为空",
          trigger: "blur"
        },
        { min: 1, max: 20, message: "长度在 1 到 20 个字符", trigger: "blur" },
      ]
    },
    {
      label: "工作组状态",
      labelWidth: 100,
      prop: "status",
      type: "select",
      slot: true,
      dicData: [],
      search: true,
      searchslot: true,
      rules: [
        {
          required: true,
          message: "请选择状态",
          trigger: "blur,change"
        }
      ]
    },
    {
      label: "创建人",
      prop: "createUser",
      editDisplay: false,
      addDisplay: false
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "datetime",
      editDisplay: false,
      addDisplay: false
    },
    {
      label: "显示顺序",
      prop: "displayOrder",
      hide: true,
      type: 'number',
      minRows: 0,
      maxRows: 10000,
      rules: [
        {
          required: true,
          message: "显示顺序不能为空",
          trigger: "blur,change"
        },
      ]
    },
    {
      label: "工作组描述",
      prop: "groupDesc",
      type: "textarea",
      hide: true,
      rules: [
        { min: 1, max: 255, message: "长度在 1 到 255 个字符", trigger: "blur" },
      ]
    }
  ]
};