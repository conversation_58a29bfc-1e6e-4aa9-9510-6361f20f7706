<template>
  <div>
    <el-container v-if="showContainer">
      <el-aside>
        <!-- <yo-table
          :option="treeoption"
          :data="treedata"
          @tree-load="treeLoad"
          @row-click="handleRowClick"
        >
          <template slot-scope="{size}" slot="menuLeft">
            <div style="height:0">部门列表</div>
          </template>
        </yo-table> -->
        <el-input
          v-model="filterText"
          placeholder="搜索关键字"
          clearable
          size="small"
          style="margin-bottom: 10px;"
        ></el-input>
        <el-tree
          ref="tree"
          node-key="id"
          :props="treeprops"
          :load="treeloadNode"
          :expand-on-click-node="false"
          :default-expanded-keys="defaultexpanded"
          :filter-node-method="filterNode"
          @node-click="handleNodeClick"
          lazy
        >
        <span class="custom-tree-node" slot-scope="{ node, data }" :title="node.label">
          <svg-icon :icon-class="iconType(data)" />&nbsp;
          <span>{{ node.label }}</span>
      </span>
        </el-tree>
      </el-aside>
      <el-main style="padding-top:0px;">
        <el-tabs>
          <el-tab-pane class="app-container">
            <span slot="label">{{treeName}}</span>
            <yo-table
              v-loading="tableLoading"
              :data="tabledata"
              :option="tableoption"
              :before-open="beforeOpen"
              ref="crud"
              :page.sync="page"
              :search.sync="search"
              @on-load="onLoad"
              @row-update="rowUpdate"
              @row-del="moreRowDel"
              @row-save="rowSave"
              v-model="formParent"
              @selection-change="selectionChange"
              @search-change="searchChange"
            >
              <!-- 自定义表单 -->
              <template slot-scope="{scope}" slot="upDeptNameAForm">
                <el-cascader
                  ref="upDeptNameAFormRef"
                  v-model="formParent.upDeptNameA"
                  :show-all-levels="false"
                  :options="treedata"
                  :props="props"
                  clearable
                  placeholder="请选择上级机构"
                  @change="closeUpDeptNameACascader()"
                ></el-cascader>
              </template>
              <template slot="deptStatus" slot-scope="scope">
                <el-tag
                  size="mini"
                  effect="plain"
                  :type="tagType(scope.row)"
                >{{userStat(scope.row)}}</el-tag>
              </template>
              <template slot-scope="{scope}" slot="searchMenu">
                <el-button
                  size="mini"
                  type="primary"
                  icon="el-icon-search"
                  @click.stop="handleQuery"
                  v-hasPermi="['admin:depts:page']"
                >查询</el-button>
                <el-button size="mini" icon="el-icon-refresh" @click.stop="resetQuery()">重置</el-button>
              </template>
              <!-- 自定义左侧操作栏 -->
              <template slot-scope="{size}" slot="menuLeft">
                <el-button
                  type="primary"
                  icon="el-icon-plus"
                  size="mini"
                  plain
                  v-hasPermi="['admin:depts:add']"
                  @click.stop="handleAdd"
                >新增</el-button>
                <el-button
                  type="danger"
                  icon="el-icon-delete"
                  size="mini"
                  plain
                  v-hasPermi="['admin:depts:remove:batch']"
                  @click.stop="handleDelMore()"
                >批量删除</el-button>
              </template>
              <!-- 自定义右侧操作栏 -->
              <template slot-scope="{size}" slot="menuRight">
                <input
                  id="fileslist"
                  v-show="false"
                  type="file"
                  accept=".xls"
                  ref="fileRef"
                  @change="fileChange"
                />
                <el-button
                  type="primary"
                  icon="el-icon-download"
                  size="mini"
                  @click="exportExample"
                  v-hasPermi="['admin:depts:download']"
                >下载模板</el-button>
                <el-button
                  type="warning"
                  icon="el-icon-upload2"
                  size="mini"
                  @click="handleImport"
                  v-hasPermi="['admin:depts:import']"
                >导入</el-button>
                <el-button
                  type="success"
                  icon="el-icon-download"
                  size="mini"
                  @click="handleExport"
                  v-hasPermi="['admin:depts:export']"
                >导出</el-button>
              </template>
              <template slot-scope="{row,size,type,index}" slot="menu">
                <el-button
                  :size="size"
                  :type="type"
                  icon="el-icon-edit"
                  @click.stop="handleEdit(row,index)"
                  v-hasPermi="['admin:depts:edit']"
                >修改</el-button>
                <el-button
                  :size="size"
                  :type="type"
                  icon="el-icon-delete"
                  @click="handleDelete(row)"
                  v-hasPermi="['admin:depts:remove']"
                >删除</el-button>
                <el-dropdown :size="size" @command="(command) => handleCommand(command, row)">
                  <span class="el-dropdown-link">
                    <i class="el-icon-d-arrow-right el-icon--right"></i>更多
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                      command="handleCheckInfo"
                      icon="el-icon-info"
                      v-hasPermi="['admin:depts:get']"
                    >查看</el-dropdown-item>
                    <el-dropdown-item
                      command="handleDeployUser"
                      icon="el-icon-s-custom"
                      v-hasPermi="['admin:depts:users:page']"
                    >配置用户</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </template>
            </yo-table>
          </el-tab-pane>
        </el-tabs>
      </el-main>
    </el-container>
    <div v-else>
      <deploy-user @show-detail="showDetails" :instId="deptId"></deploy-user>
    </div>
  </div>
</template>
<script>
import {
  instsList,
  addInsts,
  getInsts,
  editInsts,
  delInsts,
  exportInsts,
  downloadExample,
  importInsts,
  delMoreInsts
} from "@/api/system/department";
import { mapActions } from "vuex";
import treeoption from "./infoData/treeoption.js";
import tableoption from "./infoData/tableoption.js";
import deployUser from "./component/deployUser";
import pluck from "yo-utils/pluck";

export default {
  name: "Department",
  dicts: ["yoaf_dept_state"],
  components: {
    deployUser
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  data() {
    return {
      filterText: "",
      defaultexpanded: [], //默认展开第一级
      treeName: "全部",
      treeLoading: true,
      tableLoading: false,
      showContainer: true, //显示主界面
      form: {},
      formParent: {},
      search: {},
      treedata: [],
      treeoption: treeoption,
      tabledata: [],
      tableoption: tableoption,
      page: {
        pageSize: 10,
        currentPage: 1
      },
      treeprops: {
        label: "name",
        children: "children"
      },
      props: {
        value: "id",
        label: "name",
        checkStrictly: true,
        emitPath: false,
        lazy: true,
        lazyLoad: (node, resolve) => {
          if (node && resolve) {
            const tree = node.data;
            const query = {
              id: tree.id,
              queryType: tree.queryType
            };
            // 通过调用resolve将子节点数据返回，通知组件数据加载完成
            this.instDeptTreeChild(query).then(res => {
              resolve(res);
            });
          }
        }
      },
      upInstId: "",
      deptId: null,
      // 非多个禁用
      multiple: true,
      tableQuery: {
        deptId: "",
        instId: "",
        upDeptId: ""
      },
      delMoreArr: [],
      selectTree: [],
      treeType: ""
    };
  },
  computed: {},
  created() {
    this.getTreeList();
    //DeptTree列表
    this.instTree("dept").then(res => {
      this.selectTree = res;
    });
    /** *添加字典项数据*/
    this.updateDictData(
      this.tableoption.column,
      "deptStatus",
      this.dict.type["yoaf_dept_state"]
    );
  },
  methods: {
    ...mapActions(["instTree", "instDeptTreeChild"]),
    showDetails() {
      this.showContainer = true;
    },
    // 更新字典数据
    updateDictData(option, key, data) {
      let column = this.findObject(option, key);
      column.dicData = data;
    },

    /***  table方法   ****/
    // 首次加载调用此方法
    onLoad(page) {
      this.getTableList(this.tableQuery);
    },
    /**分页查询部门列表*/
    getTableList(query) {
      const data = { ...query, ...this.page };
      instsList(data).then(res => {
        let list = res.data.list;
        if(!list) return;
        list.forEach(item => {
          if (item.instId) {
            item.upDeptNameA = item.inst.instNameAbbr;
          } else {
            item.upDeptNameA = item.upDept.deptName;
          }
        });
        this.tabledata = list;
        this.page.total = res.data.total;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      const search = { ...this.tableQuery, ...this.search};
      this.page.currentPage = 1;
      this.getTableList(search);
    },
    // 搜索按钮
    searchChange(params, done) {
      this.handleQuery();
      setTimeout(() => {
        done();
      }, 1000);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.$refs.crud.searchReset();
      // this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$refs.crud.rowAdd();
    },
    // 部门表单，选择上级机构/部门后自动关闭级联框
    closeUpDeptNameACascader() {
      this.$refs.upDeptNameAFormRef.dropDownVisible = false
    },
    //批量删除按钮操作
    handleDelMore() {
      this.$refs.crud.rowDel();
    },
    //批量删除表单
    moreRowDel(form, done, loading) {
      if (this.delMoreArr.length > 0) {
        delMoreInsts(this.delMoreArr)
          .then(response => {
            if (response.code == 0) {
              this.$modal.msgSuccess(response.message);
              this.getTableList(this.tableQuery);
              this.resetNode()
              done();
            }
          })
          .catch(error => {
            loading();
          });
      } else {
        this.$modal.msgWarning("请选择删除数据！");
      }
    },
    // 弹窗打开
    beforeOpen(done, type) {
      done();
    },
    // 修改部门数据
    updateDictData(option, key, data) {
      let column = this.findObject(option, key);
      column.dicData = data;
    },
    filterArr(arr, key) {
      if (!Array.isArray(arr)) {
        return false;
      }
      arr.map(item => {
        // console.log(item.id)
        // console.log(key)
        if (item.id == key) {
          // console.log(item, "sd");
          this.treeType = item.type;
        } else {
          //console.log(item,'item.....')
          this.filterArr(item.children, key);
        }
      });
    },
    // 新增表单保存
    rowSave(form, done, loading) {
      const bb = form.upDeptNameA;
      this.filterArr(this.selectTree, bb);
      if (this.treeType == "inst") {
        form.instId = bb;
      } else {
        form.upDeptId = bb;
      }
      addInsts(form).then(response => {
        if (response.code == 0) {
          this.$message.success(response.message);
          this.handleQuery();
          this.resetNode();
          done();
        }
      });
    },
    // 修改
    async handleEdit(row, index) {
      await getInsts(row.deptId).then(res => {
        let data = res.data;
        if (data.instId != "") {
          data.upInstId = data.instId;
          data.upDeptNameA = data.inst["instNameAbbr"];
          data.upDeptNameB = data.inst["instNameAbbr"];
        } else {
          data.upInstId = data.upDeptId;
          data.upDeptNameA = data.upDept["deptName"];
          data.upDeptNameB = data.upDept["deptName"];
        }

        this.formParent = data;
        this.$refs.crud.rowEdit(data, index);
      });
    },
    // 修改表单保存
    rowUpdate(form, index, done, loading) {
      editInsts(form).then(response => {
        if (response.code == 0) {
          this.$message.success(response.message);
          this.handleQuery();
          this.resetNode();
          done();
        }
      });
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case "handleCheckInfo":
          this.handleCheckInfo(row);
          break;
        case "handleDeployUser":
          this.handleDeployUser(row);
          break;
        default:
          break;
      }
    },
    //查看
    handleCheckInfo(row) {
      getInsts(row.deptId).then(res => {
        let data = res.data;
        if (data.instId != "") {
          data.upDeptNameB = data.inst["instNameAbbr"];
        } else {
          data.upDeptNameB = data.upDept["deptName"];
        }
        this.formParent = data;
        this.$refs.crud.rowView(data);
      });
    },
    handleDeployUser(row) {
      this.deptId = row.deptId;
      this.showContainer = false;
    },
    /***多选框选中数据**/
    selectionChange(list) {
      this.delMoreArr = [];
      list.forEach(e => {
        this.delMoreArr.push(e.deptId);
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const h = this.$createElement;
      this.$msgbox({
        title: "提示",
        message: h("p", null, [
          h(
            "p",
            { style: "word-break: break-all" },
            '是否确认删除id为"' + row.deptId + '"的数据项？'
          )
        ]),
        showCancelButton: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消"
      })
        .then(() => {
          this.handleDel(row.deptId, delInsts);
        })
        .catch(() => {});
    },
    async handleDel(id, delFn) {
      let res = await delFn(id);
      if (res.code == 0) {
        this.$message.success(res.message);
        this.handleQuery();
        this.resetNode();
      }
    },
    /** 处理返回的流文件 */
    handleExportData(res) {
      if (!res) return;
      let data = res.data;
      let filename = res.headers["content-disposition"].split("=")[1];
      let _filename = decodeURI(filename);
      const link = document.createElement("a");
      //创建 Blob对象 可以存储二进制文件
      let blob = new Blob([data], { type: "application/x-excel" });
      link.style.display = "none";
      link.href = URL.createObjectURL(blob);
      link.download = _filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    /** 下载模板 */
    exportExample() {
      downloadExample().then(res => {
        this.handleExportData(res);
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      const data = { ...this.search, deptIds:this.delMoreArr };
      exportInsts(data).then(res => {
        this.handleExportData(res);
      });
    },
    fileChange(event) {
      let file = event.target.files[0];
      let formData = new FormData();
      formData.append("file", file);
      importInsts(formData).then(res => {
        if (res.code == 0) {
          this.$message.success(res.message);
          this.handleQuery();
        }
      });
      // 清除文件，防止下次上传相同文件无反应
      event.target.value = null;
    },
    /** 导入按钮操作 */
    handleImport() {
      // this.$refs.fileRef.dispatchEvent(new MouseEvent('click'))
      this.$refs.fileRef.click();
    },

    /*****以下为 tree方法 */
    /** 查询机构树 */
    getTreeList() {
      this.instTree("dept").then(res => {
        if(!res)return
        this.treedata = this.filterTree(res);
        this.treeLoading = false;
      });
    },
    // 过滤数据
    filterTree(data) {
      data.forEach(item => {
        item.hasChildren = item.lazy;
        if (item.children) return this.filterTree(item.children);
      });
      return data;
    },

    // 点击节点回调
    handleNodeClick(data) {
      this.treeName = data.name;
      this.upInstId = data.id;
      this.tableQuery = {
        deptId: "",
        instId: "",
        upDeptId: ""
      }
      if (data.type == "inst") {
        this.tableQuery.instId = data.id;
      } else if (data.type == "dept") {
        this.tableQuery.upDeptId = data.id;
      }
      this.getTableList(this.tableQuery);
    },
    /***刷新tree**/
    resetNode(){
      let theChildren = this.rootNode.childNodes
      theChildren.splice(0,theChildren.length)
      this.treeloadNode(this.rootNode,this.rootResolve)
    },
    /***懒加载回调**/
    treeloadNode(node, resolve) {
      if (node.level === 0) {
        this.rootNode = node
        this.rootResolve = resolve
        this.instTree("dept").then(res => {
        if(!res)return
          res.forEach(item => {
            this.defaultexpanded.push(item.id);
          });
          return resolve(res);
        });
      }
      if (node.level == 1) {
        if (node.data.children) return resolve(node.data.children);
        return resolve([]);
      }
      if (node.level > 1) {
        const query = {
          id: node.data.id,
          queryType: node.data.queryType
        };
        this.instDeptTreeChild(query).then(res => {
          return resolve(res);
        });
      }
    },
    // tree搜索
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },

    iconType(data) {
      switch (data.type) {
        case "inst":
          return "mechanism";
        case "dept":
          return "department";
        default:
          break;
      }
    },
    userStat(row) {
      switch (row.deptStatus) {
        case "1":
          return "启用";
        case "2":
          return "停用";
        case "3":
          return "注销";
        case "4":
          return "锁定";
        default:
          break;
      }
    },
    //tag样式
    tagType(row) {
      switch (row.deptStatus) {
        case "1":
          return "success";
        case "2":
          return "danger";
        case "3":
          return "info";
        case "4":
          return "warning";
        default:
          break;
      }
    }
  }
};
</script>
<style scoped>
.el-tree {
  max-height: calc(100vh - 180px) !important;
  min-height: calc(100vh - 300px) !important;
  overflow: auto;
  font-size: 14px
}
</style>
