import validate from "@/utils/validate"

export default {
  index: true,
  indexLabel: '序号',
  selection: true,
  align: 'center',
  card: true,
  // menuAlign: 'center',
  searchSpan: 6,
  searchMenuSpan: 6,
  searchMenuPosition: 'left',
  searchLabelWidth: 10,
  addTitle: '新增功能',
  editTitle: '修改功能',
  updateBtnText: '保存',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  searchBtn: false,
  emptyBtn: false,
  refreshBtn: false,
  labelPosition: 'right',
  labelWidth: 120,
  maxHeight: 455,
  tip: false,
  columnBtn: false,
  column: [
    {
    label: '所属菜单',
    prop: 'menuId',
    hide: true,
    formslot: true,
    rules: [{
      required: true,
      message: "请输入所属菜单",
      trigger: "blur"
    }]
  }, {
    label: '所属菜单',
    prop: 'menuName',
    addDisplay: false,
    editDisplay: false,
    rules: [{
      required: true,
      message: "请输入所属菜单",
      trigger: "blur"
    }]
  }, {
    label: '功能名称',
    prop: 'funcName',
    search: true,
    searchLabel: '',
    searchLabelWidth: 0,
    placeholder: '请输入功能名称',
    rules: [{
      required: true,
      message: "请输入功能名称",
      trigger: "blur"
    },
    {
      min: 1, max: 30,
      message: '长度在 1 到 30 个字符',
      trigger: 'blur'
    },
    {
      validator: validate.blankSpace, trigger: 'blur'
    }],
  }
    , {
    label: '按钮权限标识',
    prop: 'funcBtn',
    search: true,
    searchLabel: '',
    placeholder: '请输入按钮权限标识',
    rules: [
    {
      min: 0, max: 150,
      message: '最大长度 150 个字符',
      trigger: 'blur'
    }],
  }
    , {
    label: '请求地址',
    prop: 'funcURI',
    search: true,
    searchLabel: '',
    placeholder: '请输入请求地址',
    formslot: true,
    rules: [
    {
      min: 0, max: 150,
      message: '最大长度 150 个字符',
      trigger: 'blur'
    }],
  }, {
    label: '排序',
    prop: 'displayOrder',
    hide: true,
    type: 'number',
    formslot: true,
    minRows: 0,
    showColumn: false,
    rules: [{
      required: true,
      message: "请输入排序",
      trigger: "change"
    }],
  }, {
    label: '功能描述',
    prop: 'funcDesc',
    hide: true,
    showColumn: false,
    rules: [
      {
        min: 0, max: 200,
        message: '最大长度 200 个字符',
        trigger: 'blur'
      }],
  }
  ]
}
