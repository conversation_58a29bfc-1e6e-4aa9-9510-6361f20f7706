<template>
  <div>
    <div style="margin:10px 0 5px 20px;">
      <el-button @click="handleClose" size="mini" type="primary" icon="el-icon-back" plain>返回</el-button>
      <!-- <span style="margin:0 10px;">|</span>
      <span>角色关联菜单权限配置</span>-->
    </div>
    <el-container style="padding:0 20px;box-sizing: border-box;">
      <el-aside class="left_container" :class="!isOpenMenu?'left_container_isOpenMenu':''">
        <el-button-group v-show="isOpenMenu" style="margin: 5px 0;position:relative">
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-check"
            @click.stop="handleMenuSave"
            v-hasPermi="['admin:role:menu:add']"
          >保存</el-button>
          <el-button
            size="mini"
            type="primary"
            :icon="isShowTree?'el-icon-folder-opened':'el-icon-folder'"
            @click="openMenuTree"
          >{{isShowTree?'折叠':'展开'}}</el-button>
        </el-button-group>
        <el-tooltip effect="dark" :content="!isOpenMenu?'展开':'收起'" placement="bottom">
          <i
            class="menuOpenStyle"
            :class="!isOpenMenu?'el-icon-d-arrow-right menuNoOpenStyle':'el-icon-d-arrow-left'"
            @click="isOpenMenuFunc"
          ></i>
        </el-tooltip>
        <yo-tree
          v-show="isOpenMenu "
          ref="menuTreeRef"
          :option="treeOption"
          :data="treeData"
          @node-click="nodeClick"
          @check-change="handleCheckChange"
        >
          <span class="custom-tree-node" slot-scope="{ node, data }">
            <span>{{ node.label }}</span>
          </span>
        </yo-tree>
      </el-aside>
      <el-main style="padding-top:10px;">
        <yo-table
          ref="actionsCrud"
          :data="loadData"
          :option="loadOption"
          v-model="obj"
          @selection-change="selectionChange"
        >
          <template slot-scope="scope" slot="menuLeft">
            <el-tooltip effect="dark" content="按钮权限保存功能" placement="bottom">
              <el-button
                type="primary"
                icon="el-icon-check"
                size="mini"
                @click.stop="handleActionsSave"
                v-hasPermi="['admin:role:menu:func:add']"
              >保存</el-button>
            </el-tooltip>
          </template>
        </yo-table>
      </el-main>
    </el-container>
  </div>
</template>
<script>
import {
  getRoleMenuTreeList,
  getRoleMenuTreeActionsList,
  saveRoleMenuTreeActions,
  saveRoleMenuActions
} from "@/api/system/role";
import treeOption from "./infoData/menuTreeOption";
import loadOption from "./infoData/funcActionsOption";

export default {
  data() {
    return {
      checkData: [],
      isOpenMenu: true,
      isShowTree: true,
      // roleId: "",
      menuId: "",
      obj: {},
      treeData: [],
      treeOption: treeOption,
      loadData: [],
      loadOption: loadOption
    };
  },
  props: ["roleId"],
  created() {
    this.init();
    if (this.roleId && this.menuId) {
      this.loadGetRoleMenuTreeActionsList();
    }
  },
  methods: {
    // 初始化数据
    async init() {
      this.treeData = [];
      // this.roleId = this.$route.query.roleId
      await getRoleMenuTreeList(this.roleId).then(response => {
        // console.log(response, '角色关联菜单资源树')
        const data = response.data;
        let arr = [];
        var _filterTree = tree => {
          if (tree && tree.length) {
            tree.forEach(item => {
              if (item.checked) {
                setTimeout(() => {
                  this.$refs.menuTreeRef.setChecked(item.id, true);
                }, 100);
              }
              if (item.children) {
                _filterTree(item.children);
              }
            });
          }
        };
        _filterTree(data);
        this.treeData = response.data;
      });
    },
    // 返回按钮
    handleClose() {
      this.$emit("show-detail");
      // const obj = { path: '/role/index' }
      // this.$tab.closeOpenPage(obj)
    },
    // 加载菜单资源树按钮列表
    loadGetRoleMenuTreeActionsList() {
      this.checkData = [];
      this.$refs.actionsCrud.selectClear();
      const reqData = {
        menuId: this.menuId,
        roleId: this.roleId
      };
      getRoleMenuTreeActionsList(reqData).then(res => {
        // console.log('获取菜单资源树下的按钮', res)
        this.loadData = res.data || [];
        this.filterActions(this.loadData);
      });
    },
    expandAll() {
      let els = document.getElementsByClassName("el-tree-node__expand-icon");
      for (let i = 0; i < els.length; i++) {
        els[i].click();
      }
    },
    // 展开收起左侧菜单
    isOpenMenuFunc() {
      this.isOpenMenu = !this.isOpenMenu;
    },
    // 展开
    openMenuTree() {
      this.unFoldAll();
    },
    // 展开所有下级
    unFoldAll() {
      (this.isShowTree = !this.isShowTree),
        this.$nextTick(() => {
          setTimeout(() => {
            this.expandAll();
          }, 300);
        });
    },
    // 提交资源树时，处理数据menuList
    filterTree(data, key) {
      let res = [];
      var _filterTree = (tree, key) => {
        if (tree && tree.length) {
          tree.forEach(item => {
            if (item.checked) {
              res.push({
                id: item.id,
                key: item[key]
              });
            }
            if (item.children) {
              _filterTree(item.children, key);
            }
          });
        }
      };
      _filterTree(data, key);
      return res;
    },
    // 保存勾选的资源树
   async handleMenuSave() {
      let menuList = this.filterTree(this.treeData, "menuName");
      const submitData = {
        roleId: this.roleId,
        menuList: menuList
      };
     let response= await saveRoleMenuActions(submitData)
      if (response.code == 0) {
        this.$modal.msgSuccess(response.message);
        this.init();
      }
    },
    handleCheckChange(current, b) {
      current.checked = b;
    },
    /*************  以下为点击左侧资源树叶子节点 获取右侧按钮列表方法 ******************/
    // 点击资源树节点，当点击叶子节点时，调用按钮列表接口
    nodeClick(data) {
      this.menuId = data.id;
      const flag = Object.keys(data).includes("children");
      // if (flag) return;
      this.loadGetRoleMenuTreeActionsList();
    },
    // 过滤选中状态
    filterActions(data) {
      if (data && data.length) {
        data.forEach(item => {
          if (item.checked) {
            this.$refs.actionsCrud.toggleRowSelection(item);
          }
        });
      }
    },
    // 按钮权限列表中，选项发生变化触发
    selectionChange(selection) {
      this.checkData = selection;
    },
    // 保存选中的按钮
    handleActionsSave() {
      if (!this.checkData) return;
      let funcList = [];
      this.checkData.forEach(item => {
        funcList.push({
          id: item.id,
          funcName: item.funcName
        });
      });
      const submitData = {
        menuId: this.menuId,
        roleId: this.roleId,
        funcList: funcList
      };
       saveRoleMenuTreeActions(submitData).then(response => {
      if (response.code == 0) {
        this.$modal.msgSuccess(response.message);
        this.loadGetRoleMenuTreeActionsList()
      }
});
      // saveRoleMenuTreeActions(submitData)
      //   .then(res => {
      //     if (res.code == 0) {
      //       this.$modal.msgSuccess(res.message);
      //     }
      //     this.loadGetRoleMenuTreeActionsList();
      //   })
      //   .catch(error => {
      //     this.loadGetRoleMenuTreeActionsList();
      //   });
    }
  }
};
</script>
<style scoped>
.left_container {
  position: relative;
  min-width: 200px;
  height: calc(100vh - 150px);
  overflow: auto;
  overflow-x: hidden;
}

.left_container_isOpenMenu {
  min-width: 8px;
  width: 8px !important;
}

.menuOpenStyle {
  position: absolute;
  top: 18px;
  right: 20px;
  font-size: 20px;
  cursor: pointer;
}

.menuNoOpenStyle {
  right: 10px;
}

>>> .el-tree {
  min-height: calc(100vh - 258px) !important;
}
</style>