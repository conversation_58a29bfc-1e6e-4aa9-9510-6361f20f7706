<template>
  <div class="app-container">
    <select-user ref="select" :roleId="roleId" :activeRolename="activeRoleName" @ok="handleQuery" />
    <yo-table
      v-loading="loading"
      :data="userList"
      :option="option"
      ref="crud"
      :page.sync="page"
      :search.sync="search"
      @on-load="onLoad"
      @search-change="searchChange"
      @refresh-change="refresh"
      v-model="formParent"
      @selection-change="handleSelectionChange"
    >
      <!-- 自定义搜索框 -->
      <template slot="userStatSearch" slot-scope="{disabled,size}">
        <el-select
          v-model="search.userStat"
          :disabled="disabled"
          placeholder="状态"
          clearable
          :size="size"
        >
          <el-option
            v-for="dict in dict.type['yoaf_user_state']"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </template>
      <!-- 自定义列 -->
      <template slot="email" slot-scope="scope">
        <div>
          <span>{{scope.row.email}}</span>
          <el-button
            type="text"
            icon="el-icon-view"
            v-hasPermi="['admin:role:user:sensitive:get']"
            @click="viewEmail(scope.row)"
          ></el-button>
        </div>
      </template>
      <template slot="phoneNumber" slot-scope="scope">
        <div>
          <span>{{scope.row.phoneNumber}}</span>
          <el-button
            type="text"
            icon="el-icon-view"
            v-hasPermi="['admin:role:user:sensitive:get']"
            @click="viewPhoneNumber(scope.row)"
          ></el-button>
        </div>
      </template>
      <template slot="gender" slot-scope="scope">
        <el-tag
          :type="scope.row.gender=='1'? '':'danger'"
          size="mini"
        >{{scope.row.gender=='1'? '男':'女'}}</el-tag>
      </template>
      <template slot="userStat" slot-scope="scope">
        <el-tag size="mini" effect="plain" :type="tagType(scope.row)">{{userStat(scope.row)}}</el-tag>
      </template>
      <template slot-scope="{scope}" slot="searchMenu">
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-search"
          @click.stop="handleQuery"
          v-hasPermi="['admin:role:page:users']"
        >查询</el-button>
        <el-button size="mini" icon="el-icon-refresh" @click.stop="resetQuery">重置</el-button>
      </template>
      <!-- 自定义左侧操作栏 -->
      <template slot-scope="scope" slot="menuLeft">
        <el-button @click="handleClose" type="info" size="mini" icon="el-icon-back">返回</el-button>
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          plain
          v-hasPermi="['admin:role:add:users']"
          @click.stop="handleAdd()"
        >添加用户</el-button>
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >批量删除</el-button>
      </template>
      <!-- 自定义操作按钮 -->
      <template slot-scope="{row,size,type,index}" slot="menu">
        <el-button
          :size="size"
          :type="type"
          icon="el-icon-delete"
          @click="handleDelete(row)"
          v-hasPermi="['admin:role:remove:users']"
        >删除</el-button>
      </template>
    </yo-table>
  </div>
</template>

<script>
import {
  allocatedUserList,
  authUserCancel,
  authUserCancelAll,
  delRoleUserList,
  usersSensitive
} from "@/api/system/role";
import selectUser from "./selectUser";

export default {
  name: "AuthUser",
  dicts: ["yoaf_user_state"],
  components: { selectUser },
  data() {
    return {
      title: "",
      loading: true,
      formParent: {},
      fullscreen: false,
      search: {},
      page: {
        pageSize: 10,
        currentPage: 1
      },
      data: [],
      option: {
        title: `已配置${this.roleobj.roleName}用户列表`,
        titleSize: "h2",
        titleStyle: {
          // color:'red'
        },
        selection: true,
        index: true,
        indexLabel: "序号",
        align: "center",
        card: true,
        menuAlign: "center",
        emptyBtnText: "重置",
        emptyBtnIcon: "el-icon-refresh",
        searchMenuSpan: 4,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        searchBtn: false,
        emptyBtn: false,
        columnBtn: false,
        refreshBtn: false,
        labelWidth: 100,
        column: [
          {
            label: "用户名",
            prop: "userName",
            search: true
          },
          {
            label: "姓名",
            prop: "userNickname",
            search: true
          },
          {
            label: "性别",
            prop: "gender",
            slot: true
          },
          {
            label: "邮箱",
            prop: "email",
            slot: true
          },
          {
            label: "手机号",
            prop: "phoneNumber",
            slot: true
          },
          {
            label: "状态",
            prop: "userStat",
            slot: true,
            search: true,
            searchslot: true
          }
          // {
          //   label: '创建时间',
          //   prop: 'createTime',
          //   type: 'datetime',
          //   display: false
          // }
        ]
      },

      // 遮罩层
      loading: false,
      // 选中用户组
      userNames: [],
      // 非多个禁用
      multiple: true,
      // 用户表格数据
      userList: []
    };
  },
  props: {
    roleId: {
      type: String,
      default: ""
    },
    roleobj: {
      type: Object,
      default: {}
    },
    activeRoleName: {
      type: String,
      default: ''
    }
  },
  created() {},
  mounted() {
    console.log(this.roleobj,this.roleId)
  },
  methods: {
    // 首次加载调用此方法
    onLoad(page) {
      this.getList(this.search);
    },
    handleFullScreen() {
      this.fullscreen = !this.fullscreen;
    },
    // 刷新按钮
    refresh(val) {
      this.handleQuery();
    },
    // 搜索按钮
    searchChange(params, done) {
      // this.$message.success(JSON.stringify(params))
      this.handleQuery();
      setTimeout(() => {
        done();
      }, 1000);
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$refs.select.show();
    },

    /** 查询授权用户列表 */
    getList(query) {
      const params = { roleId: this.roleId, ...this.page, ...query };
      this.loading = true;
      allocatedUserList(params).then(response => {
        if (response.code == 0) {
          this.userList = response.data.list;
          this.page.total = response.data.total;
          this.loading = false;
        }
      });
    },
    // 返回按钮
    handleClose() {
      this.$emit("show-detail");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.page.currentPage = 1;
      this.getList(this.search);
    },
    // 删除
    handleDelete(row) {
      var query = {
        roleId: this.roleId,
        userList: []
      };
      if (row.userName) {
        query.userList = [{ userName: row.userName }];
      } else {
        this.userNames.forEach(item => {
          query["userList"].push({ userName: item });
        });
      }
      this.$confirm(`将从 ${this.roleId} 中永久删除, 是否继续?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          // 删除
          this.handleDelRole(query, delRoleUserList);
        })
        .catch(() => {});
    },
    // 删除
    async handleDelRole(query, delFn) {
      let res = await delFn(query);
      if (res.code == 0) {
        this.handleQuery();
        this.$message.success(res.message);
      }
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.$refs.crud.searchReset();
      // this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.userNames = selection.map(item => item.userName);
      this.multiple = !selection.length;
    },
    // 邮箱显隐
    viewEmail(data) {
      let sensitiveStatus = data.email.includes("**") ? "1" : "2";
      usersSensitive(data.userName, sensitiveStatus).then(response => {
        const res = response.data;
        this.userList.forEach(item => {
          if (item.userName == data.userName) {
            item.email = res.email;
          }
        });
      });
    },
    // 手机号显隐
    viewPhoneNumber(data) {
      let sensitiveStatus = data.phoneNumber.includes("****") ? "1" : "2";
      usersSensitive(data.userName, sensitiveStatus).then(response => {
        const res = response.data;
        this.userList.forEach(item => {
          if (item.userName == data.userName) {
            item.phoneNumber = res.phoneNumber;
          }
        });
      });
    },

    userStat(row) {
      switch (row.userStat) {
        case "01":
          return "启用";
        case "02":
          return "停用";
        case "03":
          return "注销";
        case "04":
          return "锁定";
        default:
          break;
      }
    },
    //tag样式
    tagType(row) {
      switch (row.userStat) {
        case "01":
          return "success";
        case "02":
          return "danger";
        case "03":
          return "info";
        case "04":
          return "warning";
        default:
          break;
      }
    }
  }
};
</script>