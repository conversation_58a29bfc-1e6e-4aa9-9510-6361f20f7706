<template>
  <div>
    <div class="app-container" v-if="showContainer">
      <yo-table
        v-loading="tableLoading"
        :data="data"
        :option="option"
        ref="crud"
        :page.sync="page"
        :search.sync="search"
        :permission="getPermission"
        @search-change="searchChange"
        @on-load="onLoad"
        @row-save="rowSave"
        @row-update="rowUpdate"
        v-model="formParent"
        :row-class-name="tableRowClassName"
      >
        <template slot-scope="{scope}" slot="searchMenu">
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            v-hasPermi="['admin:role:page']"
          >查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click.stop="resetQuery">重置</el-button>
        </template>
        <!-- 自定义左侧操作栏 -->
        <template slot-scope="{size}" slot="menuLeft">
          <el-button
            type="primary"
            icon="el-icon-plus"
            :size="size"
            plain
            v-hasPermi="['admin:role:add']"
            @click.stop="handleAdd()"
          >新增</el-button>
        </template>
        <!-- <template slot="roleTypeHeader" slot-scope="{column}">
          {{(column || {}).label}}
          <el-tooltip class="item" effect="dark" placement="top">
            <div slot="content">超管角色可配置用户和操作权限(无限制)<br/>匿名角色需谨慎配置操作权限，所有用户默认拥有该角色</div>
            <i class="el-icon-info"></i>
          </el-tooltip>
        </template> -->
        <template slot="roleType" slot-scope="scope">
          <el-tooltip class="item" effect="dark" content="超管角色可配置用户和操作权限(无限制)" v-if="scope.row.roleType === '1'" placement="top">
            <el-tag size="mini" effect="plain" class="danger-link" :type="roleIdType(scope.row)" style="font-size:10px;color:#F56C6C;border-color:#F56C6C;" :underline="false">内置</el-tag>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" content="匿名角色需谨慎配置操作权限，所有用户默认拥有该角色" v-if="scope.row.roleType === '2'" placement="top">
            <el-tag size="mini" effect="plain" class="danger-link" :type="roleIdType(scope.row)" style="font-size:10px;color:#F56C6C;border-color:#F56C6C;" :underline="false">内置</el-tag>
          </el-tooltip>
          <!-- <el-tooltip class="item" effect="dark" content="Top Center 提示文字" placement="top"> -->
            <el-tag v-if="scope.row.roleType !== '1' && scope.row.roleType !== '2'"  size="mini" effect="plain" class="default-link" style="font-size:10px;color:#13ce66;border-color:#a1ebc2;" :underline="false" >自定义</el-tag>
          <!-- </el-tooltip> -->
          <!-- <el-tag v-if="scope.row.roleType === '1'" size="mini" effect="plain" class="danger-link" :type="roleIdType(scope.row)" style="font-size:10px;color:#F56C6C;border-color:#F56C6C;" :underline="false">内置</el-tag>
          <el-tag v-else-if="scope.row.roleType === '2'" size="mini" effect="plain" class="danger-link" :type="roleIdType(scope.row)" style="font-size:10px;color:#F56C6C;border-color:#F56C6C;" :underline="false">内置</el-tag>
          <el-tag v-else size="mini" effect="plain" class="default-link" style="font-size:10px;color:#13ce66;border-color:#a1ebc2;" :underline="false" >自定义</el-tag> -->
        </template>
        <!-- 自定义列 -->
        <template slot="status" slot-scope="scope">
          <el-tag size="mini" effect="plain" :type="tagType(scope.row)">{{status(scope.row)}}</el-tag>
        </template>
        <!-- 自定义操作按钮 -->
        <template slot-scope="{row,size,type,index}" slot="menu">
          <span v-if="row.roleType === '2'">
          <el-button
            :size="size"
            :type="type"
            icon="el-icon-circle-check"
            @click.stop="handleMoreProcess(row, 'dataScope')"
            v-hasPermi="['admin:role:menu:tree']"
          >操作权限</el-button>
          </span>
          <span v-else-if="row.roleType === '1'">
            <el-button            
            :size="size"
            :type="type"
            icon="el-icon-user"
            @click.stop="handleMoreProcess(row, 'authUser')"
            v-hasPermi="['admin:role:page:users']"
          >配置用户</el-button>
          <el-button
            :size="size"
            :type="type"
            icon="el-icon-circle-check"
            @click.stop="handleMoreProcess(row, 'dataScope')"
            v-hasPermi="['admin:role:menu:tree']"
          >操作权限</el-button>
          </span>
          <span v-else>
          <el-button
            :size="size"
            :type="type"
            icon="el-icon-edit"
            @click.stop="handleUpdate(row,index)"
            v-hasPermi="['admin:role:edit']"
          >修改</el-button>
          <el-button
            :size="size"
            :type="type"
            icon="el-icon-delete"
            @click="handleDelete(row)"
            v-hasPermi="['admin:role:remove']"
          >删除</el-button>
          <el-dropdown
            :size="size"
            @command="(command) => handleCommand(command, row)"
            v-hasPermi="['admin:role:page:users','admin:role:res:tree']"
          >
            <span class="el-dropdown-link">
              <i class="el-icon-d-arrow-right el-icon--right"></i>更多
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                command="handleAuthUser"
                icon="el-icon-user"
                v-hasPermi="['admin:role:page:users']"
              >配置用户</el-dropdown-item>
              <el-dropdown-item
                command="handleDataScope"
                icon="el-icon-circle-check"
                v-hasPermi="['admin:role:menu:tree']"
              >操作权限</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          </span>

        </template>
      </yo-table>
    </div>
    <div v-else>
      <!-- <auth-user @show-detail="showDetails" :roleId="roleId"></auth-user> -->
      <auth-user v-if="isActive=='authUser'" @show-detail="showDetails" :roleId="roleId" :roleobj="roleobj"></auth-user>
      <auth-menu v-if="isActive=='dataScope'" @show-detail="showDetails" :roleId="roleId"></auth-menu>
    </div>
  </div>
</template>
<script>
import {
  listRole,
  delRole,
  addRole,
  updateRole,
  roleTreeList,
  editRoleList
} from "@/api/system/role";
import {
  treeselect as menuTreeselect,
  roleMenuTreeselect
} from "@/api/system/menu";
import {
  treeselect as deptTreeselect,
  roleDeptTreeselect
} from "@/api/system/dept";
import roleListOption from "./infoData/roleListOption.js";
import menuOption from "./infoData/menuOption.js";
import authUser from "./authUser.vue";
import AuthMenu from "./authMenu.vue";
import toTreeArray from "yo-utils/toTreeArray";
export default {
  name: "Role",
  components: {
    authUser,
    AuthMenu
  },
  dicts: ["yoaf_role_state"],
  data() {
    return {
      roleId: "",
      showContainer: true, //显示主界面
      isActive: "authUser", // 配置用户 or 操作菜单权限
      activeRoleName: '', // 
      roleobj: {},
      formParent: {
        status: "1"
      },
      fullscreen: false,
      search: {},
      page: {
        pageSize: 10,
        currentPage: 1
      },
      data: [],
      option: roleListOption,

      // 遮罩层
      loading: true,
      tableLoading: true,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      menuData: [],
      menuOption: menuOption,
      // 修改选中项
      resList: [],
      multiple: true,
      num: 0,
      // 表单参数
      form: {},
      checkPageAll: false, // 操作权限弹窗中全选
      isIndeterminate: false // 全选的不确定状态 只要有一个子集不为true 即设置true
    };
  },
  computed: {
    styleName() {
      if (!this.fullscreen) {
        return { top: "15%", bottom: "5%" };
      } else {
        return { top: 0, bottom: 0, marginTop: 0 };
      }
    }
  },
  created() {
    /** *添加字典项数据*/
    this.updateDictData(
      this.option.column,
      "status",
      this.dict.type["yoaf_role_state"]
    );
  },
  methods: {
    showDetails() {
      this.showContainer = true;
    },
    // 更新字典数据
    updateDictData(option, key, data) {
      let column = this.findObject(option, key);
      column.dicData = data;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$refs.crud.rowAdd();
    },
    // 首次加载调用此方法
    onLoad(page) {
      this.getList(this.search);
    },
    handleFullScreen() {
      this.fullscreen = !this.fullscreen;
    },
    // 超级管理员不能编辑和删除
    getPermission(key, row, index) {
      const flag =
        (key === "editBtn" || key === "delBtn") && row.roleKey === "admin";
      if (flag) {
        return false;
      } else {
        return true;
      }
    },
    // 新增表单保存
    async rowSave(form, done, loading) {
      let response = await addRole(form);
      if (response.code == 0) {
        this.$message.success(response.message);
        this.getList();
        done();
      }
    },
    // 修改表单保存
    async rowUpdate(form, index, done, loading) {
      let response = await updateRole(form);
      if (response.code == 0) {
        this.$message.success(response.message);
        this.getList();
        done();
      }
    },
    // 搜索按钮
    searchChange(params, done) {
      this.handleQuery();
      setTimeout(() => {
        done();
      }, 1000);
    },

    /** 查询角色列表 */
    getList(query) {
      this.tableLoading = true;
      const params = { ...this.page, ...query };
      listRole(params).then(response => {
        if (response.code == 0) {
          this.data = response.data.list;
          this.page.total = response.data.total;
          this.tableLoading = false;
        }
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.page.currentPage = 1;
      this.getList(this.search);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.$refs.crud.searchReset();
      // this.handleQuery();
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case "handleDataScope":
          this.handleMoreProcess(row, "dataScope");
          break;
        case "handleAuthUser":
          this.handleMoreProcess(row, "authUser");
          break;
        default:
          break;
      }
    },
    /** 修改按钮操作 */
    handleUpdate(row, index) {
      this.formParent = row;
      this.$refs.crud.rowEdit(row, index);
    },
    /** 分配数据权限操作 or  分配用户操作*/
    handleMoreProcess(row, type) {
      this.roleId = row.roleId;
      this.roleobj = row
      this.showContainer = false;
      this.isActive = type;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const roleIds = row.roleId;
      this.$confirm(`是否确认删除角色编号为" ${roleIds} "的数据项?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
      // this.$modal
      //   .confirm('是否确认删除角色编号为"' + roleIds + '"的数据项？')
        .then(async () => {
          // 删除
          this.handleDelRole(roleIds, delRole);
        })
        .catch(() => {});
    },
    // 删除角色
    async handleDelRole(roleIds, delFn) {
      let res = await delFn(roleIds);
      if (res.code == 0) {
        this.$message.success(res.message);
        this.getList();
      }
    },

    status(row) {
      switch (row.status) {
        case "1":
          return "启用";
        case "2":
          return "停用";
        case "3":
          return "注销";
        case "4":
          return "锁定";
        default:
          break;
      }
    },
    //tag样式
    tagType(row) {
      switch (row.status) {
        case "1":
          return "success";
        case "2":
          return "danger";
        case "3":
          return "info";
        case "4":
          return "warning";
        default:
          break;
      }
    },
    roleIdType(row) {
      switch (row.roleId) {
        case "anonymous":
          return 'danger'
          // return "success";
        case "superAdmin":
          return "danger";
        case "3":
          return "info";
        case "4":
          return "warning";
        default:
          break;
      }
    },
    tableRowClassName({ row, rowIndex }) {
      if(row.roleType === '1') {
        return 'superAdmin-row'
      } else if(row.roleType === '2') {
        return 'anonymous-row'
      } else {
        return ''
      }
    },
  }
};
</script>
<style scoped>
.yo-table__dialog__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.yo-table__dialog__menu {
  padding-right: 20px;
}
.item-badge {
  margin-top: 10px;
  margin-right: 10px;
}
/* 自定义行颜色 */

</style>
<style>
/* 自定义行颜色 */
.superAdmin-row {
  background-color: #EBEEF5 !important;
  /* color:#fff; */
}
.anonymous-row {
  background-color: #EBEEF5 !important;
  /* color:#fff; */
}
.superAdmin-row.hover-row td,.anonymous-row.hover-row td{
  background-color: initial !important;
}
</style>