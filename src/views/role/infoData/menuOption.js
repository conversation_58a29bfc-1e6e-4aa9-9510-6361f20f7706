export default {
  headerAlign: 'center',
  align: 'center',
  border: true,
  // index: true,
  selection: false,
  menu: false,
  searchBtn: false,
  emptyBtn: false,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  columnBtn: false,
  refreshBtn: false,
  searchShowBtn: false,
  checkStrictly: true,
  page: false,
  defaultExpandAll: true,
  tip: false,
  columnBtn:false,
  column: [

    {
      label: '名称',
      prop: 'name',
      align: 'left',
      fixed: true,
      // width: 200
    }, {
      label: '资源类型',
      prop: 'type',
      dicData: [{
        label: '菜单',
        value: "menu"
      }, {
        label: '权限',
        value: "function"
      }],
    }, {
      label: '按钮权限标识',
      prop: 'funcBtn',
    }, {
      label: '接口权限标识',
      prop: 'funcUri'
    },
    {
      label: '序号',
      prop: 'selection',
      // fixed: true,
      labelslot: true,
      width: 80
    },],
}